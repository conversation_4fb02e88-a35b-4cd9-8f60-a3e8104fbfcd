<?php declare(strict_types = 1);


use Apitte\Core\Application\IApplication;
use App\Bootstrap;
use Nette\Application\Application;

require __DIR__ . '/../vendor/autoload.php';

ini_set('session.gc_maxlifetime', '1206000');

$isApi = substr($_SERVER['REQUEST_URI'], 0, 5) === '/api/';
$container = Bootstrap::boot()->createContainer();

if ($isApi) {
	$container->getByType(IApplication::class)->run();
} else {
	$container->getByType(Application::class)->run();
}
