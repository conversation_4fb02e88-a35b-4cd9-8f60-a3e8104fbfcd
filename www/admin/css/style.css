@-ms-viewport {
  width: device-width;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  zoom: 1;
}
audio:not([controls]) {
  display: none;
}
[hidden] {
  display: none;
}
html {
  font-size: 100%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
html,
button,
input,
select,
textarea {
  font-family: sans-serif;
}
body {
  margin: 0;
}
a:focus {
  outline: none;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
h2 {
  font-size: 1.5em;
  margin: 0.83em 0;
}
h3 {
  font-size: 1.17em;
  margin: 1em 0;
}
h4 {
  font-size: 1em;
  margin: 1.33em 0;
}
h5 {
  font-size: 0.83em;
  margin: 1.67em 0;
}
h6 {
  font-size: 0.75em;
  margin: 2.33em 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
blockquote {
  margin: 1px 40px;
}
dfn {
  font-style: italic;
}
mark {
  background: #ff0;
  color: #000;
}
p,
pre {
  margin: 1em 0;
}
pre,
code,
kbd,
samp {
  font-family: monospace, serif;
  _font-family: 'courier new', monospace;
  font-size: 1em;
}
pre {
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
}
q {
  quotes: none;
}
q:before,
q:after {
  content: '';
  content: none;
}
small {
  font-size: 75%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
dl,
menu,
ol,
ul {
  margin: 1em 0;
}
dd {
  margin: 0 0 0 40px;
}
menu,
ol,
ul {
  padding: 0 0 0 40px;
}
nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}
img {
  border: 0;
  -ms-interpolation-mode: bicubic;
}
svg:not(:root) {
  overflow: hidden;
}
img,
iframe,
object,
embed {
  vertical-align: middle;
}
figure {
  margin: 0;
}
form {
  margin: 0;
}
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}
button,
input,
select,
textarea {
  font-size: 100%;
  margin: 0;
  vertical-align: baseline;
  *vertical-align: middle;
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  outline: none;
  border-radius: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
button,
input {
  line-height: normal;
}
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
  *overflow: visible;
}
button[disabled],
input[disabled] {
  cursor: default;
}
input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0;
  *height: 13px;
  *width: 13px;
}
input[type="search"] {
  -webkit-appearance: textfield;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
  vertical-align: top;
  resize: vertical;
}
label {
  cursor: default;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
caption {
  text-align: left;
  caption-side: top;
}
th {
  text-align: left;
}
.tracy-row li::before {
  display: none;
}
@font-face {
  font-family: 'icomoon';
  font-style: normal;
  font-weight: normal;
  src: url("../font/icomoon.eot");
  src: url("../font/icomoon.eot?#iefix") format('embedded-opentype'), url("../font/icomoon.woff") format('woff'), url("../font/icomoon.ttf") format('truetype');
}
/*
 *	Simply fast class
 */
.reset {
  border: none;
  margin: 0;
  padding: 0;
  background: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.reset td,
.reset th {
  border: none;
  background: none;
  padding: 0;
}
.reset > li,
.reset > dt,
.reset > dd {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  float: none;
  width: auto;
}
.reset > li:before {
  display: none;
}
.l {
  float: left;
  display: inline;
}
.r {
  float: right;
  display: inline;
}
.cb {
  clear: both;
}
.cl {
  clear: left;
}
.cr {
  clear: right;
}
.cfx {
  zoom: 1;
}
.cfx:after,
.cfx:before {
  content: '';
  display: table;
  clear: both;
}
.break {
  height: 1px;
  font-size: 1px;
  line-height: 1px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
  display: block;
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
}
.ctx {
  zoom: 1;
  display: table-cell;
}
.ie7 .ctx {
  display: block;
}
.ctx:after {
  clear: both;
  display: block;
  visibility: hidden;
  overflow: hidden;
  height: 0px !important;
  line-height: 0px;
  font-size: xx-large;
  content: " x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x x ";
}
.hide,
.js .jsHide {
  display: none;
}
.out,
.js .jsOut {
  position: absolute;
  left: -5000px;
  top: -5000px;
}
.vhide {
  position: absolute;
  height: 1px;
  width: 1px;
  overflow: hidden;
  border: 0;
  clip: rect(0 0 0 0);
  margin: -1px;
  padding: 0;
}
.left {
  text-align: left;
}
.right {
  text-align: right;
}
.center {
  text-align: center;
}
.lower {
  text-transform: lowercase;
}
.upper {
  text-transform: uppercase;
}
.bold {
  font-weight: bold;
}
.thin {
  font-weight: normal;
}
.italic {
  font-style: italic;
}
.normal {
  font-style: normal;
}
.top {
  vertical-align: top;
}
.middle {
  vertical-align: middle;
}
.big {
  font-size: 14px;
}
.bigger {
  font-size: 18px;
}
.small {
  font-size: 11px;
}
.error,
.red {
  color: #c00;
}
.green {
  color: #3c3;
}
.yellow {
  color: #fc3;
}
.grey {
  color: #999;
}
.nowrap {
  white-space: nowrap;
}
/*
 * Typo
 */
body {
  font: normal 14px/24px 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  color: #000;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font: 1em/1.2 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  color: #333;
  margin: 1.3em 0 0.5em;
}
h1,
.h1 {
  font-size: 2.571428571428572em;
  margin-top: 0;
}
h2 {
  font-size: 1.714285714285714em;
  margin-bottom: 0.7em;
}
h3 {
  font-size: 1.285714285714286em;
}
h4,
h5,
h6 {
  font-size: 1em;
}
/*
 *	Margins
 */
.margin-h1 {
  padding: 2.571428571428572em;
  margin: 3.085714285714286em 0 1.285714285714286em;
}
.margin-h2 {
  margin: 2.057142857142857em 0 0.857142857142857em;
}
.margin-h3 {
  margin: 1.542857142857143em 0 0.642857142857143em;
}
.margin-h4,
.margin-h5,
.margin-h6 {
  margin: 1.2em 0 0.5em;
}
.margin-p {
  margin: 0 0 1em;
}
.margin-ul,
.margin-ol {
  margin: 0 0 1em;
}
p {
  margin: 0 0 1em;
}
blockquote {
  margin: 0.8em 0 0.3em;
}
blockquote p {
  margin: 0;
}
a {
  color: #4790d2;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  tap-highlight-color: rgba(0,0,0,0);
  text-decoration: underline;
}
a:hover {
  color: #2c74b5;
  text-decoration: none;
}
ul,
ol {
  margin: 0 0 1em;
  list-style: none;
  padding: 0;
}
li {
  padding: 0 0 0 20px;
  margin: 0 0 0.5em;
}
li ol,
li ul {
  margin: 0.75em 0 0;
}
ul li:before {
  content: '';
  float: left;
  margin: 8px 0 0 -20px;
  width: 8px;
  height: 8px;
  border-radius: 5px;
  background: #4790d2;
}
ol {
  counter-reset: item;
}
ol li {
  background: none;
}
ol li:before,
ol li .ie-counter {
  content: counter(item) ".";
  counter-increment: item;
  float: left;
  margin-left: -20px;
  width: 18px;
  color: #4790d2;
  font-weight: bold;
}
dl {
  margin: 0 0 1.5em;
}
dt {
  font-weight: bold;
  text-transform: uppercase;
  margin: 0 0 0.4em;
}
dd {
  margin: 0 0 1em;
  padding: 0;
}
table {
  width: 100%;
  margin: 0 0 1.5em;
  empty-cells: show;
  border-collapse: separate;
  border-spacing: 0;
  border: 0px;
}
p + table {
  margin-top: 1.2em;
}
caption {
  font-weight: bold;
  text-align: left;
  padding: 0px 0px 10px;
  caption-side: top;
}
td,
th {
  vertical-align: top;
  padding: 7px 20px 6px 20px;
  border: 1px solid #ddd;
  border-width: 0 0px 1px;
}
td + *,
th + * {
  padding-left: 0;
}
th {
  font-weight: normal;
  background: #4790d2;
  color: #fff;
  border-bottom: 0px;
}
tfoot td,
tfoot th {
  background: #ddd;
}
tr.clickable {
  cursor: pointer;
}
tr.clickable:hover {
  background: #eee;
}
th.status,
td.status {
  width: 5px;
  padding: 0;
}
th.status + td,
td.status + td,
th.status + th,
td.status + th {
  padding-left: 20px;
}
td.status {
  background: #666;
}
td.status.green {
  background: #3c3;
}
td.status.red {
  background: #c00;
}
td.status.yellow {
  background: #fc3;
}
/*
 *	Grid
 */
.row,
.row-main {
  position: relative;
  margin: 0;
  padding: 0;
  zoom: 1;
}
.row:after,
.row-main:after,
.row:before,
.row-main:before {
  content: '';
  display: table;
  clear: both;
}
.row-main {
  margin: 0 auto;
  width: 960px;
}
.col-side {
  position: relative;
  float: left;
  background: #191919;
  margin-left: -200px;
  padding: 30px 20px;
  width: 160px;
  overflow: hidden;
  -webkit-transition: margin-left  0.3s  ease,   width  0.3s  ease;
  -o-transition: margin-left  0.3s  ease,   width  0.3s  ease;
  -webkit-transition: margin-left 0.3s ease, width 0.3s ease;
  -o-transition: margin-left 0.3s ease, width 0.3s ease;
  transition: margin-left 0.3s ease, width 0.3s ease;
}
.menu-hover .col-side {
  width: 160px;
  margin-left: -200px;
}
.col-main {
  padding: 30px;
  overflow: hidden;
}
.col-main::after {
  content: '';
  display: block;
  height: 70vh;
}
.col-tree {
  float: left;
  width: 280px;
  margin-left: -10px;
  padding-right: 20px;
}
.col-tree:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 520px;
  width: 1px;
  background: #ddd;
  -webkit-transition: left 0.3s ease;
  -o-transition: left 0.3s ease;
  transition: left 0.3s ease;
}
.menu-hover .col-tree:before {
  left: 520px;
}
.skbox-window .col-tree {
  margin-left: 0;
}
.skbox-window .col-tree:before {
  display: none;
}
.col-content {
  margin-left: 320px;
}
.grid-row {
  font-family: 'Courier New', monospace;
  letter-spacing: -0.63em;
  word-spacing: -0.63em;
  margin-left: -20px;
  zoom: 1;
}
.grid-row > * {
  display: inline-block;
  vertical-align: top;
  width: 100%;
  font-family: 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  letter-spacing: 0px;
  word-spacing: 0px;
}
.ie7 .grid-row > * {
  display: inline;
  zoom: 1;
}
.grid-row > * {
  padding-left: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.grid-row > p {
  margin-bottom: 14px;
}
.grid-row > p.reset {
  margin-bottom: 0;
}
.grid-1 {
  width: 100%;
}
.grid-1-2 {
  width: 50%;
}
.grid-1-3 {
  width: 33.333%;
}
.grid-2-3 {
  width: 66.666%;
}
.grid-1-4 {
  width: 25%;
}
.grid-2-4 {
  width: 50%;
}
.grid-3-4 {
  width: 75%;
}
.grid-1-5 {
  width: 20%;
}
.grid-2-5 {
  width: 40%;
}
.grid-3-5 {
  width: 60%;
}
.grid-4-5 {
  width: 80%;
}
.icon,
[data-icon]:before,
.inp-item input[type="checkbox"]:checked + label:after,
.inp-item input[type="checkbox"]:checked + span:after,
.ui-datepicker-prev,
.ui-datepicker-next,
.ui_tpicker_minute .ui-spinner-up,
.ui_tpicker_hour .ui-spinner-up,
.ui_tpicker_second .ui-spinner-up,
.ui_tpicker_minute .ui-spinner-down,
.ui_tpicker_hour .ui-spinner-down,
.ui_tpicker_second .ui-spinner-down,
.crossroad-images li.selected .thumb:after,
.crossroad-images li.selected:hover .thumb:after,
.jstree a > .jstree-icon {
  font-family: 'icomoon';
  content: attr(data-icon);
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
}
a.icon {
  text-decoration: none;
}
table .icon {
  vertical-align: middle;
  display: inline-block;
}
.icon-home:before {
  content: "\e000";
}
.icon-home-2:before {
  content: "\e001";
}
.icon-home-3:before {
  content: "\e002";
}
.icon-office:before {
  content: "\e003";
}
.icon-newspaper:before {
  content: "\e004";
}
.icon-pencil:before {
  content: "\e005";
}
.icon-pencil-2:before {
  content: "\e006";
}
.icon-quill:before {
  content: "\e007";
}
.icon-pen:before {
  content: "\e008";
}
.icon-blog:before {
  content: "\e009";
}
.icon-droplet:before {
  content: "\e00a";
}
.icon-paint-format:before {
  content: "\e00b";
}
.icon-image:before {
  content: "\e00c";
}
.icon-image-2:before {
  content: "\e00d";
}
.icon-images:before {
  content: "\e00e";
}
.icon-camera:before {
  content: "\e00f";
}
.icon-music:before {
  content: "\e010";
}
.icon-headphones:before {
  content: "\e011";
}
.icon-play:before {
  content: "\e012";
}
.icon-film:before {
  content: "\e013";
}
.icon-camera-2:before {
  content: "\e014";
}
.icon-dice:before {
  content: "\e015";
}
.icon-pacman:before {
  content: "\e016";
}
.icon-spades:before {
  content: "\e017";
}
.icon-clubs:before {
  content: "\e018";
}
.icon-diamonds:before {
  content: "\e019";
}
.icon-pawn:before {
  content: "\e01a";
}
.icon-bullhorn:before {
  content: "\e01b";
}
.icon-connection:before {
  content: "\e01c";
}
.icon-podcast:before {
  content: "\e01d";
}
.icon-feed:before {
  content: "\e01e";
}
.icon-book:before {
  content: "\e01f";
}
.icon-books:before {
  content: "\e020";
}
.icon-library:before {
  content: "\e021";
}
.icon-file:before,
.jstree .jstree-leaf > a > ins:before {
  content: "\e022";
}
.icon-profile:before {
  content: "\e023";
}
.icon-file-2:before {
  content: "\e024";
}
.icon-file-3:before {
  content: "\e025";
}
.icon-file-4:before {
  content: "\e026";
}
.icon-copy:before {
  content: "\e027";
}
.icon-copy-2:before {
  content: "\e028";
}
.icon-copy-3:before {
  content: "\e029";
}
.icon-paste:before {
  content: "\e02a";
}
.icon-paste-2:before {
  content: "\e02b";
}
.icon-paste-3:before {
  content: "\e02c";
}
.icon-stack:before {
  content: "\e02d";
}
.icon-folder:before,
.jstree a > .jstree-icon:before,
.jstree.one-level > ul > .jstree-leaf > a > ins:before {
  content: "\e02e";
}
.icon-folder-open:before,
.jstree .jstree-open > a > ins:before {
  content: "\e02f";
}
.icon-tag:before {
  content: "\e030";
}
.icon-tags:before {
  content: "\e031";
}
.icon-barcode:before {
  content: "\e032";
}
.icon-qrcode:before {
  content: "\e033";
}
.icon-ticket:before {
  content: "\e034";
}
.icon-cart:before {
  content: "\e035";
}
.icon-cart-2:before {
  content: "\e036";
}
.icon-cart-3:before {
  content: "\e037";
}
.icon-coin:before {
  content: "\e038";
}
.icon-credit:before {
  content: "\e039";
}
.icon-calculate:before {
  content: "\e03a";
}
.icon-support:before {
  content: "\e03b";
}
.icon-phone:before {
  content: "\e03c";
}
.icon-phone-hang-up:before {
  content: "\e03d";
}
.icon-address-book:before {
  content: "\e03e";
}
.icon-notebook:before {
  content: "\e03f";
}
.icon-envelop:before {
  content: "\e040";
}
.icon-pushpin:before {
  content: "\e041";
}
.icon-location:before {
  content: "\e042";
}
.icon-location-2:before {
  content: "\e043";
}
.icon-compass:before {
  content: "\e044";
}
.icon-map:before {
  content: "\e045";
}
.icon-map-2:before {
  content: "\e046";
}
.icon-history:before {
  content: "\e047";
}
.icon-clock:before {
  content: "\e048";
}
.icon-clock-2:before {
  content: "\e049";
}
.icon-alarm:before {
  content: "\e04a";
}
.icon-alarm-2:before {
  content: "\e04b";
}
.icon-bell:before {
  content: "\e04c";
}
.icon-stopwatch:before {
  content: "\e04d";
}
.icon-calendar:before {
  content: "\e04e";
}
.icon-calendar-2:before {
  content: "\e04f";
}
.icon-print:before {
  content: "\e050";
}
.icon-keyboard:before {
  content: "\e051";
}
.icon-screen:before {
  content: "\e052";
}
.icon-laptop:before {
  content: "\e053";
}
.icon-mobile:before {
  content: "\e054";
}
.icon-mobile-2:before {
  content: "\e055";
}
.icon-tablet:before {
  content: "\e056";
}
.icon-tv:before {
  content: "\e057";
}
.icon-cabinet:before {
  content: "\e058";
}
.icon-drawer:before {
  content: "\e059";
}
.icon-drawer-2:before {
  content: "\e05a";
}
.icon-drawer-3:before {
  content: "\e05b";
}
.icon-box-add:before {
  content: "\e05c";
}
.icon-box-remove:before {
  content: "\e05d";
}
.icon-download:before {
  content: "\e05e";
}
.icon-upload:before {
  content: "\e05f";
}
.icon-disk:before {
  content: "\e060";
}
.icon-storage:before {
  content: "\e061";
}
.icon-undo:before {
  content: "\e062";
}
.icon-redo:before {
  content: "\e063";
}
.icon-flip:before {
  content: "\e064";
}
.icon-flip-2:before {
  content: "\e065";
}
.icon-undo-2:before {
  content: "\e066";
}
.icon-redo-2:before {
  content: "\e067";
}
.icon-forward:before {
  content: "\e068";
}
.icon-reply:before {
  content: "\e069";
}
.icon-bubble:before {
  content: "\e06a";
}
.icon-bubbles:before {
  content: "\e06b";
}
.icon-bubbles-2:before {
  content: "\e06c";
}
.icon-bubble-2:before {
  content: "\e06d";
}
.icon-bubbles-3:before {
  content: "\e06e";
}
.icon-bubbles-4:before {
  content: "\e06f";
}
.icon-user:before {
  content: "\e070";
}
.icon-users:before {
  content: "\e071";
}
.icon-user-2:before {
  content: "\e072";
}
.icon-users-2:before {
  content: "\e073";
}
.icon-user-3:before {
  content: "\e074";
}
.icon-user-4:before {
  content: "\e075";
}
.icon-quotes-left:before {
  content: "\e076";
}
.icon-busy:before {
  content: "\e077";
}
.icon-spinner:before {
  content: "\e078";
}
.icon-spinner-2:before {
  content: "\e079";
}
.icon-spinner-3:before {
  content: "\e07a";
}
.icon-spinner-4:before {
  content: "\e07b";
}
.icon-spinner-5:before {
  content: "\e07c";
}
.icon-spinner-6:before {
  content: "\e07d";
}
.icon-binoculars:before {
  content: "\e07e";
}
.icon-search:before {
  content: "\e07f";
}
.icon-zoom-in:before {
  content: "\e080";
}
.icon-zoom-out:before {
  content: "\e081";
}
.icon-expand:before {
  content: "\e082";
}
.icon-contract:before {
  content: "\e083";
}
.icon-expand-2:before {
  content: "\e084";
}
.icon-contract-2:before {
  content: "\e085";
}
.icon-key:before {
  content: "\e086";
}
.icon-key-2:before {
  content: "\e087";
}
.icon-lock:before {
  content: "\e088";
}
.icon-lock-2:before {
  content: "\e089";
}
.icon-unlocked:before {
  content: "\e08a";
}
.icon-wrench:before {
  content: "\e08b";
}
.icon-settings:before {
  content: "\e08c";
}
.icon-equalizer:before {
  content: "\e08d";
}
.icon-cog:before {
  content: "\e08e";
}
.icon-cogs:before {
  content: "\e08f";
}
.icon-cog-2:before {
  content: "\e090";
}
.icon-hammer:before {
  content: "\e091";
}
.icon-wand:before {
  content: "\e092";
}
.icon-aid:before {
  content: "\e093";
}
.icon-bug:before {
  content: "\e094";
}
.icon-pie:before {
  content: "\e095";
}
.icon-stats:before {
  content: "\e096";
}
.icon-bars:before {
  content: "\e097";
}
.icon-bars-2:before {
  content: "\e098";
}
.icon-gift:before {
  content: "\e099";
}
.icon-trophy:before {
  content: "\e09a";
}
.icon-glass:before {
  content: "\e09b";
}
.icon-mug:before {
  content: "\e09c";
}
.icon-food:before {
  content: "\e09d";
}
.icon-leaf:before {
  content: "\e09e";
}
.icon-rocket:before {
  content: "\e09f";
}
.icon-meter:before {
  content: "\e0a0";
}
.icon-meter2:before {
  content: "\e0a1";
}
.icon-dashboard:before {
  content: "\e0a2";
}
.icon-hammer-2:before {
  content: "\e0a3";
}
.icon-fire:before {
  content: "\e0a4";
}
.icon-lab:before {
  content: "\e0a5";
}
.icon-magnet:before {
  content: "\e0a6";
}
.icon-remove:before {
  content: "\e0a7";
}
.icon-remove-2:before {
  content: "\e0a8";
}
.icon-briefcase:before {
  content: "\e0a9";
}
.icon-airplane:before {
  content: "\e0aa";
}
.icon-truck:before {
  content: "\e0ab";
}
.icon-road:before {
  content: "\e0ac";
}
.icon-accessibility:before {
  content: "\e0ad";
}
.icon-target:before {
  content: "\e0ae";
}
.icon-shield:before {
  content: "\e0af";
}
.icon-lightning:before {
  content: "\e0b0";
}
.icon-switch:before {
  content: "\e0b1";
}
.icon-power-cord:before {
  content: "\e0b2";
}
.icon-signup:before {
  content: "\e0b3";
}
.icon-list:before {
  content: "\e0b4";
}
.icon-list-2:before {
  content: "\e0b5";
}
.icon-numbered-list:before {
  content: "\e0b6";
}
.icon-menu:before {
  content: "\e0b7";
}
.icon-menu-2:before {
  content: "\e0b8";
}
.icon-tree:before {
  content: "\e0b9";
}
.icon-cloud:before {
  content: "\e0ba";
}
.icon-cloud-download:before {
  content: "\e0bb";
}
.icon-cloud-upload:before {
  content: "\e0bc";
}
.icon-download-2:before {
  content: "\e0bd";
}
.icon-upload-2:before {
  content: "\e0be";
}
.icon-download-3:before {
  content: "\e0bf";
}
.icon-upload-3:before {
  content: "\e0c0";
}
.icon-globe:before {
  content: "\e0c1";
}
.icon-earth:before {
  content: "\e0c2";
}
.icon-link:before {
  content: "\e0c3";
}
.icon-flag:before {
  content: "\e0c4";
}
.icon-attachment:before {
  content: "\e0c5";
}
.icon-eye:before {
  content: "\e0c6";
}
.icon-eye-blocked:before {
  content: "\e0c7";
}
.icon-eye-2:before {
  content: "\e0c8";
}
.icon-bookmark:before {
  content: "\e0c9";
}
.icon-bookmarks:before {
  content: "\e0ca";
}
.icon-brightness-medium:before {
  content: "\e0cb";
}
.icon-brightness-contrast:before {
  content: "\e0cc";
}
.icon-contrast:before {
  content: "\e0cd";
}
.icon-star:before {
  content: "\e0ce";
}
.icon-star-2:before {
  content: "\e0cf";
}
.icon-star-3:before {
  content: "\e0d0";
}
.icon-heart:before {
  content: "\e0d1";
}
.icon-foursquare:before {
  content: "\e0d3";
}
.icon-paypal:before {
  content: "\e0d4";
}
.icon-paypal-2:before {
  content: "\e0d5";
}
.icon-paypal-3:before {
  content: "\e0d6";
}
.icon-yelp:before {
  content: "\e0d7";
}
.icon-libreoffice:before {
  content: "\e0d8";
}
.icon-file-pdf:before {
  content: "\e0d9";
}
.icon-file-openoffice:before {
  content: "\e0da";
}
.icon-file-word:before {
  content: "\e0db";
}
.icon-file-excel:before {
  content: "\e0dc";
}
.icon-file-zip:before {
  content: "\e0dd";
}
.icon-file-powerpoint:before {
  content: "\e0de";
}
.icon-safari:before {
  content: "\e0e0";
}
.icon-file-xml:before {
  content: "\e0df";
}
.icon-IcoMoon:before {
  content: "\e0d2";
}
.icon-stumbleupon:before {
  content: "\e0e1";
}
.icon-stackoverflow:before {
  content: "\e0e2";
}
.icon-pinterest:before {
  content: "\e0e3";
}
.icon-pinterest-2:before {
  content: "\e0e4";
}
.icon-xing:before {
  content: "\e0e5";
}
.icon-xing-2:before {
  content: "\e0e6";
}
.icon-flattr:before {
  content: "\e0e7";
}
.icon-foursquare-2:before {
  content: "\e0e8";
}
.icon-file-css:before {
  content: "\e0e9";
}
.icon-html5:before {
  content: "\e0ea";
}
.icon-html5-2:before {
  content: "\e0eb";
}
.icon-css3:before {
  content: "\e0ec";
}
.icon-chrome:before {
  content: "\e0ed";
}
.icon-firefox:before {
  content: "\e0ee";
}
.icon-IE:before {
  content: "\e0ef";
}
.icon-opera:before {
  content: "\e0f0";
}
.icon-radio-checked:before {
  content: "\e0f1";
}
.icon-radio-unchecked:before {
  content: "\e0f2";
}
.icon-crop:before {
  content: "\e0f3";
}
.icon-scissors:before {
  content: "\e0f4";
}
.icon-filter:before {
  content: "\e0f5";
}
.icon-filter-2:before {
  content: "\e0f6";
}
.icon-font:before {
  content: "\e0f7";
}
.icon-text-height:before {
  content: "\e0f8";
}
.icon-text-width:before {
  content: "\e0f9";
}
.icon-bold:before {
  content: "\e0fa";
}
.icon-underline:before {
  content: "\e0fb";
}
.icon-italic:before {
  content: "\e0fc";
}
.icon-strikethrough:before {
  content: "\e0fd";
}
.icon-omega:before {
  content: "\e0fe";
}
.icon-sigma:before {
  content: "\e0ff";
}
.icon-table:before {
  content: "\e100";
}
.icon-table-2:before {
  content: "\e101";
}
.icon-insert-template:before {
  content: "\e102";
}
.icon-pilcrow:before {
  content: "\e103";
}
.icon-left-to-right:before {
  content: "\e104";
}
.icon-right-to-left:before {
  content: "\e105";
}
.icon-paragraph-left:before {
  content: "\e106";
}
.icon-paragraph-center:before {
  content: "\e107";
}
.icon-paragraph-right:before {
  content: "\e108";
}
.icon-paragraph-justify:before {
  content: "\e109";
}
.icon-paragraph-left-2:before {
  content: "\e10a";
}
.icon-paragraph-center-2:before {
  content: "\e10b";
}
.icon-paragraph-right-2:before {
  content: "\e10c";
}
.icon-paragraph-justify-2:before {
  content: "\e10d";
}
.icon-indent-increase:before {
  content: "\e10e";
}
.icon-indent-decrease:before {
  content: "\e10f";
}
.icon-new-tab:before {
  content: "\e110";
}
.icon-embed:before {
  content: "\e111";
}
.icon-code:before {
  content: "\e112";
}
.icon-console:before {
  content: "\e113";
}
.icon-share:before {
  content: "\e114";
}
.icon-mail:before {
  content: "\e115";
}
.icon-mail-2:before {
  content: "\e116";
}
.icon-mail-3:before {
  content: "\e117";
}
.icon-mail-4:before {
  content: "\e118";
}
.icon-google:before {
  content: "\e119";
}
.icon-google-plus:before {
  content: "\e11a";
}
.icon-google-plus-2:before {
  content: "\e11b";
}
.icon-google-plus-3:before {
  content: "\e11c";
}
.icon-google-plus-4:before {
  content: "\e11d";
}
.icon-google-drive:before {
  content: "\e11e";
}
.icon-facebook:before {
  content: "\e11f";
}
.icon-facebook-2:before {
  content: "\e120";
}
.icon-facebook-3:before {
  content: "\e121";
}
.icon-instagram:before {
  content: "\e122";
}
.icon-twitter:before {
  content: "\e123";
}
.icon-twitter-2:before {
  content: "\e124";
}
.icon-twitter-3:before {
  content: "\e125";
}
.icon-feed-2:before {
  content: "\e126";
}
.icon-feed-3:before {
  content: "\e127";
}
.icon-feed-4:before {
  content: "\e128";
}
.icon-youtube:before {
  content: "\e129";
}
.icon-youtube-2:before {
  content: "\e12a";
}
.icon-vimeo:before {
  content: "\e12b";
}
.icon-vimeo2:before {
  content: "\e12c";
}
.icon-vimeo-2:before {
  content: "\e12d";
}
.icon-lanyrd:before {
  content: "\e12e";
}
.icon-flickr:before {
  content: "\e12f";
}
.icon-flickr-2:before {
  content: "\e130";
}
.icon-flickr-3:before {
  content: "\e131";
}
.icon-flickr-4:before {
  content: "\e132";
}
.icon-picassa:before {
  content: "\e133";
}
.icon-picassa-2:before {
  content: "\e134";
}
.icon-dribbble:before {
  content: "\e135";
}
.icon-dribbble-2:before {
  content: "\e136";
}
.icon-dribbble-3:before {
  content: "\e137";
}
.icon-forrst:before {
  content: "\e138";
}
.icon-forrst-2:before {
  content: "\e139";
}
.icon-deviantart:before {
  content: "\e13a";
}
.icon-deviantart-2:before {
  content: "\e13b";
}
.icon-steam:before {
  content: "\e13c";
}
.icon-steam-2:before {
  content: "\e13d";
}
.icon-github:before {
  content: "\e13e";
}
.icon-github-2:before {
  content: "\e13f";
}
.icon-github-3:before {
  content: "\e140";
}
.icon-github-4:before {
  content: "\e141";
}
.icon-github-5:before {
  content: "\e142";
}
.icon-wordpress:before {
  content: "\e143";
}
.icon-wordpress-2:before {
  content: "\e144";
}
.icon-joomla:before {
  content: "\e145";
}
.icon-blogger:before {
  content: "\e146";
}
.icon-blogger-2:before {
  content: "\e147";
}
.icon-tumblr:before {
  content: "\e148";
}
.icon-tumblr-2:before {
  content: "\e149";
}
.icon-yahoo:before {
  content: "\e14a";
}
.icon-tux:before {
  content: "\e14b";
}
.icon-apple:before {
  content: "\e14c";
}
.icon-finder:before {
  content: "\e14d";
}
.icon-android:before {
  content: "\e14e";
}
.icon-windows:before {
  content: "\e14f";
}
.icon-windows8:before {
  content: "\e150";
}
.icon-soundcloud:before {
  content: "\e151";
}
.icon-soundcloud-2:before {
  content: "\e152";
}
.icon-skype:before {
  content: "\e153";
}
.icon-reddit:before {
  content: "\e154";
}
.icon-linkedin:before {
  content: "\e155";
}
.icon-lastfm:before {
  content: "\e156";
}
.icon-lastfm-2:before {
  content: "\e157";
}
.icon-delicious:before {
  content: "\e158";
}
.icon-stumbleupon-2:before {
  content: "\e159";
}
.icon-heart-2:before {
  content: "\e15a";
}
.icon-heart-broken:before {
  content: "\e15b";
}
.icon-thumbs-up:before {
  content: "\e15c";
}
.icon-thumbs-up-2:before {
  content: "\e15d";
}
.icon-happy:before {
  content: "\e15e";
}
.icon-happy-2:before {
  content: "\e15f";
}
.icon-smiley:before {
  content: "\e160";
}
.icon-smiley-2:before {
  content: "\e161";
}
.icon-tongue:before {
  content: "\e162";
}
.icon-tongue-2:before {
  content: "\e163";
}
.icon-sad:before {
  content: "\e164";
}
.icon-sad-2:before {
  content: "\e165";
}
.icon-wink:before {
  content: "\e166";
}
.icon-wink-2:before {
  content: "\e167";
}
.icon-grin:before {
  content: "\e168";
}
.icon-grin-2:before {
  content: "\e169";
}
.icon-cool:before {
  content: "\e16a";
}
.icon-cool-2:before {
  content: "\e16b";
}
.icon-angry:before {
  content: "\e16c";
}
.icon-angry-2:before {
  content: "\e16d";
}
.icon-evil:before {
  content: "\e16e";
}
.icon-evil-2:before {
  content: "\e16f";
}
.icon-shocked:before {
  content: "\e170";
}
.icon-shocked-2:before {
  content: "\e171";
}
.icon-confused:before {
  content: "\e172";
}
.icon-confused-2:before {
  content: "\e173";
}
.icon-neutral:before {
  content: "\e174";
}
.icon-neutral-2:before {
  content: "\e175";
}
.icon-wondering:before {
  content: "\e176";
}
.icon-wondering-2:before {
  content: "\e177";
}
.icon-point-up:before {
  content: "\e178";
}
.icon-point-right:before {
  content: "\e179";
}
.icon-point-down:before {
  content: "\e17a";
}
.icon-point-left:before {
  content: "\e17b";
}
.icon-warning:before {
  content: "\e17c";
}
.icon-notification:before {
  content: "\e17d";
}
.icon-question:before {
  content: "\e17e";
}
.icon-info:before {
  content: "\e17f";
}
.icon-info-2:before {
  content: "\e180";
}
.icon-blocked:before {
  content: "\e181";
}
.icon-cancel-circle:before {
  content: "\e182";
}
.icon-checkmark-circle:before {
  content: "\e183";
}
.icon-spam:before {
  content: "\e184";
}
.icon-close:before {
  content: "\e185";
}
.icon-checkmark:before {
  content: "\e186";
}
.icon-checkmark-2:before {
  content: "\e187";
}
.icon-spell-check:before {
  content: "\e188";
}
.icon-minus:before {
  content: "\e189";
}
.icon-plus:before {
  content: "\e18a";
}
.icon-enter:before {
  content: "\e18b";
}
.icon-exit:before {
  content: "\e18c";
}
.icon-play-2:before {
  content: "\e18d";
}
.icon-pause:before {
  content: "\e18e";
}
.icon-stop:before {
  content: "\e18f";
}
.icon-backward:before {
  content: "\e190";
}
.icon-forward-2:before {
  content: "\e191";
}
.icon-play-3:before {
  content: "\e192";
}
.icon-pause-2:before {
  content: "\e193";
}
.icon-stop-2:before {
  content: "\e194";
}
.icon-backward-2:before {
  content: "\e195";
}
.icon-forward-3:before {
  content: "\e196";
}
.icon-first:before {
  content: "\e197";
}
.icon-last:before {
  content: "\e198";
}
.icon-previous:before {
  content: "\e199";
}
.icon-next:before {
  content: "\e19a";
}
.icon-eject:before {
  content: "\e19b";
}
.icon-volume-high:before {
  content: "\e19c";
}
.icon-volume-medium:before {
  content: "\e19d";
}
.icon-volume-low:before {
  content: "\e19e";
}
.icon-volume-mute:before {
  content: "\e19f";
}
.icon-volume-mute-2:before {
  content: "\e1a0";
}
.icon-volume-increase:before {
  content: "\e1a1";
}
.icon-volume-decrease:before {
  content: "\e1a2";
}
.icon-loop:before {
  content: "\e1a3";
}
.icon-loop-2:before {
  content: "\e1a4";
}
.icon-loop-3:before {
  content: "\e1a5";
}
.icon-shuffle:before {
  content: "\e1a6";
}
.icon-arrow-up-left:before {
  content: "\e1a7";
}
.icon-arrow-up:before {
  content: "\e1a8";
}
.icon-arrow-up-right:before {
  content: "\e1a9";
}
.icon-arrow-right:before {
  content: "\e1aa";
}
.icon-arrow-down-right:before {
  content: "\e1ab";
}
.icon-arrow-down:before {
  content: "\e1ac";
}
.icon-arrow-down-left:before {
  content: "\e1ad";
}
.icon-arrow-left:before {
  content: "\e1ae";
}
.icon-arrow-up-left-2:before {
  content: "\e1af";
}
.icon-arrow-up-2:before,
.ui_tpicker_minute .ui-spinner-up:before,
.ui_tpicker_hour .ui-spinner-up:before,
.ui_tpicker_second .ui-spinner-up:before {
  content: "\e1b0";
}
.icon-arrow-up-right-2:before {
  content: "\e1b1";
}
.icon-arrow-right-2:before,
.ui-datepicker-next:before {
  content: "\e1b2";
}
.icon-arrow-down-right-2:before {
  content: "\e1b3";
}
.icon-arrow-down-2:before,
.ui_tpicker_minute .ui-spinner-down:before,
.ui_tpicker_hour .ui-spinner-down:before,
.ui_tpicker_second .ui-spinner-down:before {
  content: "\e1b4";
}
.icon-arrow-down-left-2:before {
  content: "\e1b5";
}
.icon-arrow-left-2:before,
.ui-datepicker-prev:before {
  content: "\e1b6";
}
.icon-arrow-up-left-3:before {
  content: "\e1b7";
}
.icon-arrow-up-3:before {
  content: "\e1b8";
}
.icon-arrow-up-right-3:before {
  content: "\e1b9";
}
.icon-arrow-right-3:before {
  content: "\e1ba";
}
.icon-arrow-down-right-3:before {
  content: "\e1bb";
}
.icon-arrow-down-3:before {
  content: "\e1bc";
}
.icon-arrow-down-left-3:before {
  content: "\e1bd";
}
.icon-arrow-left-3:before {
  content: "\e1be";
}
.icon-tab:before {
  content: "\e1bf";
}
.icon-checkbox-checked:before {
  content: "\e1c0";
}
.icon-checkbox-unchecked:before {
  content: "\e1c1";
}
.icon-checkbox-partial:before {
  content: "\e1c2";
}
[data-toggle] {
  position: relative;
  margin-bottom: 0;
  text-decoration: underline;
  color: #4790d2;
  cursor: pointer;
}
[data-toggle]:after {
  content: '';
  height: 0;
  width: 0;
  position: absolute;
  top: 50%;
  margin-left: 8px;
  margin-top: -2px;
  overflow: hidden;
  border-width: 6px 6px 0 6px;
  border-color: #4790d2 transparent transparent transparent;
  border-style: solid;
}
[data-toggle]:hover {
  color: #2c74b5;
}
[data-toggle]:hover:after {
  border-color: #2c74b5 transparent transparent transparent;
}
[data-toggle].open:after {
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent #4790d2 transparent;
}
[data-toggle].open:hover:after {
  border-color: transparent transparent #2c74b5 transparent;
}
[data-toggle] + * > :first-child {
  padding-top: 12px;
}
.fixed-bar {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 99;
  left: 200px;
  max-width: 1400px;
  padding: 10px 30px;
  background: rgba(255,255,255,0.85);
  border-top: 1px solid #ddd;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: left  0.3s  ease,   max-width  0.3s  ease;
  -o-transition: left  0.3s  ease,   max-width  0.3s  ease;
  -webkit-transition: left 0.3s ease, max-width 0.3s ease;
  -o-transition: left 0.3s ease, max-width 0.3s ease;
  transition: left 0.3s ease, max-width 0.3s ease;
}
.menu-hover .fixed-bar {
  left: 200px;
  max-width: 1400px;
}
.col-content .fixed-bar {
  left: 521px;
  max-width: 1079px;
}
.menu-hover .col-content .fixed-bar {
  left: 521px;
  max-width: 1079px;
}
.skbox-window .fixed-bar {
  display: none;
}
.paging {
  overflow: hidden;
}
.paging .icon {
  position: relative;
  top: 2px;
}
.paging a {
  margin: 0 2px;
}
.paging .prev {
  margin: 0 13px 0 0;
}
.paging .next {
  margin: 0 0 0 13px;
}
.paging .disabled,
.paging .disabled:hover {
  color: #ddd;
  cursor: default;
}
.paging .active {
  color: #666;
  text-decoration: none;
}
/*
 *	Forms
 */
.inp-text {
  display: block;
  border: 2px solid #ddd;
  padding: 6px 10px;
  background: #fff;
  color: #333;
  font-family: 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 14px;
  line-height: 20px;
  height: 36px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
}
.inp-text:focus {
  border-color: #b1b1b1;
}
.inp-text.error {
  border-color: #c00;
}
textarea.inp-text {
  height: auto;
}
select.inp-text[multiple] {
  height: auto;
}
@media (-webkit-min-device-pixel-ratio: 0) {
  .inp-fix-select select.inp-text {
    padding-right: 25px;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
  }
  .inp-fix-select:after {
    content: '';
    position: absolute;
    top: 16px;
    right: 10px;
    height: 0;
    width: 0;
    overflow: hidden;
    border-width: 6px 6px 0 6px;
    border-color: #333 transparent transparent transparent;
    border-style: solid;
    pointer-events: none;
  }
  .inp-fix-multiselect select {
    padding: 0;
  }
}
.w-full {
  width: 100%;
}
label + br ~ *,
label + br ~ .btn {
  margin-top: 3px;
}
.inp-fix {
  display: block;
  position: relative;
  overflow: hidden;
  color: #333;
}
.inp-fix .inp-text {
  width: 100%;
}
.btn.r + .inp-fix {
  padding-right: 10px;
}
.inp-item {
  position: relative;
  padding-left: 25px;
  display: inline-block;
}
.inp-item + .inp-item {
  margin-left: 20px;
}
.inp-item input[type="checkbox"],
.inp-item input[type="radio"] {
  position: absolute;
  left: -5000px;
}
.inp-item input[type="checkbox"] + label:before,
.inp-item input[type="radio"] + label:before,
.inp-item input[type="checkbox"] + span:before,
.inp-item input[type="radio"] + span:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  margin-top: 3px;
  width: 12px;
  height: 12px;
  border: 2px solid #ddd;
  border-radius: 3px;
  background: #fff;
}
.inp-item input[type="checkbox"]:focus + label:before,
.inp-item input[type="radio"]:focus + label:before,
.inp-item input[type="checkbox"]:focus + span:before,
.inp-item input[type="radio"]:focus + span:before {
  border-color: #b1b1b1;
}
.inp-item input[type="checkbox"]:checked + label:after,
.inp-item input[type="checkbox"]:checked + span:after {
  content: '\e186';
  position: absolute;
  left: 4px;
  top: 1px;
  margin-top: 3px;
  font-size: 12px;
}
.inp-item input[type="radio"] + label:before,
.inp-item input[type="radio"] + span:before {
  border-radius: 8px;
  left: 1px;
}
.inp-item input[type="radio"]:checked + label:after,
.inp-item input[type="radio"]:checked + span:after {
  content: '';
  position: absolute;
  top: 5px;
  left: 6px;
  margin-top: 3px;
  width: 6px;
  height: 6px;
  background: #666;
  border-radius: 3px;
}
.inp-list .inp-item {
  display: block;
  margin: 0 0 2px;
}
label {
  cursor: pointer;
}
.inp-icon-after .inp-text {
  padding-right: 35px;
}
.inp-icon-after .icon {
  position: absolute;
  right: 10px;
  top: 10px;
}
.inp-icon-after span.icon {
  pointer-events: none;
}
.inp-icon-before .inp-text {
  padding-left: 35px;
}
.inp-icon-before .icon {
  position: absolute;
  left: 10px;
  top: 10px;
}
.inp-icon-before span.icon {
  pointer-events: none;
}
.no-label {
  padding-top: 27px;
}
.inp-center,
.inp-center:first-child {
  margin-top: 6px;
  display: inline-block;
}
.btn {
  display: inline-block;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  text-decoration: none;
  overflow: visible;
  cursor: pointer;
  position: relative;
}
.ie7 .btn {
  display: inline;
  zoom: 1;
}
.btn > span {
  position: relative;
  display: block;
  padding: 0 20px;
  background: #4790d2;
  color: #fff;
  font: bold 14px/36px 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  border-radius: 3px;
  -webkit-transition: background 0.2s ease;
  -o-transition: background 0.2s ease;
  transition: background 0.2s ease;
}
.btn:not(.btn-disabled):hover > span {
  background: #3282cb;
}
.btn-green > span {
  background: #3c3;
  color: #fff;
}
.btn-green:not(.btn-disabled):hover > span {
  background: #2eb82e;
}
.btn-red > span {
  background: #c00;
  color: #fff;
}
.btn-red:not(.btn-disabled):hover > span {
  background: #b80000;
}
.btn-dark > span {
  background: #191919;
  color: #fff;
}
.btn-dark:not(.btn-disabled):hover > span {
  background: #171717;
}
.btn + .btn {
  margin-left: 6px;
}
.btn-disabled {
  opacity: 0.5;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
  cursor: default;
}
.btn:not(.btn-disabled):active > span {
  top: 1px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.5);
  box-shadow: inset 0 1px 1px rgba(0,0,0,0.5);
}
.btn-icon-before > span {
  padding-right: 15px;
  padding-left: 35px;
}
.btn-icon-before .icon {
  position: absolute;
  left: 11px;
  top: 50%;
  margin-top: -8px;
}
.btn-icon-after > span {
  padding-left: 15px;
  padding-right: 35px;
}
.btn-icon-after .icon {
  position: absolute;
  right: 11px;
  top: 50%;
  margin-top: -8px;
}
.icon-checkmark.r {
  margin-left: 10px;
  margin-top: 12px;
}
.ui-datepicker {
  display: none;
  background: #eee;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 15px 18px 12px;
  width: 270px;
  min-height: 213px;
  z-index: 2 !important;
}
@media (min-width: 651px) {
  .ui-datepicker {
    width: 419px;
    padding-right: 189px;
  }
  .ui-datepicker:after {
    content: '';
    position: absolute;
    right: 170px;
    top: 56px;
    bottom: 20px;
    width: 1px;
    background: #ddd;
  }
}
.ui-datepicker-header {
  position: relative;
  margin: 0 0 17px;
}
.ui-datepicker-calendar {
  margin: 0;
  border: none;
  font-size: 12px;
  font-weight: 600;
}
.ui-datepicker-calendar td,
.ui-datepicker-calendar th {
  background: none;
  padding: 0;
  border: none;
  text-align: center;
  text-transform: uppercase;
  color: #a8a8a8;
  padding-bottom: 4px;
}
.ui-datepicker-calendar td + td,
.ui-datepicker-calendar th + td,
.ui-datepicker-calendar td + th,
.ui-datepicker-calendar th + th {
  padding-left: 15px;
}
.ui-datepicker-calendar td a,
.ui-datepicker-calendar th a {
  text-decoration: none;
  color: #565656;
}
.ui-datepicker-calendar td .ui-state-highlight,
.ui-datepicker-calendar th .ui-state-highlight {
  color: $colorBrand;
}
.ui-datepicker-calendar th {
  font-size: 10px;
  font-weight: bold;
  padding-bottom: 9px;
}
.ui-datepicker-prev,
.ui-datepicker-next {
  position: absolute;
  top: 2px;
  left: 0;
  font-size: 14px;
  margin-left: -7px;
  color: #333;
  text-decoration: none;
  cursor: pointer;
}
.ui-datepicker-prev .ui-icon,
.ui-datepicker-next .ui-icon {
  display: none;
}
.ui-datepicker-next {
  left: auto;
  right: 0;
}
.ui-datepicker-title,
.ui_tpicker_time {
  text-align: center;
  font: bold 14px/18px 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  color: $colorBrand;
  text-transform: uppercase;
}
.ui_tpicker_time {
  margin-bottom: 24px;
}
.ui-datepicker-year,
.ui-datepicker-current,
.ui_tpicker_time_label,
.ui_tpicker_minute_label,
.ui_tpicker_hour_label,
.ui_tpicker_second_label {
  display: none;
}
.ui-timepicker-div {
  width: 134px;
  padding-top: 15px;
}
@media (min-width: 651px) {
  .ui-timepicker-div {
    position: absolute;
    right: 0;
    top: 0;
    padding-right: 18px;
  }
}
.ui_tpicker_minute,
.ui_tpicker_hour,
.ui_tpicker_second {
  position: relative;
  padding: 18px 0 44px;
  width: 36px;
  float: left;
}
.ui_tpicker_minute .ui-spinner-up,
.ui_tpicker_hour .ui-spinner-up,
.ui_tpicker_second .ui-spinner-up,
.ui_tpicker_minute .ui-spinner-down,
.ui_tpicker_hour .ui-spinner-down,
.ui_tpicker_second .ui-spinner-down {
  position: absolute;
  left: 50%;
  top: 50px;
  font-size: 14px;
  margin-left: -7px;
  color: #333;
  text-decoration: none;
  cursor: pointer;
}
.ui_tpicker_minute .ui-spinner-up .ui-button-text,
.ui_tpicker_hour .ui-spinner-up .ui-button-text,
.ui_tpicker_second .ui-spinner-up .ui-button-text,
.ui_tpicker_minute .ui-spinner-down .ui-button-text,
.ui_tpicker_hour .ui-spinner-down .ui-button-text,
.ui_tpicker_second .ui-spinner-down .ui-button-text {
  display: none;
}
.ui_tpicker_minute .ui-spinner-up,
.ui_tpicker_hour .ui-spinner-up,
.ui_tpicker_second .ui-spinner-up {
  top: 0;
}
.ui_tpicker_hour,
.ui_tpicker_minute,
.ui_tpicker_second {
  margin: 0;
}
.ui_tpicker_hour:after,
.ui_tpicker_minute:after,
.ui_tpicker_second:after {
  content: 'hod.';
  position: absolute;
  bottom: 0;
  left: 0;
  right: -3px;
  text-align: center;
  color: #666;
  font-size: 12px;
}
.ui_tpicker_minute,
.ui_tpicker_second {
  margin-left: 13px;
}
.ui_tpicker_minute:before,
.ui_tpicker_second:before {
  content: ':';
  position: absolute;
  left: -8px;
  top: 19px;
  color: #c5c5c5;
}
.ui_tpicker_minute:after,
.ui_tpicker_second:after {
  content: 'min.';
}
.ui_tpicker_second:after {
  content: 'sec.';
}
.ui-timepicker-input {
  padding: 5px 5px 3px;
  text-align: center;
  width: 100% !important;
  height: 28px;
}
.ui-datepicker-buttonpane {
  display: none;
  position: absolute;
  bottom: 17px;
  right: 15px;
}
button.ui-datepicker-close {
  display: inline-block;
  vertical-align: middle;
  font: 0px/0px a;
  color: transparent;
  text-decoration: none;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  text-decoration: none;
  border-top: 1px solid #7ac53b;
  border-bottom: 2px solid #63933d;
  border-radius: 3px;
  height: 36px;
  width: 70px;
  line-height: 36px;
  padding: 2px 0 0;
  background: #76ad48;
  color: #fff;
  text-decoration: none;
  -webkit-transition: background-color  0.2s,   border-color  0.2s;
  -o-transition: background-color  0.2s,   border-color  0.2s;
  -webkit-transition: background-color 0.2s, border-color 0.2s;
  -o-transition: background-color 0.2s, border-color 0.2s;
  transition: background-color 0.2s, border-color 0.2s;
  text-align: center;
}
.ie7 button.ui-datepicker-close {
  display: inline;
  zoom: 1;
}
button.ui-datepicker-close:after {
  content: '';
  display: inline-block;
  vertical-align: middle;
  position: relative;
  top: -3px;
}
.ie7 button.ui-datepicker-close:after {
  display: inline;
  zoom: 1;
}
button.ui-datepicker-close:hover {
  background: #8abd60;
}
/*
 *	Plugins
 */
.skbox-overlay {
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.4;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
}
.skbox-window {
  position: absolute;
  z-index: 101;
  color: #000;
  border-radius: 7px;
  -webkit-box-shadow: 0 0 20px rgba(0,0,0,0.5);
  box-shadow: 0 0 20px rgba(0,0,0,0.5);
}
.skbox-window:focus {
  outline: none;
}
.skbox-window .skbox-content {
  position: relative;
}
.skbox-window .skbox-spc {
  position: absolute;
  left: 20px;
  top: 20px;
  bottom: 20px;
  right: 20px;
  overflow: auto;
}
.skbox-window .has-scroll {
  padding-right: 20px;
}
.skbox-window .type-image .skbox-content {
  text-align: center;
}
.skbox-window .type-image .skbox-content span {
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.skbox-window .type-image .skbox-spc,
.skbox-window .type-image img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}
.skbox-window > .skbox-close {
  cursor: pointer;
  text-decoration: none;
  color: #c00;
  position: absolute;
  right: 14px;
  top: 14px;
  margin: 0;
}
.skbox-window-fixed {
  position: fixed;
}
.skbox-title {
  padding: 15px 40px 0 20px;
  font: 1.714285714285714em/1.2 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  color: #333;
  display: block;
}
.no-title .skbox-title {
  display: none;
}
.skbox-inner {
  position: relative;
  height: 100%;
  background: #fff;
}
.skbox-window > .skbox-prev,
.skbox-window > .skbox-next,
.skbox-window > .skbox-pages {
  display: none;
}
.skbox-window-group .skbox-pages {
  display: block;
}
.skbox-window-group .skbox-inner {
  margin-right: 110px;
}
.skbox-slides {
  position: relative;
  height: 100%;
}
.skbox-slide {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
}
.skbox-pages {
  width: 102px;
  position: absolute;
  right: 8px;
  top: 60px;
  bottom: 8px;
  text-align: center;
  overflow: hidden;
}
.skbox-pages a {
  display: block;
  font: bold 16px/32px arial, helvetica, sans-serif;
  color: #333;
  width: auto;
  margin: 0 0 10px;
  border: 1px solid #a3a3a3;
  padding: 1px;
  background: #fff;
  text-decoration: none;
}
.skbox-pages a:hover {
  border-color: #2886ca;
  color: #2886ca;
}
.skbox-pages .active {
  border-width: 2px;
  padding: 0;
  border-color: #2886ca;
}
.skbox-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.has-loading {
  background: url("../img/lb/lb-loading.gif") no-repeat 50% 50%;
}
.uploadifive-button {
  position: absolute !important;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100% !important;
  height: auto !important;
  cursor: pointer;
}
.uploadify-queue {
  display: none;
}
.uploadify-progress {
  padding: 4px;
  background: #eee;
  position: absolute;
  top: 50%;
  left: 10px;
  right: 10px;
  margin-top: -9px;
}
.uploadify-progress-bar {
  height: 10px;
  background: #4790d2;
}
html {
  height: 100%;
  background: #eee;
}
body {
  position: relative;
  color: #666;
  min-height: 100%;
  padding-left: 200px;
  background: #fff;
}
body:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 200px;
  background: #191919;
}
body.page-login {
  background: #4790d2;
  padding: 0;
  max-width: 1600px;
}
body.page-login:before {
  display: none;
}
.form-login {
  margin-top: 10px;
  padding: 25px 30px 22px;
  background: #fff;
}
.form-login .center {
  margin-top: 1.5em;
}
.app-status {
  position: fixed;
  height: 0;
  top: 0;
  left: 0;
  right: 0;
  max-width: 1600px;
  text-align: center;
  z-index: 102;
}
.app-status .bar {
  position: relative;
  display: inline-block;
  vertical-align: top;
  background: #191919;
  color: #fff;
}
.ie7 .app-status .bar {
  display: inline;
  zoom: 1;
}
.app-status .text {
  padding: 5px 20px;
}
.crossroad-attached .grid-row {
  margin-left: -10px;
}
.crossroad-attached .grid-row > * {
  padding-left: 10px;
}
.crossroad-attached .hd {
  padding-left: 30px;
  padding-right: 28px;
  margin: 0 0 3px;
}
.crossroad-attached .hd p {
  margin: 0;
}
.crossroad-attached .bd {
  margin: 0 0 15px;
}
.crossroad-attached .bd p {
  margin: 0;
}
.crossroad-attached ul {
  -webkit-transition: height 0.2s ease;
  -o-transition: height 0.2s ease;
  transition: height 0.2s ease;
}
.crossroad-attached li {
  position: relative;
}
.crossroad-attached .inner {
  position: relative;
  padding: 7px 28px 7px 30px;
  border: 1px solid transparent;
  margin: 0 -1px;
  border-radius: 3px;
  background: #fff;
  min-height: 36px;
}
.crossroad-attached .uploadify-progress {
  position: static;
  margin-top: 9px;
}
.crossroad-attached li:hover .inner {
  border: 1px solid #eee;
  margin: 0 -6px;
  padding: 7px 33px 7px 35px;
}
.crossroad-attached li:hover .inner .drag-area {
  left: 5px;
}
.crossroad-attached li:hover .inner .remove {
  right: 8px;
}
.crossroad-attached .drag-area {
  position: absolute;
  top: 7px;
  left: 0;
  bottom: 7px;
  width: 20px;
  background: #ddd;
  border-radius: 3px;
  cursor: pointer;
}
.crossroad-attached .drag-area:after {
  content: '';
  position: absolute;
  left: 5px;
  right: 5px;
  top: 50%;
  margin-top: -7px;
  height: 1px;
  background: #b1b1b1;
  -webkit-box-shadow: 0 1px 0 #eee, 0 3px 0 #b1b1b1, 0 4px 0 #eee, 0 6px 0 #b1b1b1, 0 7px 0 #eee, 0 9px 0 #b1b1b1, 0 10px 0 #eee, 0 12px 0 #b1b1b1, 0 13px 0 #eee;
  box-shadow: 0 1px 0 #eee, 0 3px 0 #b1b1b1, 0 4px 0 #eee, 0 6px 0 #b1b1b1, 0 7px 0 #eee, 0 9px 0 #b1b1b1, 0 10px 0 #eee, 0 12px 0 #b1b1b1, 0 13px 0 #eee;
}
.crossroad-attached .remove {
  position: absolute;
  right: 3px;
  top: 50%;
  margin-top: -8px;
  color: #c00;
}
.crossroad-attached .ui-sortable-helper {
  -webkit-box-shadow: 0 0 10px rgba(0,0,0,0.15);
  box-shadow: 0 0 10px rgba(0,0,0,0.15);
}
.btns-attached em {
  margin-left: 1em;
}
.crossroad-images {
  margin: 20px 0 0;
  position: relative;
}
.crossroad-images ul {
  font-family: 'Courier New', monospace;
  letter-spacing: -0.63em;
  word-spacing: -0.63em;
  margin-left: -10px;
}
.crossroad-images ul > * {
  display: inline-block;
  vertical-align: top;
  width: 160px;
  font-family: 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  letter-spacing: 0px;
  word-spacing: 0px;
}
.ie7 .crossroad-images ul > * {
  display: inline;
  zoom: 1;
}
@media (min-width: 1200px) {
  .crossroad-images li {
    width: 25%;
  }
}
@media (min-width: 1400px) {
  .crossroad-images li {
    width: 16.666%;
  }
}
.crossroad-images .inner {
  margin-bottom: 10px;
}
.crossroad-images .inner .name {
  display: block;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
  overflow: hidden;
}
.crossroad-images .thumb {
  position: relative;
  z-index: 2;
  border: 1px solid #eee;
  padding: 10px;
  margin-left: 10px;
  margin-bottom: 10px;
  background: #fff;
  overflow: hidden;
}
.crossroad-images .remove {
  position: absolute;
  right: -50px;
  top: -50px;
  opacity: 0;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  color: #c00;
  -webkit-transition: opacity 0.5s ease;
  -o-transition: opacity 0.5s ease;
  transition: opacity 0.5s ease;
}
.crossroad-images .img {
  position: relative;
  display: block;
  width: 100%;
  padding-top: 100%;
}
.crossroad-images img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  position: absolute;
  margin: auto;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}
.crossroad-images .loading img {
  opacity: 0;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
}
.crossroad-images .detail {
  position: absolute;
  left: 0;
  width: 100%;
  overflow: hidden;
  height: 0px;
/*&:after
			content ''
			position absolute
			bottom 0
			left 0
			right 0
			height 2px
			background $colorGray*/
}
.crossroad-images .detail-holder {
  margin-top: 10px;
  border: 2px solid #ddd;
  padding: 18px 20px 20px;
}
.crossroad-images li:hover .thumb {
  border: 2px solid #ddd;
  padding: 9px;
  cursor: pointer;
}
.crossroad-images li:hover .remove {
  opacity: 1;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  right: 6px;
  top: 6px;
}
.crossroad-images li.selected .thumb,
.crossroad-images li.selected:hover .thumb {
  border: 2px solid #4790d2;
  padding: 9px;
}
.crossroad-images li.selected .thumb:before,
.crossroad-images li.selected:hover .thumb:before {
  content: '';
  position: absolute;
  z-index: 5;
  width: 30px;
  height: 30px;
  background: #4790d2;
  top: 0;
  left: 0;
}
.crossroad-images li.selected .thumb:after,
.crossroad-images li.selected:hover .thumb:after {
  content: '\e186';
  position: absolute;
  top: 6px;
  left: 6px;
  z-index: 6;
  color: #fff;
}
.crossroad-images li.selected .remove,
.crossroad-images li.selected:hover .remove {
  right: 6px;
  top: 6px;
}
.crossroad-images .ui-sortable-helper .thumb {
  -webkit-box-shadow: 0 0 10px rgba(0,0,0,0.15);
  box-shadow: 0 0 10px rgba(0,0,0,0.15);
}
.crossroad-images .active-detail li {
  opacity: 0.5;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
}
.crossroad-images .active,
.crossroad-images .active:hover {
  opacity: 1;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
}
.crossroad-images .active .thumb,
.crossroad-images .active:hover .thumb {
  border: 2px solid #ddd;
  padding: 9px;
  padding-bottom: 23px;
  margin-bottom: -12px;
  border-bottom-width: 0px;
}
.crossroad-images .active .thumb:after,
.crossroad-images .active:hover .thumb:after {
  position: absolute;
  bottom: -12px;
  left: 50%;
  margin-left: -10px;
  height: 0;
  width: 0;
  overflow: hidden;
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent #eee transparent;
  border-style: solid;
}
/*
 *	Base layout
 */
#header {
  zoom: 1;
  background: #4790d2;
  color: #fff;
  position: relative;
  z-index: 10;
  padding: 0 0 0 20px;
  margin-left: -200px;
  min-height: 50px;
  -webkit-transition: margin-left 0.3s ease;
  -o-transition: margin-left 0.3s ease;
  transition: margin-left 0.3s ease;
}
#header:after,
#header:before {
  content: '';
  display: table;
  clear: both;
}
#header a {
  color: #fff;
}
.page-login #header {
  padding-top: 100px;
  padding-bottom: 15px;
  margin-left: 0;
  text-align: center;
}
.menu-hover #header {
  margin-left: -200px;
}
#user {
  float: right;
  position: relative;
  padding: 0 70px 0 25px;
  margin: 0;
  line-height: 48px;
}
#user > .icon {
  position: absolute;
  top: 16px;
  left: 0;
}
#user .logout {
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  background: rgba(0,0,0,0.2);
  width: 50px;
  line-height: 50px;
  text-align: center;
  -webkit-transition: background 0.2s ease;
  -o-transition: background 0.2s ease;
  transition: background 0.2s ease;
}
#user .logout:hover {
  background: rgba(0,0,0,0.3);
}
#logo {
  float: left;
  font: 20px/46px 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  margin: 1px 0 0;
  padding: 0;
}
#logo .icon {
  margin-right: 10px;
}
.page-login #logo {
  float: none;
  font-size: 32px;
}
#main {
  zoom: 1;
  max-width: 1400px;
  border-right: 1px solid #ddd;
}
#main:after,
#main:before {
  content: '';
  display: table;
  clear: both;
}
.page-login #main {
  max-width: 350px;
  margin: 0 auto;
}
/*
 *	Menu
 */
/* Nav skip */
#menu-accessibility {
  position: absolute;
  left: -5000px;
  top: 0;
}
#menu-accessibility a:focus,
#menu-accessibility a:active {
  position: absolute;
  top: 0;
  left: 5000px;
  width: 200px;
  padding: 2px 0 5px;
  z-index: 900;
  text-align: center;
  background: #fff;
}
.menu-main {
  font: 300 15px/21px 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  margin: 0 0 30px;
}
.menu-main h2 {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: bold;
  color: #444;
  margin: 0 0 1em;
}
.menu-main a {
  position: relative;
  display: block;
  padding: 7px 20px 8px 45px;
  margin: 0 -20px;
  text-decoration: none;
  color: #666;
  -webkit-transition: color  $t  ease,   background  $t  ease;
  -o-transition: color  $t  ease,   background  $t  ease;
  -webkit-transition: color $t ease, background $t ease;
  -o-transition: color $t ease, background $t ease;
  transition: color $t ease, background $t ease;
}
.menu-main a .icon {
  position: absolute;
  left: 20px;
  top: 10px;
}
.menu-main a:hover {
  color: #fff;
}
.menu-main a.active {
  background: #262626;
  color: #fff;
}
.menu-tabs,
.menu-tabs-reload {
  margin: 30px -30px 25px;
  padding: 0 30px;
  background: #eee;
}
.menu-tabs ul,
.menu-tabs-reload ul {
  font-family: 'Courier New', monospace;
  letter-spacing: -0.63em;
  word-spacing: -0.63em;
}
.menu-tabs ul > *,
.menu-tabs-reload ul > * {
  display: inline-block;
  vertical-align: top;
  width: auto;
  font-family: 'Open Sans', Arial, "Helvetica Neue", Helvetica, sans-serif;
  letter-spacing: 0px;
  word-spacing: 0px;
}
.ie7 .menu-tabs ul > *,
.ie7 .menu-tabs-reload ul > * {
  display: inline;
  zoom: 1;
}
.menu-tabs li + li,
.menu-tabs-reload li + li {
  margin-left: 35px;
}
.menu-tabs a,
.menu-tabs-reload a {
  position: relative;
  display: block;
  text-decoration: none;
  color: #666;
  padding: 13px 0;
}
.menu-tabs a.active,
.menu-tabs-reload a.active {
  color: #333;
}
.menu-tabs a.active:after,
.menu-tabs-reload a.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -8px;
  height: 0;
  width: 0;
  overflow: hidden;
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent #fff transparent;
  border-style: solid;
}
h2 + .menu-tabs {
  margin-top: 15px;
}
.tab-fragment {
  margin-bottom: 30px;
}
.sk-tab-hide {
  position: absolute;
  left: -5000px;
  top: -5000px;
  width: 100%;
}
.menu-tree {
  overflow: hidden;
  overflow-x: auto;
  padding-bottom: 50px;
}
.menu-tree li:before {
  display: none;
}
.jstree ul,
.jstree li {
  display: block;
  margin: 0 0 0 0;
  padding: 0 0 0 0;
  list-style-type: none;
}
.jstree li {
  display: block;
  min-height: 18px;
  line-height: 18px;
  white-space: nowrap;
  margin-left: 18px;
  min-width: 18px;
}
.jstree > ul > li {
  margin-left: 0px;
}
.jstree ins {
  display: inline-block;
  text-decoration: none;
  width: 18px;
  height: 24px;
  vertical-align: middle;
  margin: 0 0 0 0;
  padding: 0;
}
.jstree a {
  display: inline-block;
  line-height: 16px;
  height: 16px;
  color: #666;
  vertical-align: middle;
  white-space: nowrap;
  text-decoration: none;
  padding: 4px 4px;
  margin: 0;
}
.jstree a:focus {
  outline: none;
}
.jstree a > ins {
  height: 16px;
  width: 16px;
}
.jstree a > .jstree-icon {
  margin-right: 3px;
}
.jstree-rtl li {
  margin-left: 0;
  margin-right: 18px;
}
.jstree-rtl > ul > li {
  margin-right: 0px;
}
.jstree-rtl a > .jstree-icon {
  margin-left: 3px;
  margin-right: 0;
}
li.jstree-open > ul {
  display: block;
}
li.jstree-closed > ul {
  display: none;
}
.jstree li,
.jstree ins {
  background-image: url("../img/ico/tree/d.png");
  background-repeat: no-repeat;
  background-color: transparent;
  color: #4790d2;
}
.jstree li {
  background-position: -90px 1px;
  background-repeat: repeat-y;
}
.jstree li.jstree-last {
  background: transparent;
}
.jstree .jstree-open > ins {
  background-position: -72px -101px;
}
.jstree .jstree-closed > ins {
  background-position: -54px -101px;
}
.jstree .jstree-leaf > ins {
  background-position: -36px -101px;
}
.jstree .jstree-hovered {
  background: #dae9f6;
}
.jstree .jstree-clicked {
  background: #4790d2;
  color: #fff;
}
.jstree .jstree-clicked > ins {
  color: #fff;
}
.jstree a > .jstree-icon {
  background: none;
  vertical-align: middle;
  position: relative;
  top: -1px;
  margin-right: 5px;
}
.jstree a.jstree-loading .jstree-icon {
  background: url("../img/ico/tree/throbber.gif") center center no-repeat !important;
}
.jstree a.jstree-search {
  color: #0ff;
}
.jstree .jstree-no-dots .jstree-open > ins {
  background-position: -18px 0;
}
.jstree .jstree-no-dots .jstree-closed > ins {
  background-position: 0 0;
}
.jstree .jstree-no-icons a .jstree-icon {
  display: none;
}
.jstree .jstree-no-icons .jstree-checkbox {
  display: inline-block;
}
.jstree .jstree-search {
  font-style: italic;
}
.jstree .jstree-no-checkboxes .jstree-checkbox {
  display: none !important;
}
.jstree .jstree-checked > a > .jstree-checkbox {
  background-position: -38px -19px;
}
.jstree .jstree-checked > a > .jstree-checkbox:hover {
  background-position: -38px -37px;
}
.jstree .jstree-unchecked > a > .jstree-checkbox {
  background-position: -2px -19px;
}
.jstree .jstree-unchecked > a > .jstree-checkbox:hover {
  background-position: -2px -37px;
}
.jstree .jstree-undetermined > a > .jstree-checkbox {
  background-position: -20px -19px;
}
.jstree .jstree-undetermined > a > .jstree-checkbox:hover {
  background-position: -20px -37px;
}
.jstree .jstree-locked a {
  color: #c0c0c0;
  cursor: default;
}
.jstree .jstree-no-dots li,
.jstree .jstree-no-dots .jstree-leaf > ins {
  background: transparent;
}
#vakata-dragged.jstree ins {
  background: transparent !important;
}
#vakata-dragged.jstree .jstree-ok {
  background: url("../img/ico/tree/d.png") -2px -53px no-repeat !important;
}
#vakata-dragged.jstree .jstree-invalid {
  background: url("../img/ico/tree/d.png") -18px -53px no-repeat !important;
}
#jstree-marker.jstree {
  background: url("../img/ico/tree/d.png") -41px -57px no-repeat !important;
  text-indent: -100px;
}
body #vakata-contextmenu {
  background: #fff;
  border: 1px solid #ddd;
  -webkit-box-shadow: 0 0 10px rgba(0,0,0,0.15);
  box-shadow: 0 0 10px rgba(0,0,0,0.15);
  border-radius: 3px;
  padding: 5px;
}
body #vakata-contextmenu li {
  zoom: 1;
}
body #vakata-contextmenu li:before {
  display: none;
}
body #vakata-contextmenu li.vakata-separator {
  background: #fff;
  border-top: 1px solid #e0e0e0;
  margin: 0;
}
body #vakata-contextmenu li a {
  color: #666;
  line-height: 20px;
  padding: 1px 10px;
}
body #vakata-contextmenu li ins {
  display: none;
}
body #vakata-contextmenu li a:hover,
body #vakata-contextmenu .vakata-hover > a {
  color: #666;
  padding: 1px 10px;
  background: #dae9f6;
  border: none;
  color: #000;
  border-radius: 2px;
}
/*
 *	Crossroads
 */
.c-custom-fields__menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.c-custom-fields__menu li::before {
  display: none;
}
.c-custom-fields__item {
  border: 2px solid #ddd;
  margin: 0 0 20px;
}
.c-custom-fields__title {
  margin: 0;
  padding: 20px;
  background: #eee;
}
.c-custom-fields__bd {
  padding: 20px;
  margin: 0 !important;
}
.c-custom-fields__bd .sortable li {
  margin: 0 0 20px;
}
.c-custom-fields__bd .sortable li:last-child {
  margin-bottom: 0;
}
.c-custom-fields__ft {
  padding: 15px 20px;
  border-top: 1px solid #ddd;
}
.c-custom-fields__inp {
  margin-bottom: 20px;
}
.c-custom-fields__inp:last-child {
  margin-bottom: 0;
}
.c-custom-fields__label {
  display: inline-block;
  vertical-align: top;
  margin: 0 0 5px;
}
.crossroad-params-table {
  margin: -1.1em 0 0;
}
.crossroad-params-table td {
  padding: 10px 0 0 10px;
}
.crossroad-params-table td:first-child {
  padding-left: 0;
}
.crossroad-params-table td:first-child label {
  min-height: 30px;
}
.crossroad-params-table .title span {
  margin-top: 15px;
  display: block;
  color: #333;
  font-size: 1.28571em;
  padding-top: 10px;
  padding-bottom: 8px;
  border-top: 1px solid #eee;
}
/*
 *	Box
 */
.box-param-type h2 {
  margin: 0.65em 0;
}
.box-title {
  margin: 0 0 18px;
  line-height: 44px;
}
.box-title h1,
.box-title h2 {
  line-height: 44px;
  overflow: hidden;
  zoom: 1;
}
.box-title .r {
  margin-left: 30px;
  margin: 0;
}
.box-detail-table {
  margin-top: 2.5em;
  margin-bottom: 2.5em;
}
.box-detail-table td {
  padding-top: 6px;
}
.box-detail-table tr:first-child td {
  padding-top: 0;
}
.box-detail-table td:first-child {
  width: 0;
  white-space: nowrap;
  padding-right: 20px;
}
/*
 *	Form
 */
#form-search {
  position: absolute;
  right: 50%;
  margin-right: -480px;
  width: 200px;
  top: 50px;
}
.form-filter {
  position: relative;
  margin: 25px -30px;
  padding: 17px 90px 8px 30px;
  background: #eee;
}
.form-filter p {
  margin-bottom: 12px;
}
.form-filter .btns {
  position: absolute;
  right: 30px;
  bottom: 15px;
}
.form-filter .btns .icon {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
:first-child {
  margin-top: 0;
}
:last-child:not([class*="u-mb-"]) {
  margin-bottom: 0;
}

/*# sourceMappingURL=style.css.map */
