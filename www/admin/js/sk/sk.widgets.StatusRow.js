(function(sk, $)
{

	sk.widgets.StatusRow = function($element, options)
	{
		this.$element = $element;
		this.options = $.extend({
			'showDuration': 150,
			'textSelector': '.text',
			'changeDuration': 200
		}, options)
		this.isShown = false;
	};

	var _fn = sk.widgets.StatusRow.prototype;

	_fn.init = function()
	{
		this.$element
			.show()
			.css('top', -this.$element.height() );

		this.$text = this.$element.find( this.options.textSelector );

		return this;
	};

	_fn.destroy = function()
	{
		this.$element.remove();

		return this;
	};

	_fn.show = function()
	{
		this.isShown = true;

		this.$element
			.animate({'top': 0}, this.options.showDuration);
	};

	_fn.hide = function(delay)
	{
		this.isShown = false;

		this.$element
			.delay(delay || 0)
			.animate({'top': -this.$element.height() }, this.options.showDuration);
	};

	_fn.text = function(obj)
	{
		if(this.isShown)
		{
			var _this = this;
			var dur = _this.options.changeDuration / 2;

			this.$text
				.fadeOut(dur, function()
				{
					obj.klass && _this.addClass(obj.klass);

					$(this)
						.text(obj.text)
						.fadeIn(dur, function()
						{
							_this = dur = null;
						});
				});
		}
		else
		{
			this.$text
				.text(obj.text);

			this.$element
				.css('top', -this.$element.height() );
		}
	};

	_fn.addClass = function(klass)
	{
		this.klasses = klass;
		this.$element.addClass(klass);
	};

	_fn.removeClass = function(klass)
	{
		this.$element.removeClass(klass || this.klasses || '' );
	};

})(sk, jQuery);