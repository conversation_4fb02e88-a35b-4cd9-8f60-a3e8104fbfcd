@use 'base/variables';

.img {
	position: relative;
	display: block;
	overflow: hidden;
	&::before {
		content: '';
		display: block;
		padding-top: 100%;
		pointer-events: none;
	}
	> *:where(img, .img__media, lite-youtube, lite-vimeo, video, iframe) {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		@supports (object-fit: cover) {
			object-fit: cover;
		}
	}

	// MODIF
	&--16-9::before {
		padding-top: percentage(calc(9 / 16));
	}
	&--2-1::before {
		padding-top: percentage(calc(1 / 2));
	}
	&--contain > *:where(img, .img__media, lite-youtube, lite-vimeo, video, iframe) {
		object-fit: contain;
	}
}
