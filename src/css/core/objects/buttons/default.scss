@use 'base/variables';

.btn {
	--btn-fs: 3.6rem;
	--btn-bg: #{variables.$color-black};
	--btn-c: #{variables.$color-white};
	--btn-bdc: transparent;
	--btn-hover-bg: #{variables.$color-hover};
	--btn-hover-c: #{variables.$color-white};
	--btn-hover-bdc: transparent;
	--btn-h: 3rem;
	--btn-gap: 1rem;
	--btn-icon-size: 2.5rem;
	$s: &;
	display: inline-block;
	vertical-align: middle;
	padding: 0;
	border: 0;
	background: none;
	text-decoration: none;
	cursor: pointer;
	&__text {
		position: relative;
		display: flex;
		gap: var(--btn-gap);
		justify-content: center;
		align-items: center;
		min-height: var(--btn-h);
		padding: 0.9rem 6.6rem 1.1rem;
		border: 0.1rem solid var(--btn-bdc);
		background-color: var(--btn-bg);
		color: var(--btn-c);
		font-family: variables.$font-secondary;
		font-size: clamp(2.5rem, calc(36 / 1920 * 100vw), var(--btn-fs));
		line-height: variables.$line-height;
		text-align: center;
		text-transform: uppercase;
		text-decoration: none;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t;
		font-variation-settings: 'wdth' 60, 'wght' 700, 'ital' 0;
	}
	&__icon {
		flex: 0 0 auto;
		width: var(--btn-icon-size);
		height: var(--btn-icon-size);
	}

	// VARIANT
	&--block {
		display: block;
		min-width: 100%;
	}
	&--loader &__text::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: calc(var(--btn-h) / 3);
		height: calc(var(--btn-h) / 3);
		margin: calc(var(--btn-h) / -6) 0 0 calc(var(--btn-h) / -6);
		border: 0.1rem solid var(--btn-c);
		border-top-color: transparent;
		border-radius: 50%;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
	}

	// STATEs
	&:disabled,
	&.is-disabled {
		opacity: 0.5;
		pointer-events: none;
	}
	.is-loading &--loader,
	.hoverevents .is-loading &--loader:hover,
	&--loader.is-loading,
	.hoverevents &--loader:hover.is-loading {
		position: relative;
		pointer-events: none;
		#{$s}__text,
		#{$s}__text .icon-svg {
			color: transparent;
		}
		#{$s}__text::before {
			opacity: 1;
			visibility: visible;
			animation: animation-rotate 0.8s infinite linear;
		}
	}

	// HOVERs
	.hoverevents &:hover &__text {
		border-color: var(--btn-hover-bdc);
		background-color: var(--btn-hover-bg);
		color: var(--btn-hover-c);
	}
	&:focus-visible {
		outline: 0.2rem solid variables.$color-black;
		outline-offset: 0.2rem;
	}
}
