@use 'base/variables';
@use 'config';

.inp-label {
	display: inline-block;
	vertical-align: top;
	margin-bottom: 1.5rem;
	font-size: clamp(1.6rem, calc(22 / 1920 * 100vw), 2.2rem);
	.inp & {
		position: absolute;
		top: 1.1rem;
		left: 0;
		z-index: 1;
		display: inline-block;
		margin: 0;
		background-color: transparent;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		pointer-events: none;
		transition: top variables.$t, left variables.$t, font-size variables.$t, background-color variables.$t, color variables.$t;
	}

	// STATEs
	.no-js &,
	.has-focus &,
	.is-filled & {
		top: -1rem;
		left: 0.5rem;
		padding: 0.1rem;
		background-color: variables.$color-secondary;
		color: variables.$color-white;
		font-size: 1.2rem;
	}
	.is-filled:not(.has-focus) & {
		left: 0;
		padding: 0;
		background-color: transparent;
		color: variables.$color-black;
	}

	// VARIANTs
	.inp &--tel {
		position: relative;
		top: auto;
	}

	// MQ
	@media (config.$md-up) {
		// STATEs
		.no-js &,
		.has-focus &,
		.is-filled & {
			top: -1.2rem;
		}
	}
	@media (config.$lg-up) {
		// STATEs
		.no-js &,
		.has-focus &,
		.is-filled & {
			top: -1.5rem;
			font-size: 1.6rem;
		}
	}
}
