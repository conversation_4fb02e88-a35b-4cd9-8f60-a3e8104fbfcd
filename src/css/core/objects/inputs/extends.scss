@use 'base/variables';

%inp {
	display: block;
	width: 100%;
	padding: 0.8rem 0;
	border: 0.2rem solid variables.$color-bd;
	border-width: 0 0 0.2rem;
	border-radius: 0;
	background-color: transparent;
	color: variables.$color-black;
	font-size: clamp(1.6rem, calc(22 / 1920 * 100vw), 2.2rem);
	appearance: none;
	transition: background-color variables.$t, border-color variables.$t, padding variables.$t;

	// hide number arrows
	&::-webkit-outer-spin-button,
	&::-webkit-inner-spin-button {
		margin: 0;
		-webkit-appearance: none;
	}
	&[type='number'] {
		-moz-appearance: textfield;
	}

	// STATEs
	&:disabled {
		background-color: variables.$color-bg;
	}

	&:focus {
		border-color: variables.$color-secondary;
	}
	&:focus-visible {
		padding: 0.8rem 0.5rem;
		border-color: variables.$color-secondary;
		outline: 0.2rem solid variables.$focus-outline-color;
	}
	.has-error & {
		border-color: variables.$color-red;
	}
}

.inp {
	position: relative;
	.icon-svg {
		pointer-events: none;
	}
	&__info {
		position: absolute;
		display: block;
		margin-top: 0;
		font-size: 1rem;
		line-height: calc(16 / 10);
	}

	// STATEs
	&--required &-label {
		&::before {
			content: '*';
		}
	}
}
