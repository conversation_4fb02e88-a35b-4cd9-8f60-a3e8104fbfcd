@use 'config';
@use 'base/variables';
@use 'base/functions';

.header {
	$s: &;
	padding: clamp(6rem, calc(230 / 1920 * 100vw), 23rem) 0 clamp(4rem, calc(110 / 1920 * 100vw), 11rem);
	border-bottom: 0.1rem solid variables.$color-black;
	background: linear-gradient(
		to bottom,
		variables.$color-yellow 0%,
		variables.$color-yellow 37.76%,
		variables.$color-blue 37.76%,
		variables.$color-blue 100%
	);

	&__logo {
		display: block;
		margin: 0;
		padding: 0;
		font-family: variables.$font-primary;
		font-size: 100%;
		line-height: 1;
	}
	&__badge {
		position: absolute;
		top: calc(50% + 2rem);
		right: 2rem;
		width: clamp(8rem, calc(120 / 375 * 100vw), 12rem);
		transform: translateY(-50%);
	}

	// VARIANTs
	&--cookie {
		position: relative;
		border-width: 0;
		background: transparent;
		&::before {
			content: '';
			position: absolute;
			inset: 0;
			bottom: 40%;
			background: variables.$color-yellow;
		}
		#{$s}__logo-blue {
			display: none;
		}
		#{$s}__badge {
			top: auto;
			bottom: clamp(-4rem, calc(-60 / 375 * 100vw), -6rem);
			transform: translateY(0);
		}
	}

	// MQ
	@media (config.$xs-up) {
		&__badge {
			width: clamp(12rem, calc(217 / 1920 * 100vw), 21.7rem);
		}
	}
	@media (config.$md-up) {
		&__badge {
			top: 50%;
			right: calc(40 / 1920 * 100vw);
		}
	}
	@media (config.$xxxl-up) {
		&__badge {
			right: 4rem;
		}
	}
}
