html {
	--scroll-offset: var(--header-height);
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	min-height: 100%;
	scrollbar-gutter: stable;
	@media screen and (prefers-reduced-motion: no-preference) {
		scroll-behavior: smooth;
	}
}
*:target {
	scroll-margin-top: var(--scroll-offset);
}
*,
*::before,
*::after {
	box-sizing: inherit;
}
body {
	position: relative;
	display: flex;
	flex: 1;
	flex-direction: column;
	min-width: 32rem;
}
:first-child {
	margin-top: 0;
}
