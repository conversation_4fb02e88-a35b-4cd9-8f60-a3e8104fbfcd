@use 'config';

:root {
	--grid-gutter: 2rem;
	--row-main-gutter: var(--grid-gutter);
	--row-main-width: calc(144rem + 2 * var(--row-main-gutter));
}

// Colors
$color-black: #000000;
$color-white: #ffffff;
$color-red: #ff0000;
$color-blue: #cde8f0;
$color-green: #008800;
$color-yellow: #ffcc23;
$color-lime: #d6ff27;
$color-orange: #ffaa00;
$color-orange-dk: #ff5c15;
$color-gray: #808080;

$color-primary: $color-blue;
$color-secondary: $color-orange-dk;

$color-text: $color-black;
$color-bd: $color-black;
$color-bg: #eeeeee;
$color-link: #47737f;
$color-hover: $color-secondary;

$color-facebook: #3b5998;
$color-twitter: #1da1f2;
$color-google: #dd4b39;
$color-youtube: #ff0000;
$color-linkedin: #0077b5;
$color-instagram: #c13584;
$color-pinterest: #bd081c;

// Font
$font-system: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$font-primary: 'degular-variable', $font-system;
$font-secondary: 'obviously-variable', $font-primary;
$font-tertiary: 'ohno-casual-variable', $font-primary;
$font-size: 3rem;
$line-height: calc(36 / 30);

// Typography
$typo-space-vertical: 1.25em;

// Focus
$focus-outline-color: $color-secondary;
$focus-outline-style: solid;
$focus-outline-width: 0.1rem;

// Spacing
$utils-spacing: (
	'0': 0,
	'sm': 2rem,
	'md': 4rem,
	'lg': 6rem,
	'xl': 8rem,
	'2xl': 10rem,
	'3xl': 13rem,
	'4xl': 15rem
);

// Grid
$grid-columns: 12;
$grid-gutter: var(--grid-gutter);
$row-main-width: var(--row-main-width);
$row-main-gutter: var(--row-main-gutter);

// Paths
$img-path: map-get(config.$paths, 'images');
$fonts-path: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.3s;

// SVGs
$svg-bullet: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath d='M0 0h4v4H0z'/%3E%3C/svg%3E%0A";
$svg-select: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 5'%3E%3Cpath d='M10 0L5 5 0 0'/%3E%3C/svg%3E%0A";
