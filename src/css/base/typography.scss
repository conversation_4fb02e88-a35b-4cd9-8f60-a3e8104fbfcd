@use 'config';
@use 'base/variables';

html {
	color: variables.$color-text;
	font-size: 62.5%;
	font-variation-settings: 'opsz' 6, 'wght' 400, 'ital' 0;
}

body {
	font-family: variables.$font-primary;
	font-size: clamp(1.6rem, calc(30 / 1920 * 100vw), variables.$font-size);
	line-height: variables.$line-height;
}

// Headings
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 1.5em 0 0.5em;
	font-family: variables.$font-secondary;
	line-height: 1.2;
	text-transform: uppercase;
	font-variation-settings: 'wdth' 60, 'wght' 700, 'ital' 0;
}
h1,
.h1 {
	font-size: clamp(3rem, calc(115 / 1920 * 100vw), 11.5rem);
}
h2,
.h2 {
	font-size: clamp(3rem, calc(115 / 1920 * 100vw), 11.5rem);
}
h3,
.h3 {
	font-family: variables.$font-primary;
	font-size: clamp(2.4rem, calc(40 / 1920 * 100vw), 4rem);
	font-variation-settings: 'opsz' 6, 'wght' 600, 'ital' 0;
}
h4,
.h4 {
	font-size: 1.8rem;
}
h5,
.h5 {
	font-size: 1.4rem;
}
h6,
.h6 {
	font-size: 1.2rem;
}

// Paragraph
p {
	margin: 0 0 variables.$typo-space-vertical;
}
hr {
	height: 0.1rem;
	margin: variables.$typo-space-vertical 0;
	border: solid variables.$color-bd;
	border-width: 0.1rem 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 0 0 variables.$typo-space-vertical;
	padding: 0;
	p {
		margin-bottom: 0;
	}
}

// Links
a,
.as-link {
	color: variables.$color-link;
	text-decoration: underline;
	transition: color variables.$t;
	-webkit-tap-highlight-color: transparent;
	.hoverevents &:hover {
		color: variables.$color-hover;
	}
}

.as-link {
	cursor: pointer;
}

// Lists
*:is(ul, ol, dl) {
	margin: 0 0 variables.$typo-space-vertical;
	padding: 0;
	list-style: none;
}
li {
	margin: 0 0 calc(variables.$typo-space-vertical / 4);
	padding: 0 0 0 2rem;
}
ul {
	li {
		background-image: url(variables.$svg-bullet);
		background-position: 0.5rem 0.5em;
		background-repeat: no-repeat;
		background-size: 0.4rem 0.4rem;
	}
}
ol {
	counter-reset: item;
	li {
		position: relative;
		&::before {
			content: counter(item) '.';
			counter-increment: item;
			position: absolute;
			top: 0;
			left: 0;
		}
	}
	ol {
		li {
			&::before {
				content: counter(item, lower-alpha) '.';
			}
		}
	}
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 calc(variables.$typo-space-vertical / 2);
	padding: 0;
}

// Tables
table {
	--table-x-padding: 2rem;
	--table-y-padding: 1.5rem;
	--table-bd-color: #{variables.$color-bd};
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	margin: 0 0 variables.$typo-space-vertical;
	border: 0.1rem solid var(--table-bd-color);
}
caption {
	padding: 0 0 1rem;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
*:is(td, th) {
	vertical-align: top;
	padding: var(--table-y-padding) var(--table-x-padding);
	border: 0.1rem solid var(--table-bd-color);
}
th {
	font-weight: bold;
	text-align: left;
}
thead th {
	background: variables.$color-bg;
}

// Image
figure {
	margin-bottom: variables.$typo-space-vertical;
}
figcaption {
	margin-top: 0.5em;
}

img {
	max-width: 100%;
	height: auto;
}
