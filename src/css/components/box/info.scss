@use 'config';
@use 'base/variables';

.b-info {
	background-color: variables.$color-blue;
	&__inner {
		margin-bottom: clamp(5rem, calc(100 / 1920 * 100vw), 10rem);
	}
	&__form {
		width: 100%;
		/* stylelint-disable-next-line declaration-no-important */
		margin-top: -7.5rem !important;
		padding: clamp(4rem, calc(85 / 1920 * 100vw), 8.5rem) clamp(3rem, calc(60 / 1920 * 100vw), 6rem);
		border: 0.2rem solid variables.$color-bd;
		background-color: variables.$color-yellow;
	}
	&__info {
		padding-top: clamp(2rem, calc(40 / 1920 * 100vw), 4rem);
		font-size: clamp(2rem, calc(30 / 1920 * 100vw), 3rem);
		text-transform: uppercase;
		a {
			color: variables.$color-black;
		}
	}

	// MQ
	@media (config.$xl-up) {
		&__inner {
			display: flex;
			justify-content: space-between;
		}
		&__form {
			max-width: 68.6rem;
		}
		&__info {
			text-align: right;
		}
	}
}
