// Box
@forward 'components/box/annot';
@forward 'components/box/article';
@forward 'components/box/cookie';
@forward 'components/box/header';
@forward 'components/box/map';
@forward 'components/box/suggest';
@forward 'components/box/content';
@forward 'components/box/smallbasket';
@forward 'components/box/cart-summary';
@forward 'components/box/prebasket';
@forward 'components/box/address';
@forward 'components/box/product-detail';
@forward 'components/box/std';
@forward 'components/box/filters';
@forward 'components/box/parameters';
@forward 'components/box/product';
@forward 'components/box/modal';
@forward 'components/box/story';
@forward 'components/box/info';

// Crossroad
@forward 'components/crossroad/categories';

// Form
@forward 'components/form/filter';
@forward 'components/form/open';
@forward 'components/form/search';
@forward 'components/form/basket';
@forward 'components/form/method';
@forward 'components/form/address';
@forward 'components/form/contact';

// Menu
@forward 'components/menu/accessibility';
@forward 'components/menu/main';
@forward 'components/menu/submenu';
@forward 'components/menu/login';
@forward 'components/menu/lang';
@forward 'components/menu/footer';
@forward 'components/menu/breadcrumb';
