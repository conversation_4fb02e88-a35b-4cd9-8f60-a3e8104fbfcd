import { Controller } from '@hotwired/stimulus';

export default class focusInput extends Controller {
	static targets = ['input'];
	connect() {
		const inp = this.inputTarget;
		const inputPlaceholder = inp.getAttribute('placeholder');

		if ((inputPlaceholder && inputPlaceholder.length > 0) || inp.value != '') {
			this.element.classList.add('is-filled');
		} else {
			if (inp) {
				inp.addEventListener('focus', this.focus.bind(this));
				inp.addEventListener('blur', this.blur.bind(this));
			}
		}
	}
	disconnect() {
		const inp = this.inputTarget;

		if (inp) {
			inp.removeEventListener('focus', this.focus.bind(this));
			inp.removeEventListener('blur', this.blur.bind(this));
		}
	}

	focus() {
		this.element.classList.add('has-focus');
	}

	blur(event) {
		var inputValue = event.currentTarget.value;

		if (inputValue == '') {
			this.element.classList.remove('is-filled');
			this.element.classList.remove('has-focus');
		} else {
			this.element.classList.add('is-filled');
			this.element.classList.remove('has-focus');
		}
	}
}
