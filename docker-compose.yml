networks:
  kohinoor:
  superkoders:
      external: true

volumes:
  db:
  es:

services:
  app:
    build:
      dockerfile: docker/app/Dockerfile
      context: .
    hostname: app
    container_name: kohinoor_app
    restart: unless-stopped
    ports:
      - "8080:80"
    networks:
      - kohinoor
      - superkoders
    labels:
        - "traefik.enable=true"
        - "traefik.docker.network=superkoders"
        - "traefik.http.routers.kohinoor.rule=Host(`kohinoor.superkoders.test`)"
        - "traefik.http.routers.kohinoor.tls=true"
    volumes:
      - .:/var/www/html
      - ./docker/app/php-xdebug-${SUPERADMIN_XDEBUG:-off}.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - db
      - es
      - front
      - admin

  front:
    build:
      dockerfile: docker/front/Dockerfile
      context: .
    container_name: kohinoor_front
    restart: unless-stopped
    networks:
      - kohinoor
    volumes:
      - .:/app

  admin:
    build:
      dockerfile: docker/admin/Dockerfile
      context: .
    container_name: kohinoor_admin
    restart: unless-stopped
    networks:
      - kohinoor
    volumes:
      - .:/app

  adminer:
    image: adminer
    restart: always
    networks:
      - kohinoor
    ports:
      - "81:8080"

  db:
    image: mariadb:10
    hostname: kohinoor_db
    container_name: kohinoor_db
    restart: unless-stopped
    networks:
      - kohinoor
    ports:
      - "3306:3306"
    volumes:
        - db:/var/lib/mysql
        - ./docker/db:/docker/db
        - ./docker/db/compare-db.sh:/docker/db/compare-db.sh
    environment:
      MARIADB_ROOT_PASSWORD: 'root'
      MARIADB_DATABASE: 'kohinoor'
      MARIADB_USER: 'kohinoor'
      MARIADB_PASSWORD: 'kohinoor'

  db-init:
      image: mariadb:10
      container_name: kohinoor_db-init
      depends_on:
          - db
      networks:
          - kohinoor
      volumes:
          - ./docker/db/init-db.sh:/docker/db/init-db.sh
          - ./docker/db:/docker/db
      environment:
          MARIADB_HOST: 'kohinoor_db'
          MARIADB_ROOT_PASSWORD: 'root'
          MARIADB_DATABASE: 'kohinoor'
          MARIADB_USER: 'kohinoor'
          MARIADB_PASSWORD: 'kohinoor'
      entrypoint: [ "/docker/db/init-db.sh" ]

  es:
    image: elasticsearch:7.17.6
    hostname: kohinoor_es
    container_name: kohinoor_es
    restart: unless-stopped
    networks:
      - kohinoor
    ports:
      - "9200:9200"
    volumes:
      - es:/usr/share/elasticsearch/data
    environment:
      "discovery.type": single-node

  redis:
    image: redis:latest
    hostname: kohinoor_redis
    container_name: kohinoor_redis
    restart: unless-stopped
    networks:
      - kohinoor
    ports:
      - "6379:6379"

  mailcatcher:
    image: dockage/mailcatcher
    hostname: kohinoor_mailcatcher
    container_name: kohinoor_mailcatcher
    restart: unless-stopped
    networks:
      - kohinoor
    ports:
      - "1080:1080"
