-- MySQL dump 10.13  Distrib 5.7.39-42, for debian-linux-gnu (x86_64)
--
-- Host: 127.0.0.1    Database: 1691_kohinoor
-- ------------------------------------------------------
-- Server version	5.7.39-42-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*!50717 SELECT COUNT(*) INTO @rocksdb_has_p_s_session_variables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'performance_schema' AND TABLE_NAME = 'session_variables' */;
/*!50717 SET @rocksdb_get_is_supported = IF (@rocksdb_has_p_s_session_variables, 'SELECT COUNT(*) INTO @rocksdb_is_supported FROM performance_schema.session_variables WHERE VARIABLE_NAME=\'rocksdb_bulk_load\'', 'SELECT 0') */;
/*!50717 PREPARE s FROM @rocksdb_get_is_supported */;
/*!50717 EXECUTE s */;
/*!50717 DEALLOCATE PREPARE s */;
/*!50717 SET @rocksdb_enable_bulk_load = IF (@rocksdb_is_supported, 'SET SESSION rocksdb_bulk_load = 1', 'SET @rocksdb_dummy_bulk_load = 0') */;
/*!50717 PREPARE s FROM @rocksdb_enable_bulk_load */;
/*!50717 EXECUTE s */;
/*!50717 DEALLOCATE PREPARE s */;

--
-- Table structure for table `alias`
--

DROP TABLE IF EXISTS `alias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alias` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
  `module` varchar(30) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
  UNIQUE KEY `idref_modul_lg` (`referenceId`,`module`,`mutationId`) USING BTREE,
  KEY `idref` (`referenceId`),
  KEY `mutationId` (`mutationId`),
  KEY `modul` (`module`) USING BTREE,
  CONSTRAINT `alias_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2267 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alias`
--

LOCK TABLES `alias` WRITE;
/*!40000 ALTER TABLE `alias` DISABLE KEYS */;
INSERT INTO `alias` VALUES (75,'hledat','tree',42,1),(128,'obchodni-podminky','tree',40,1),(166,'profil','tree',75,1),(167,'prohlaseni-o-pouzivani-cookies','tree',12,1),(172,'registrace','tree',76,1),(211,'prihlaseny','tree',72,1),(219,'zapomenute-heslo','tree',74,1),(220,'e-shop','tree',21,1),(224,'prihlaseni','tree',37,1),(229,'reset-hesla','tree',26,1),(231,'kontakt','tree',8,1),(247,'produktova-kategorie-2','tree',78,1),(442,'noze','tree',94,1),(454,'o-nas','tree',102,1),(604,'zpracovanim-osobnich-udaju','tree',255,1),(615,'stranka-nenalezena','tree',262,1),(2107,'popupcontact','tree',394,1),(2110,'produktova-kategorie-1','tree',77,1),(2112,'','tree',1,1),(2139,'system-pages-only-develpers','tree',398,1),(2145,'odhlasit','tree',405,1),(2159,'zmena-hesla','tree',413,1),(2197,'styleguide','tree',445,1),(2199,'blog','tree',446,1),(2200,'kategorie-1','tree',447,1),(2201,'kategorie-2','tree',448,1),(2202,'tag-super','blogTagLocalization',1,1),(2204,'tag-0','blogTagLocalization',2,1),(2206,'tomas-fuk','authorLocalization',1,1),(2207,'autori','tree',449,1),(2222,'pepik-nepovim','authorLocalization',2,1),(2226,'','tree',2,2),(2229,'test-product','productLocalization',5,2),(2230,'produkt-7','productLocalization',6,1),(2233,'diary','productLocalization',9,2),(2234,'diar','productLocalization',10,1),(2235,'energy-drink','productLocalization',13,2),(2236,'energy-drink','productLocalization',14,1),(2237,'bussiness-cards-pack','productLocalization',15,2),(2238,'sada-vizitek','productLocalization',16,1),(2239,'sport-tee','productLocalization',17,2),(2240,'sk-sportovni-tricko','productLocalization',18,1),(2241,'test-product-4','productLocalization',19,2),(2242,'vzduchovka-hammerli-hunter-force-900','productLocalization',20,1),(2249,'predkosik','tree',450,1),(2250,'kosik','tree',451,1),(2251,'doprava-a-platba','tree',452,1),(2252,'dorucovaci-udaje','tree',453,1),(2253,'objednavka-dokoncena','tree',454,1),(2256,'novy-clanek','blogLocalization',12,1),(2257,'moje-adresy','tree',455,1),(2258,'historie-objednavek','tree',456,1),(2259,'historie-objednavek-detail','tree',457,1),(2262,'produkt-test','productLocalization',32,1),(2264,'rozcestnik-komponent','tree',459,1),(2265,'sk-tricko','productLocalization',36,1),(2266,'ostatni','tree',460,1);
/*!40000 ALTER TABLE `alias` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `alias_history`
--

DROP TABLE IF EXISTS `alias_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alias_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alias` varchar(120) CHARACTER SET utf8mb4 NOT NULL,
  `module` varchar(30) NOT NULL,
  `referenceId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `alias_lg` (`alias`,`mutationId`),
  UNIQUE KEY `alias_modul_idref_lg` (`alias`,`module`,`referenceId`,`mutationId`) USING BTREE,
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `alias_history_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alias_history`
--

LOCK TABLES `alias_history` WRITE;
/*!40000 ALTER TABLE `alias_history` DISABLE KEYS */;
INSERT INTO `alias_history` VALUES (12,'','tree',1,1),(13,'czaaaa','tree',1,1),(3,'nova-stranka','tree',459,1),(1,'objednavka-krok-1','tree',452,1),(2,'objednavka-krok-2','tree',453,1),(6,'produkt-2','productLocalization',18,1),(8,'produkt-4','productLocalization',16,1),(10,'produkt-5','productLocalization',14,1),(5,'test-11-en','productLocalization',9,2),(7,'test-product-5','productLocalization',17,2),(9,'test-product-6','productLocalization',15,2),(11,'test-product-7','productLocalization',13,2);
/*!40000 ALTER TABLE `alias_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `api_token`
--

DROP TABLE IF EXISTS `api_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `api_token` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `issuerId` int(11) NOT NULL,
  `issuedAt` datetime NOT NULL,
  `expiresAt` datetime DEFAULT NULL,
  `revokedAt` datetime DEFAULT NULL,
  `scope` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  KEY `issuerId` (`issuerId`),
  CONSTRAINT `api_token_ibfk_1` FOREIGN KEY (`issuerId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `api_token`
--

LOCK TABLES `api_token` WRITE;
/*!40000 ALTER TABLE `api_token` DISABLE KEYS */;
INSERT INTO `api_token` VALUES (1,'SA-AT-37FMp1N7YNNONQl65CmRrG0ScwwZzhcIeKasxifjeO','test',28,'2024-07-04 15:21:15',NULL,NULL,'all'),(2,'SA-AT-f07u75uX3awhNoGKGeHJsM9tKzsa3iy3onqBOqzpSv','!!!',28,'2024-07-08 18:42:39',NULL,'2024-07-08 18:43:18','all');
/*!40000 ALTER TABLE `api_token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `author`
--

DROP TABLE IF EXISTS `author`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `author` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `author`
--

LOCK TABLES `author` WRITE;
/*!40000 ALTER TABLE `author` DISABLE KEYS */;
INSERT INTO `author` VALUES (1,'Tomáš Fuk','{\"base\":[{\"mainImage\":\"103\"}]}'),(2,'Pepík Nepovím',NULL);
/*!40000 ALTER TABLE `author` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `author_localization`
--

DROP TABLE IF EXISTS `author_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `author_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `mutationId_authorId` (`mutationId`,`authorId`),
  KEY `FK_author_mutation` (`mutationId`) USING BTREE,
  KEY `FK_author_localization_author` (`authorId`) USING BTREE,
  CONSTRAINT `FK_author_localization_author` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_author_localization_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `author_localization`
--

LOCK TABLES `author_localization` WRITE;
/*!40000 ALTER TABLE `author_localization` DISABLE KEYS */;
INSERT INTO `author_localization` VALUES (1,1,1,'Tomáš Fuk','Tomáš Fuk','Tomáš Fuk','','',1,0,0,0,NULL,NULL,11,'2024-07-24 13:53:09','{\"base\":[{\"position\":\"CEO\",\"content\":\"<p>Popisek osoby Tomáš Fuk.</p>\"}]}','{}'),(2,1,2,'Pepík Nepovím','Pepík Nepovím','Pepík Nepovím','','',1,0,0,0,NULL,NULL,NULL,NULL,'{}','{}');
/*!40000 ALTER TABLE `author_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog`
--

DROP TABLE IF EXISTS `blog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog`
--

LOCK TABLES `blog` WRITE;
/*!40000 ALTER TABLE `blog` DISABLE KEYS */;
INSERT INTO `blog` VALUES (3,'Testovací článek','{}');
/*!40000 ALTER TABLE `blog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_localization`
--

DROP TABLE IF EXISTS `blog_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `blogId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT '0',
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`blogId`) USING BTREE,
  CONSTRAINT `FK_blog_localization_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_localization`
--

LOCK TABLES `blog_localization` WRITE;
/*!40000 ALTER TABLE `blog_localization` DISABLE KEYS */;
INSERT INTO `blog_localization` VALUES (12,1,3,'Testovací článek','Testovací článek','Testovací článek','','',0,1,0,0,0,'2024-04-09 21:20:00','2124-04-09 21:20:00',4,'2025-04-09 17:06:10','{\"base\":[{\"mainImage\":\"100\"}]}',NULL,'{\"content____0FB39uqbf3wOhUtRrjsbQ\":[{\"content\":\"<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Soluta asperiores debitis facere perferendis repellendus ratione, error nihil sit deleniti in sequi non harum ea earum. Fugit quia aperiam illo consectetur minus inventore provident quasi tenetur praesentium id iste saepe, ab asperiores atque quidem earum, magnam molestias nulla, blanditiis cum? Minima eum molestias exercitationem. At eligendi animi voluptatem officiis eveniet. Voluptates, aliquam. Molestias quos alias, dicta iusto quaerat nulla earum perferendis dolorem labore, illo nemo recusandae non. Voluptates quaerat molestias eveniet commodi, fugiat, aut dolore velit iusto recusandae ratione expedita itaque nam placeat incidunt vel dolorum, cupiditate assumenda suscipit ab fugit!</p>\\n<div>\\n<div><span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Soluta asperiores debitis facere perferendis repellendus ratione, error nihil sit deleniti in sequi non harum ea earum. Fugit quia aperiam illo consectetur minus inventore provident quasi tenetur praesentium id iste saepe, ab asperiores atque quidem earum, magnam molestias nulla, blanditiis cum? Minima eum molestias exercitationem. At eligendi animi voluptatem officiis eveniet. Voluptates, aliquam. Molestias quos alias, dicta iusto quaerat nulla earum perferendis dolorem labore, illo nemo recusandae non. Voluptates quaerat molestias eveniet commodi, fugiat, aut dolore velit iusto recusandae ratione expedita itaque nam placeat incidunt vel dolorum, cupiditate assumenda suscipit ab fugit!</span></div>\\n</div>\"}],\"gallery____Z_HsdxY3bYM2Gkj57BUsr\":[{\"images\":[\"102\",\"101\",\"100\",\"104\",\"105\"]}],\"gallery____v_JestlUHzQH7gWrYNg0N\":[{\"images\":[\"57\",\"23\"]}],\"video____1hsWpIfvsxF21ZZELYNlY\":[{\"video\":[{\"toggle\":\"file\",\"file\":[{\"file\":{\"id\":\"129\",\"name\":\"Contact us | LEVIT 2025-04-09 12-47-36.png\"}}]}]}]}',72);
/*!40000 ALTER TABLE `blog_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_localization_tree`
--

DROP TABLE IF EXISTS `blog_localization_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_localization_tree` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `blogLocalizationId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`Id`),
  KEY `FK_blog_localization_tree_blog_localization` (`blogLocalizationId`),
  KEY `FK_blog_localization_tree_tree` (`treeId`),
  CONSTRAINT `FK_blog_localization_tree_blog_localization` FOREIGN KEY (`blogLocalizationId`) REFERENCES `blog_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_localization_tree_tree` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_localization_tree`
--

LOCK TABLES `blog_localization_tree` WRITE;
/*!40000 ALTER TABLE `blog_localization_tree` DISABLE KEYS */;
INSERT INTO `blog_localization_tree` VALUES (4,12,446,0),(5,12,447,1);
/*!40000 ALTER TABLE `blog_localization_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_tag`
--

DROP TABLE IF EXISTS `blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_tag`
--

LOCK TABLES `blog_tag` WRITE;
/*!40000 ALTER TABLE `blog_tag` DISABLE KEYS */;
INSERT INTO `blog_tag` VALUES (1,'Štítek 1','{}'),(2,'Štítek 2','{}');
/*!40000 ALTER TABLE `blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_tag_localization`
--

DROP TABLE IF EXISTS `blog_tag_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_tag_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `blogTagId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL DEFAULT '0',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_tag_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_tag_localization_blog_tag` (`blogTagId`),
  CONSTRAINT `FK_blog_tag_localization_blog_tag` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_tag_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_tag_localization`
--

LOCK TABLES `blog_tag_localization` WRITE;
/*!40000 ALTER TABLE `blog_tag_localization` DISABLE KEYS */;
INSERT INTO `blog_tag_localization` VALUES (1,1,1,'Štítek 1',10,'Štítek 1','super super tag','','',1,0,0,0,11,'2024-07-24 10:53:05','{}','{}'),(2,2,1,'Štítek 2',0,'Štítek 2','Štítek 2','','',1,0,0,0,11,'2024-07-24 10:53:19','{}','{}');
/*!40000 ALTER TABLE `blog_tag_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_author`
--

DROP TABLE IF EXISTS `blog_x_author`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_author` (
  `blogId` int(11) NOT NULL,
  `authorId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`authorId`) USING BTREE,
  KEY `FK_blog_x_author_blog` (`blogId`) USING BTREE,
  KEY `FK_blog_x_author_author` (`authorId`) USING BTREE,
  CONSTRAINT `FK_blog_x_author_author` FOREIGN KEY (`authorId`) REFERENCES `author` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_author_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_author`
--

LOCK TABLES `blog_x_author` WRITE;
/*!40000 ALTER TABLE `blog_x_author` DISABLE KEYS */;
INSERT INTO `blog_x_author` VALUES (3,1),(3,2);
/*!40000 ALTER TABLE `blog_x_author` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_blog`
--

DROP TABLE IF EXISTS `blog_x_blog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_blog` (
  `blogId` int(11) NOT NULL,
  `attachedBlogId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`attachedBlogId`) USING BTREE,
  KEY `FK_blog_x_blog_blog` (`blogId`) USING BTREE,
  KEY `FK_blog_x_blog_blog_2` (`attachedBlogId`) USING BTREE,
  CONSTRAINT `FK_blog_x_blog_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_blog_blog_2` FOREIGN KEY (`attachedBlogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_blog`
--

LOCK TABLES `blog_x_blog` WRITE;
/*!40000 ALTER TABLE `blog_x_blog` DISABLE KEYS */;
/*!40000 ALTER TABLE `blog_x_blog` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blog_x_blog_tag`
--

DROP TABLE IF EXISTS `blog_x_blog_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_x_blog_tag` (
  `blogId` int(11) NOT NULL,
  `blogTagId` int(11) NOT NULL,
  PRIMARY KEY (`blogId`,`blogTagId`) USING BTREE,
  KEY `FK__blog` (`blogId`) USING BTREE,
  KEY `FK__blog_tag` (`blogTagId`) USING BTREE,
  CONSTRAINT `FK_blog_x_blog_tag_blog` FOREIGN KEY (`blogId`) REFERENCES `blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_blog_x_blog_tag_blog_tag` FOREIGN KEY (`blogTagId`) REFERENCES `blog_tag` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blog_x_blog_tag`
--

LOCK TABLES `blog_x_blog_tag` WRITE;
/*!40000 ALTER TABLE `blog_x_blog_tag` DISABLE KEYS */;
INSERT INTO `blog_x_blog_tag` VALUES (3,1);
/*!40000 ALTER TABLE `blog_x_blog_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `card_payment`
--

DROP TABLE IF EXISTS `card_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `card_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cardPaymentInformationId` int(11) NOT NULL,
  `paymentGatewayUniqueIdentifier` varchar(255) NOT NULL,
  `externalId` varchar(255) NOT NULL,
  `externalUrl` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `paymentGatewayUniqueIdentifier` (`paymentGatewayUniqueIdentifier`,`externalId`),
  KEY `cardPaymentInformationId` (`cardPaymentInformationId`),
  CONSTRAINT `card_payment_ibfk_1` FOREIGN KEY (`cardPaymentInformationId`) REFERENCES `order_payment_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `card_payment`
--

LOCK TABLES `card_payment` WRITE;
/*!40000 ALTER TABLE `card_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `card_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `card_payment_status_change`
--

DROP TABLE IF EXISTS `card_payment_status_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `card_payment_status_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentId` int(11) NOT NULL,
  `changedAt` datetime NOT NULL,
  `from` varchar(255) DEFAULT NULL,
  `to` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentId` (`paymentId`),
  CONSTRAINT `card_payment_status_change_ibfk_1` FOREIGN KEY (`paymentId`) REFERENCES `card_payment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `card_payment_status_change`
--

LOCK TABLES `card_payment_status_change` WRITE;
/*!40000 ALTER TABLE `card_payment_status_change` DISABLE KEYS */;
/*!40000 ALTER TABLE `card_payment_status_change` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method`
--

DROP TABLE IF EXISTS `delivery_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` varchar(50) DEFAULT NULL,
  `deliveryMethodUniqueIdentifier` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `desc` varchar(255) NOT NULL,
  `tooltip` varchar(255) DEFAULT NULL,
  `sort` int(11) NOT NULL,
  `public` tinyint(1) NOT NULL,
  `isRecommended` tinyint(1) NOT NULL,
  `deliveryDayFrom` int(11) NOT NULL,
  `deliveryDayTo` int(11) DEFAULT NULL,
  `deliveryHourByStock` text NOT NULL,
  `mutationId` int(11) NOT NULL,
  `vats` text NOT NULL,
  `customFieldsJson` longtext,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `delivery_method_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method`
--

LOCK TABLES `delivery_method` WRITE;
/*!40000 ALTER TABLE `delivery_method` DISABLE KEYS */;
INSERT INTO `delivery_method` VALUES (1,NULL,'PPL','PPL','popis','obsah',0,1,0,0,NULL,'{\"shop\":null,\"supplier_store\":null}',1,'{\"61\":\"standard\"}','{\"deliveryPayment\":[{\"icon\":[\"106\"]}]}');
/*!40000 ALTER TABLE `delivery_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_price`
--

DROP TABLE IF EXISTS `delivery_method_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deliveryMethodId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) NOT NULL,
  `freeFrom` decimal(18,4) DEFAULT NULL,
  `maxWeight` int(11) DEFAULT NULL,
  `maxCodPrice` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `delivery_method_price_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_price_ibfk_2` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `delivery_method_price_ibfk_3` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_price`
--

LOCK TABLES `delivery_method_price` WRITE;
/*!40000 ALTER TABLE `delivery_method_price` DISABLE KEYS */;
INSERT INTO `delivery_method_price` VALUES (1,1,1,61,0.0000,'CZK',NULL,NULL,NULL);
/*!40000 ALTER TABLE `delivery_method_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_x_payment_method`
--

DROP TABLE IF EXISTS `delivery_method_x_payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_x_payment_method` (
  `deliveryMethodId` int(11) NOT NULL,
  `paymentMethodId` int(11) NOT NULL,
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `paymentMethodId` (`paymentMethodId`),
  CONSTRAINT `delivery_method_x_payment_method_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_x_payment_method_ibfk_2` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_x_payment_method`
--

LOCK TABLES `delivery_method_x_payment_method` WRITE;
/*!40000 ALTER TABLE `delivery_method_x_payment_method` DISABLE KEYS */;
INSERT INTO `delivery_method_x_payment_method` VALUES (1,1);
/*!40000 ALTER TABLE `delivery_method_x_payment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_method_x_state`
--

DROP TABLE IF EXISTS `delivery_method_x_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_method_x_state` (
  `deliveryMethodId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`deliveryMethodId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `delivery_method_x_state_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `delivery_method_x_state_ibfk_2` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_method_x_state`
--

LOCK TABLES `delivery_method_x_state` WRITE;
/*!40000 ALTER TABLE `delivery_method_x_state` DISABLE KEYS */;
INSERT INTO `delivery_method_x_state` VALUES (1,61);
/*!40000 ALTER TABLE `delivery_method_x_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `elastic_search_index`
--

DROP TABLE IF EXISTS `elastic_search_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `elastic_search_index` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `createdTime` datetime DEFAULT NULL,
  `startTime` datetime DEFAULT NULL,
  `finishTime` datetime DEFAULT NULL,
  `recreate` tinyint(1) DEFAULT '0',
  `status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `active` int(11) DEFAULT '0',
  `errorCount` int(11) NOT NULL DEFAULT '0',
  `errorDetail` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mutationId` (`mutationId`) USING BTREE,
  CONSTRAINT `elastic_search_index_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `elastic_search_index`
--

LOCK TABLES `elastic_search_index` WRITE;
/*!40000 ALTER TABLE `elastic_search_index` DISABLE KEYS */;
INSERT INTO `elastic_search_index` VALUES (28,1,'product','superadmin_stage_product_cs','2025-04-04 14:51:55','2025-04-04 14:51:57','2025-04-04 14:52:05',0,NULL,1,0,'{}'),(29,1,'common','superadmin_stage_common_cs','2025-04-04 14:52:05','2025-04-04 14:52:06','2025-04-04 14:52:09',0,NULL,1,0,'{}'),(30,2,'product','superadmin_stage_product_en','2025-04-04 14:52:08','2025-04-04 14:52:10','2025-04-04 14:52:13',0,NULL,1,0,'{}'),(31,2,'common','superadmin_stage_common_en','2025-04-04 14:52:13','2025-04-04 14:52:14','2025-04-04 14:52:14',0,NULL,1,0,'{}'),(32,1,'all','superadmin_stage_all_cs','2025-04-04 14:52:14','2025-04-09 17:22:34','2025-04-09 17:22:34',0,NULL,1,0,'{}'),(33,1,'product','superadmin_stage_product_cs','2025-04-04 14:56:01',NULL,NULL,0,NULL,0,0,'{}');
/*!40000 ALTER TABLE `elastic_search_index` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template`
--

DROP TABLE IF EXISTS `email_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isDeveloper` tinyint(1) NOT NULL DEFAULT '0',
  `isHidden` tinyint(1) NOT NULL DEFAULT '0',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `email_template_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template`
--

LOCK TABLES `email_template` WRITE;
/*!40000 ALTER TABLE `email_template` DISABLE KEYS */;
INSERT INTO `email_template` VALUES (6,1,'lostPassword',0,0,'Zap. heslo','Změna hesla','<h2>Změna hesla</h2>\n<h4>Změňte svoje heslo kliknutím na odkaz:</h4>\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\"><a href=\"[DATA-link]\">[DATA-link]</a></span></p>\n<p>________<strong></strong></p>\n<p>Úspěšný den přeje</p>\n<p>tým superadmin</p>'),(13,1,'sendOnEmail',1,0,'Odeslat na email',NULL,'<h2>Zpráva z webu</h2>\r\n<p><strong>Jméno: </strong> [DATA-name]<br /> <strong>E-mail: </strong> [DATA-email]</p>\r\n<p><strong>text: </strong><br /> [DATA-text]</p>\r\n<p>________</p>\r\n<p>Úspěšný den přeje,<br />tým superadmin</p>'),(15,1,'newsletterInfo',0,0,'Odběr novinek',NULL,'<h2>Přihlášení k odběru novinek od superadmin.cz</h2>\r\n<p>Dobrý den,</p>\r\n<p>moc nás těší, že s námi chcete zůstat ve spojení. Jako první budete dostávat informace o novinkách, akcích a slevách, zkrátka o všem, co se děje na superadmin.cz</p>\r\n<p>Brzy u čerstvých novinek!<br />Tým superadmin</p>'),(45,1,'passwordChanged',0,0,'Heslo bylo změněno','','<h2>Vaše heslo bylo změněno</h2>\r\n<h4><strong style=\"color: #848587; font-family: GTAmerica, sans-serif;\"></strong><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Heslo k e-mailu [DATA-email] bylo změněno.</span></h4>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pokud jste změnu provedli vy, tak tento e-mail <span>můžete</span> ignorovat.</span></p>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pokud ne, tak si zkontrolujte zabezpečení svého účtu a případně si <a href=\"[DATA-link]\">nechte vygenerovat nové heslo</a>.</span></p>\r\n<p>________</p>\r\n<p>Pěkný den přeje<br />tým superadmin</p>'),(50,1,'softBlock',0,0,'Prodloužení platnosti účtu',NULL,'<h2>Prodloužení platnosti účtu</h2>\r\n<h4><strong style=\"color: #848587; font-family: GTAmerica, sans-serif;\"></strong><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Pro prodloužení platnosti účtu stačí kliknout na tento odkaz: <a href=\"[DATA-link]\" target=\"_blank\">[DATA-link]</a></span></h4>\r\n<p><span style=\"color: #848587; font-family: GTAmerica, sans-serif;\">Na prodloužení platnosti máte 7 dní od obdržení tohoto e-mailu.</span></p>\r\n<p>V případě, že se vyskytnou problémy nebo uplynula 7 denní lhůta nás kontaktujte na:</p>\r\n<p>e-mail: <a href=\"mailto:<EMAIL>\"><EMAIL></a><br /><span>tel.:<span> </span></span><a href=\"tel:+420286880161\">+420 286 880 161</a></p>\r\n<p>________</p>\r\n<p>Pěkný den přeje<br />tým superadmin</p>'),(56,1,'contact',0,0,'contact','contact1','<h2><strong>Zpráva z konktaktního formuláře</strong></h2>\r\n<p> </p>\r\n<p><span><strong>Jméno: </strong>[DATA-name]</span></p>\r\n<p><strong>E-mail: </strong>[DATA-email]</p>\r\n<p><strong>Telefon: </strong>[DATA-phone]</p>\r\n<p><strong>Zpráva:</strong><br />[DATA-text]</p>\r\n<p> </p>\r\n<p><span> </span></p>');
/*!40000 ALTER TABLE `email_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template_file`
--

DROP TABLE IF EXISTS `email_template_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_template_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fileId` int(11) NOT NULL COMMENT 'idfile',
  `emailTemplateId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fileId_emailTemplateId` (`fileId`,`emailTemplateId`),
  KEY `emailTemplateId` (`emailTemplateId`),
  KEY `fileId` (`fileId`),
  CONSTRAINT `email_template_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `email_template_file_ibfk_2` FOREIGN KEY (`emailTemplateId`) REFERENCES `email_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template_file`
--

LOCK TABLES `email_template_file` WRITE;
/*!40000 ALTER TABLE `email_template_file` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_template_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `file`
--

DROP TABLE IF EXISTS `file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  `isDeleted` tinyint(1) DEFAULT '0',
  `createdTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `deletedTime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=133 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `file`
--

LOCK TABLES `file` WRITE;
/*!40000 ALTER TABLE `file` DISABLE KEYS */;
INSERT INTO `file` VALUES (1,'superkoderi@oldrichhrb_1200px-01.jpg','1-superkoderi-oldrichhrb-1200px-01.jpg',813255,1,'2025-04-04 17:37:47','2025-04-09 17:01:46'),(2,'calibra_cn_8578680.pdf','2-calibra-cn-8578680.pdf',84226,0,'2025-04-04 17:37:47',NULL),(3,'395046CZK120201BRM_20201231_1 (1).pdf','3-395046czk120201brm-20201231-1-1.pdf',1250815,0,'2025-04-04 17:37:47',NULL),(4,'annot-products.jpg','4-annot-products.jpeg',76762,0,'2025-04-04 17:37:47',NULL),(5,'2101010939_eshop_snauwaert_cz (2).pdf','5-2101010939-eshop-snauwaert-cz-2.pdf',67631,0,'2025-04-04 17:37:47',NULL),(6,'2.png','6-2.png',365705,0,'2025-04-04 17:37:47',NULL),(7,'[SkT]Rytiri_spravedlnosti___Retf&#230;rdighedens_ryttere_(2020)[WebRip][1080p]_=_CSFD_83%.torrent','7-skt-rytiri-spravedlnosti-retf-230-rdighedens-ryttere-2020-webrip-1080p-csfd-83.torrent',26101,0,'2025-04-04 17:37:47',NULL),(8,'Ryzlink_Rynsky.png','8-ryzlink-rynsky.png',4055271,0,'2025-04-04 17:37:47',NULL),(9,'image (10).png','9-image-10.png',9141,0,'2025-04-04 17:37:47',NULL),(10,'brtnik 01-2021.pdf','10-brtnik-01-2021.pdf',63704,0,'2025-04-04 17:37:47',NULL),(11,'Eu0gZ5_XMAABywS.jfif','11-eu0gz5-xmaabyws.jpeg',63479,0,'2025-04-04 17:37:47',NULL),(12,'image (11).png','12-image-11.png',20042,0,'2025-04-04 17:37:47',NULL),(13,'ats-pumpa---navod---1-2-3-eh-te.pdf','13-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,1,'2025-04-04 17:37:47','2025-04-09 17:01:49'),(14,'ats-pumpa---navod---1-2-3-eh-te.pdf','14-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,1,'2025-04-04 17:37:47','2025-04-09 17:01:51'),(15,'ats-pumpa---navod---1-2-3-eh-te.pdf','15-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(16,'ats-pumpa---navod---1-2-3-eh-te.pdf','16-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(17,'ats-pumpa---navod---1-2-3-eh-te.pdf','17-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(18,'ats-pumpa---navod---1-2-3-eh-te.pdf','18-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(19,'ats-pumpa---navod---1-2-3-eh-te.pdf','19-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(20,'ats-pumpa---navod---1-2-3-eh-te.pdf','20-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(21,'ats-pumpa---navod---1-2-3-eh-te.pdf','21-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(22,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','22-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-04 17:37:47',NULL),(23,'ats-pumpa---navod---1-2-3-eh-te.pdf','23-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(24,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','24-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-04 17:37:47',NULL),(25,'ats-pumpa---navod---1-2-3-eh-te.pdf','25-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(26,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','26-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-04 17:37:47',NULL),(27,'ats-pumpa---navod---1-2-3-eh-te.pdf','27-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(28,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','28-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-04 17:37:47',NULL),(29,'ats-pumpa---navod---1-2-3-eh-te.pdf','29-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(30,'D2C MPX API Integration Specification (MASTER) -- 3.pdf','30-d2c-mpx-api-integration-specification-master-3.pdf',1702568,0,'2025-04-04 17:37:47',NULL),(31,'ats-pumpa---navod---1-2-3-eh-te.pdf','31-ats-pumpa-navod-1-2-3-eh-te.pdf',4104733,0,'2025-04-04 17:37:47',NULL),(32,'payment-detail-T568208798 (13).pdf','32-payment-detail-t568208798-13.pdf',3081,0,'2025-04-04 17:37:47',NULL),(33,'payment-detail-T568208798 (14).pdf','33-payment-detail-t568208798-14.pdf',3082,0,'2025-04-04 17:37:47',NULL),(34,'payment-detail-T568208798 (11).pdf','34-payment-detail-t568208798-11.pdf',2485,0,'2025-04-04 17:37:47',NULL),(35,'rotao60-8878719.pdf','35-rotao60-8878719.pdf',187781,0,'2025-04-04 17:37:47',NULL),(36,'toby-elliott-m3SrHEMrmbQ-unsplash.jpg','36-toby-elliott-m3srhemrmbq-unsplash.jpeg',2864450,0,'2025-04-04 17:37:47',NULL),(37,'girl-with-red-hat-sLcVe47YUJI-unsplash.jpg','37-girl-with-red-hat-slcve47yuji-unsplash.jpeg',5611172,0,'2025-04-04 17:37:47',NULL),(38,'_kamenivo_cenik_2021.pdf','38-kamenivo-cenik-2021.pdf',4785772,0,'2025-04-04 17:37:47',NULL),(39,'chyby,kterekaziwebvitals4.036.jpeg','39-chyby-kterekaziwebvitals4.036.jpeg',387417,0,'2025-04-04 17:37:47',NULL),(40,'payment-detail-T644971264.pdf','40-payment-detail-t644971264.pdf',3045,0,'2025-04-04 17:37:47',NULL),(41,'payment-detail-T738633803.pdf','41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(42,'41-payment-detail-t738633803.pdf','42-41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(43,'41-payment-detail-t738633803.pdf','43-41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(44,'payment-detail-T644971264.pdf','44-payment-detail-t644971264.pdf',3045,0,'2025-04-04 17:37:47',NULL),(45,'payment-detail-T738633803.pdf','45-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(46,'payment-detail-T644971264.pdf','46-payment-detail-t644971264.pdf',3045,0,'2025-04-04 17:37:47',NULL),(47,'41-payment-detail-t738633803.pdf','47-41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(48,'41-payment-detail-t738633803.pdf','48-41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(49,'payment-detail-T644971264.pdf','49-payment-detail-t644971264.pdf',3045,0,'2025-04-04 17:37:47',NULL),(50,'41-payment-detail-t738633803.pdf','50-41-payment-detail-t738633803.pdf',3034,0,'2025-04-04 17:37:47',NULL),(51,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(52,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(53,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(54,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(55,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(56,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(57,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg','57-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',379502,0,'2025-04-04 17:37:47',NULL),(58,'sdvsfgnehmhadsfds.2519740793.1623055896.jpg','58-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',379502,0,'2025-04-04 17:37:47',NULL),(59,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpg','59-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpeg',380305,0,'2025-04-04 17:37:47',NULL),(60,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpg','60-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpeg',410929,0,'2025-04-04 17:37:47',NULL),(61,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(62,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(63,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(64,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(65,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpg',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(66,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf',NULL,NULL,0,'2025-04-04 17:37:47',NULL),(67,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf','67-2.4936-hammerli-hunter-force-900-combo-bullet.pdf',382049,0,'2025-04-04 17:37:47',NULL),(68,'payment-detail-T438632305 (2).pdf','68-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(69,'203821655_10226477269460316_3028584662214159215_n.jpg','69-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(70,'203956436_10226477268660296_1686485296554203402_n.jpg','70-203956436-10226477268660296-1686485296554203402-n.jpeg',100205,0,'2025-04-04 17:37:47',NULL),(71,'203821655_10226477269460316_3028584662214159215_n.jpg','71-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(72,'203821655_10226477269460316_3028584662214159215_n.jpg','72-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(73,'203821655_10226477269460316_3028584662214159215_n.jpg','73-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(74,'203821655_10226477269460316_3028584662214159215_n.jpg','74-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(75,'203821655_10226477269460316_3028584662214159215_n.jpg','75-203821655-10226477269460316-3028584662214159215-n.jpeg',80903,0,'2025-04-04 17:37:47',NULL),(76,'4287454258.jpg','76-4287454258.jpeg',491793,0,'2025-04-04 17:37:47',NULL),(77,'registration_side_img (3).jpg','77-registration-side-img-3.jpeg',4632,0,'2025-04-04 17:37:47',NULL),(78,'payment-detail-T438632305 (2).pdf','78-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(79,'payment-detail-T438632305 (2).pdf','79-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(80,'payment-detail-T438632305 (2).pdf','80-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(81,'payment-detail-T438632305 (2).pdf','81-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(82,'lake-4841884_1920.jpg','82-lake-4841884-1920.jpeg',312203,0,'2025-04-04 17:37:47',NULL),(83,'payment-detail-T438632305 (2).pdf','83-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(84,'lake-4841884_1920.jpg','84-lake-4841884-1920.jpeg',312203,0,'2025-04-04 17:37:47',NULL),(85,'payment-detail-T438632305 (2).pdf','85-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(86,'payment-detail-T438632305 (2).pdf','86-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(87,'payment-detail-T438632305 (2).pdf','87-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(88,'payment-detail-T438632305 (2).pdf','88-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(89,'payment-detail-T438632305 (2).pdf','89-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(90,'payment-detail-T438632305 (2).pdf','90-payment-detail-t438632305-2.pdf',3030,0,'2025-04-04 17:37:47',NULL),(91,'berlin-4679964_1920.jpg','91-berlin-4679964-1920.jpeg',615867,0,'2025-04-04 17:37:47',NULL),(92,'m4-177.jpg','92-m4-177.jpeg',333275,0,'2025-04-04 17:37:47',NULL),(93,'m4-177.jpg','93-m4-177.jpeg',333275,0,'2025-04-04 17:37:47',NULL),(94,'m4-177.jpg','94-m4-177.jpeg',333275,0,'2025-04-04 17:37:47',NULL),(95,'123388959_10221854227656351_5532550917549873870_o.jpg','95-123388959-10221854227656351-5532550917549873870-o.jpeg',384737,0,'2025-04-04 17:37:47',NULL),(96,'123388959_10221854227656351_5532550917549873870_o.jpg','96-123388959-10221854227656351-5532550917549873870-o.jpeg',384737,0,'2025-04-04 17:37:47',NULL),(97,'03-w1920.jpg','97-03-w1920.jpeg',3033314,0,'2025-04-04 17:37:47',NULL),(98,'A4.pdf','98-a4.pdf',15730,1,'2025-04-04 17:37:47','2025-04-09 17:01:37'),(99,'bydleni-mobil.jpg','99-bydleni-mobil.jpeg',97257,0,'2025-04-04 17:37:47',NULL),(100,'A4.pdf','100-a4.pdf',15730,0,'2025-04-04 17:37:47',NULL),(101,'A4.pdf','101-a4.pdf',15730,0,'2025-04-04 17:37:47',NULL),(102,'clinic-material-4.pdf','102-clinic-material-4.pdf',1061611,0,'2025-04-04 17:37:47',NULL),(103,'laboratorni-zprava.pdf','103-laboratorni-zprava.pdf',2079698,0,'2025-04-04 17:37:47',NULL),(104,'laboratorni-zprava.pdf','104-laboratorni-zprava.pdf',2079698,0,'2025-04-04 17:37:47',NULL),(105,'laboratorni-zprava.pdf','105-laboratorni-zprava.pdf',2079698,0,'2025-04-04 17:37:47',NULL),(106,'4440931-hd_1920_1080_25fps.mp4','106-4440931-hd-1920-1080-25fps.mp4',5968483,0,'2025-04-04 17:37:47',NULL),(107,'42A385AE-282D-4864-A124-2F5B13224F88_1_105_c.jpeg','107-42a385ae-282d-4864-a124-2f5b13224f88-1-105-c.jpeg',424445,0,'2025-04-04 17:37:47',NULL),(108,'42A385AE-282D-4864-A124-2F5B13224F88_1_105_c.jpeg','108-42a385ae-282d-4864-a124-2f5b13224f88-1-105-c.jpeg',424445,0,'2025-04-04 17:37:47',NULL),(109,'42A385AE-282D-4864-A124-2F5B13224F88_1_105_c.jpeg','109-42a385ae-282d-4864-a124-2f5b13224f88-1-105-c.jpeg',424445,0,'2025-04-04 17:37:47',NULL),(112,'137-20230515-111321.jpg 2023-05-16 13-32-27.png','112-137-20230515-111321.jpg-2023-05-16-13-32-27.png',237768,1,'2025-04-04 17:37:47','2025-04-09 14:39:17'),(113,'9CE3467F-77CF-4277-8F23-EDA1BB978054_1_201_a.jpeg','113-9ce3467f-77cf-4277-8f23-eda1bb978054-1-201-a.jpeg',335484,1,'2025-04-04 17:37:47','2025-04-04 18:20:15'),(115,'42A385AE-282D-4864-A124-2F5B13224F88_1_105_c.jpeg','115-42a385ae-282d-4864-a124-2f5b13224f88-1-105-c.jpeg',424445,1,'2025-04-04 17:37:47','2025-04-04 18:20:06'),(116,'42A385AE-282D-4864-A124-2F5B13224F88_1_105_c.jpeg','_deleted__deleted_116-42a385ae-282d-4864-a124-2f5b13224f88-1-105-c.jpeg',424445,1,'2025-04-04 17:37:47','2025-04-04 18:19:57'),(121,'ChatGPT Image 4. 4. 2025 11_11_20.png','121-chatgpt-image-4.4.2025-11-11-20.png',2214266,1,'2025-04-04 17:37:47','2025-04-04 18:19:52'),(124,'402-1200x80.jpg','124-402-1200x80.jpeg',15287,0,'2025-04-04 17:37:47',NULL),(126,'2020-Scrum-Guide-US.pdf','_deleted_126-2020-scrum-guide-us.pdf',254353,1,NULL,'2025-04-09 14:30:18'),(127,'405-924x130.jpg','127-405-924x130.jpeg',26028,0,NULL,NULL),(128,'402-1200x80.jpg','128-402-1200x80.jpeg',15287,0,NULL,NULL),(129,'Contact us | LEVIT 2025-04-09 12-47-36.png','129-contact-us-levit-2025-04-09-12-47-36.png',135796,0,NULL,NULL),(130,'402-1200x80.jpg','130-402-1200x80.jpeg',15287,1,NULL,'2025-04-09 17:24:40'),(131,'2020-Scrum-Guide-US.pdf','_deleted_131-2020-scrum-guide-us.pdf',254353,1,NULL,'2025-04-10 10:30:21'),(132,'2020-Scrum-Guide-US.pdf','132-2020-scrum-guide-us.pdf',254353,0,NULL,NULL);
/*!40000 ALTER TABLE `file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `form_failed_submission`
--

DROP TABLE IF EXISTS `form_failed_submission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `form_failed_submission` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `form_type` varchar(64) NOT NULL,
  `discriminator` text NOT NULL,
  `ip_address` varbinary(16) NOT NULL,
  `attempted_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `form_type` (`form_type`,`attempted_at`),
  KEY `attempted_at` (`attempted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `form_failed_submission`
--

LOCK TABLES `form_failed_submission` WRITE;
/*!40000 ALTER TABLE `form_failed_submission` DISABLE KEYS */;
INSERT INTO `form_failed_submission` VALUES (1,'Admin:SignIn','<EMAIL>',_binary '\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0','2025-04-04 13:56:32'),(2,'Admin:SignIn','<EMAIL>',_binary 'U]a�','2025-04-09 14:14:50');
/*!40000 ALTER TABLE `form_failed_submission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `holiday`
--

DROP TABLE IF EXISTS `holiday`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `holiday` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) COLLATE utf8_czech_ci NOT NULL,
  `publicFrom` date NOT NULL,
  `publicTo` date NOT NULL,
  `created` datetime NOT NULL,
  `public` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `public_publicTo_publicFrom` (`public`,`publicTo`,`publicFrom`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `holiday`
--

LOCK TABLES `holiday` WRITE;
/*!40000 ALTER TABLE `holiday` DISABLE KEYS */;
INSERT INTO `holiday` VALUES (1,'','2024-09-05','2024-09-06','2024-09-05 12:25:28',0);
/*!40000 ALTER TABLE `holiday` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `image`
--

DROP TABLE IF EXISTS `image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `filename` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `libraryId` int(11) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `sourceImage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `md5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `alts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `timeOfChange` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cat_idx` (`libraryId`),
  CONSTRAINT `cat` FOREIGN KEY (`libraryId`) REFERENCES `library_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `image`
--

LOCK TABLES `image` WRITE;
/*!40000 ALTER TABLE `image` DISABLE KEYS */;
INSERT INTO `image` VALUES (13,'superkoderi@oldrichhrb_1200px-09','13-superkoderi-oldrichhrb-1200px-09.jpg',1,210,NULL,NULL,NULL,NULL),(14,'superkoderi@oldrichhrb_1200px-07','14-superkoderi-oldrichhrb-1200px-07.jpg',1,200,NULL,NULL,NULL,NULL),(23,'lake-4841884_1920','23-lake-4841884-1920.jpeg',1,150,NULL,NULL,NULL,NULL),(28,'cameron-venti-pqyvyqqa87s-unsplash','28-cameron-venti-pqyvyqqa87s-unsplash.jpeg',4,-28,NULL,NULL,NULL,NULL),(30,'caseen-kyle-registos-1ht1wnmfDiA-unsplash','30-caseen-kyle-registos-1ht1wnmfdia-unsplash.jpeg',4,-30,NULL,NULL,NULL,NULL),(32,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930','32-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55359.2519740793.1623055930.jpeg',5,-32,NULL,NULL,NULL,NULL),(33,'sdvsfgnehmhadsfds.2519740793.1623055896','33-sdvsfgnehmhadsfds.2519740793.1623055896.jpeg',5,-33,NULL,NULL,NULL,NULL),(34,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930','34-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55357.2519740793.1623055930.jpeg',5,-34,NULL,NULL,NULL,NULL),(35,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930','35-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55362.2519740793.1623055930.jpeg',5,-35,NULL,NULL,NULL,NULL),(36,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930','36-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55358.2519740793.1623055930.jpeg',5,-36,NULL,NULL,NULL,NULL),(37,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930','37-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55363.2519740793.1623055930.jpeg',5,-37,NULL,NULL,NULL,NULL),(38,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930','38-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55361.2519740793.1623055930.jpeg',5,-38,NULL,NULL,NULL,NULL),(39,'vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930','39-vzduchovka-hammerli-hunter-force-900-combo-4-5mm-55360.2519740793.1623055930.jpeg',5,-39,NULL,NULL,NULL,NULL),(40,'vzduchovka-kral-arms-n-08-camo-5-5mm.2519740793.1610146923','40-vzduchovka-kral-arms-n-08-camo-5-5mm.2519740793.1610146923.jpeg',5,-40,NULL,NULL,NULL,NULL),(41,'vzduchovka-kral-arms-n-08-camo-5-5mm-46295.2519740793.1610146923','41-vzduchovka-kral-arms-n-08-camo-5-5mm-46295.2519740793.1610146923.jpeg',5,-41,NULL,NULL,NULL,NULL),(42,'vzduchovka-kral-arms-n-08-camo-5-5mm-46296.2519740793.1610146923','42-vzduchovka-kral-arms-n-08-camo-5-5mm-46296.2519740793.1610146923.jpeg',5,-42,NULL,NULL,NULL,NULL),(43,'vzduchovka-kral-arms-n-08-camo-5-5mm-46299.2519740793.1610146923','43-vzduchovka-kral-arms-n-08-camo-5-5mm-46299.2519740793.1610146923.jpeg',5,-43,NULL,NULL,NULL,NULL),(44,'vzduchovka-kral-arms-n-08-camo-5-5mm-46293.2519740793.1610146923','44-vzduchovka-kral-arms-n-08-camo-5-5mm-46293.2519740793.1610146923.jpeg',5,-44,NULL,NULL,NULL,NULL),(45,'vzduchovka-kral-arms-n-08-camo-5-5mm-46297.2519740793.1610146923','45-vzduchovka-kral-arms-n-08-camo-5-5mm-46297.2519740793.1610146923.jpeg',5,-45,NULL,NULL,NULL,NULL),(46,'vzduchovka-kral-arms-n-08-camo-5-5mm-46294.2519740793.1610146923','46-vzduchovka-kral-arms-n-08-camo-5-5mm-46294.2519740793.1610146923.jpeg',5,-46,NULL,NULL,NULL,NULL),(47,'vzduchovka-kral-arms-n-08-camo-5-5mm-46298.2519740793.1610146923','47-vzduchovka-kral-arms-n-08-camo-5-5mm-46298.2519740793.1610146923.jpeg',5,-47,NULL,NULL,NULL,NULL),(52,'m4-177','52-m4-177.jpeg',5,-52,NULL,NULL,NULL,NULL),(53,'m4-177','53-m4-177.jpeg',5,-53,NULL,NULL,NULL,NULL),(57,'berlin-4679964_1920','57-berlin-4679964-1920.jpeg',1,80,NULL,NULL,NULL,NULL),(63,'feature-1','63-feature-1.jpeg',4,-63,NULL,NULL,NULL,NULL),(65,'07_03-pracovna','65-07-03-pracovna.jpeg',5,-65,NULL,NULL,NULL,NULL),(66,'07_03-pracovna','66-07-03-pracovna.jpeg',5,-66,NULL,NULL,NULL,NULL),(67,'07_03-pracovna','67-07-03-pracovna.jpeg',5,-67,NULL,NULL,NULL,NULL),(68,'07_03-pracovna','68-07-03-pracovna.jpeg',5,-68,NULL,NULL,NULL,NULL),(69,'07_03-pracovna','69-07-03-pracovna.jpeg',5,-69,NULL,NULL,NULL,NULL),(70,'07_03-pracovna','70-07-03-pracovna.jpeg',5,-70,NULL,NULL,NULL,NULL),(71,'07_03-pracovna','71-07-03-pracovna.jpeg',5,-71,NULL,NULL,NULL,NULL),(72,'07_03-pracovna','72-07-03-pracovna.jpeg',5,-72,NULL,NULL,NULL,NULL),(73,'07_03-pracovna','73-07-03-pracovna.jpeg',5,-73,NULL,NULL,NULL,NULL),(74,'07_03-pracovna','74-07-03-pracovna.jpeg',5,-74,NULL,NULL,NULL,NULL),(75,'07_03-pracovna','75-07-03-pracovna.jpeg',5,-75,NULL,NULL,NULL,NULL),(90,'137-20230515-111321.jpg 2023-05-16 13-32-27','90-137-20230515-111321.jpg-2023-05-16-13-32-27.png',2,-90,NULL,NULL,'{}','2024-06-25 15:22:14'),(100,'57277723_2263721630315358_4996225036122587136_n','100-57277723-2263721630315358-4996225036122587136-n.jpg',4,-100,NULL,NULL,'{}',NULL),(101,'57267652_2263721586982029_1189501496253743104_n','101-57267652-2263721586982029-1189501496253743104-n.jpg',4,-101,NULL,NULL,'{}',NULL),(102,'57154488_2263721566982031_6147697470303371264_n','102-57154488-2263721566982031-6147697470303371264-n.jpg',4,-102,NULL,NULL,'{}',NULL),(103,'superkoderi-1920px@oldrichhrb-4','103-superkoderi-1920px-oldrichhrb-4.jpg',4,-103,NULL,NULL,'{}',NULL),(104,'56869901_2263721450315376_1107564755880509440_n','104-56869901-2263721450315376-1107564755880509440-n.jpg',4,-104,NULL,NULL,'{}',NULL),(105,'56850390_2263721606982027_1673383008721174528_n','105-56850390-2263721606982027-1673383008721174528-n.jpg',4,-105,NULL,NULL,'{}',NULL),(106,'Logo_PPL','106-logo-ppl.jpg',28,-106,NULL,NULL,'{}',NULL),(107,'credit-card','107-credit-card.svg',28,-107,NULL,NULL,'{}',NULL),(108,'57154488_2263721566982031_6147697470303371264_n','108-57154488-2263721566982031-6147697470303371264-n.jpg',4,-108,NULL,NULL,'{}',NULL),(109,'57267652_2263721586982029_1189501496253743104_n','109-57267652-2263721586982029-1189501496253743104-n.jpg',4,-109,NULL,NULL,'{}',NULL),(110,'391593655_811856410945792_7878251948170040325_n','110-391593655-811856410945792-7878251948170040325-n.jpg',4,-110,NULL,NULL,'{}',NULL);
/*!40000 ALTER TABLE `image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `import_cache`
--

DROP TABLE IF EXISTS `import_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `import_cache` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(256) COLLATE utf8mb4_czech_ci NOT NULL,
  `data` mediumtext COLLATE utf8mb4_czech_ci NOT NULL,
  `status` enum('new','ready','imported','error','processing','skipped') COLLATE utf8mb4_czech_ci NOT NULL DEFAULT 'new',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `createdTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `importedTime` datetime DEFAULT NULL,
  `message` text COLLATE utf8mb4_czech_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `type_status_createdTime` (`type`,`status`,`createdTime`) USING BTREE,
  KEY `createdTime` (`createdTime`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `type_status_extId` (`type`,`status`,`extId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `import_cache`
--

LOCK TABLES `import_cache` WRITE;
/*!40000 ALTER TABLE `import_cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `import_cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `library_tree`
--

DROP TABLE IF EXISTS `library_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `library_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parentId` int(11) DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `last` tinyint(1) DEFAULT NULL,
  `created` int(11) NOT NULL,
  `createdTime` datetime NOT NULL,
  `edited` int(11) NOT NULL,
  `editedTime` datetime NOT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idParent` (`parentId`),
  CONSTRAINT `library_tree_ibfk_2` FOREIGN KEY (`parentId`) REFERENCES `library_tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `library_tree`
--

LOCK TABLES `library_tree` WRITE;
/*!40000 ALTER TABLE `library_tree` DISABLE KEYS */;
INSERT INTO `library_tree` VALUES (1,NULL,NULL,NULL,NULL,0,0,'2021-06-28 09:26:24',1,'2013-10-03 18:19:28','2021-06-28 09:26:24','2021-06-28 09:26:24','Knihovna obrázků','Knihovna obrázků','Knihovna obrázků',NULL),(2,1,1,'1|',NULL,1,0,'2021-06-28 09:26:24',1,'2013-10-03 18:19:28','2021-06-28 09:26:24','2021-06-28 09:26:24','Výchozí složka','Výchozí složka','Výchozí složka','default'),(4,1,1,'1|',1,1,1,'2021-06-28 09:26:24',1,'2021-06-28 09:26:24','2021-06-28 09:26:24','2100-01-01 00:00:00','Sample images','Enter a new name','Enter a new name',NULL),(5,1,1,'1|',1,0,9,'2021-07-08 13:44:29',9,'2021-07-08 13:44:29','2021-07-08 13:44:29','2100-01-01 00:00:00','Produkty','Enter a new name','Enter a new name',NULL),(28,1,1,'1|',1,1,1,'2024-03-18 13:36:53',1,'2024-03-18 13:36:53','2024-03-18 13:36:53','2100-01-01 00:00:00','Doprava a platba','Nová stránka','Nová stránka',NULL),(29,5,2,'1|5|',1,0,13,'2024-07-16 10:55:44',13,'2024-07-16 10:55:44','2024-07-16 10:55:44','2100-01-01 00:00:00','Test','Nová stránka','Nová stránka',NULL),(30,29,3,'1|5|29|',1,1,13,'2024-07-16 10:56:13',13,'2024-07-16 10:56:13','2024-07-16 10:56:13','2100-01-01 00:00:00','Test 2','Nová stránka','Nová stránka',NULL);
/*!40000 ALTER TABLE `library_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `log`
--

DROP TABLE IF EXISTS `log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `context` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `createdAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `log`
--

LOCK TABLES `log` WRITE;
/*!40000 ALTER TABLE `log` DISABLE KEYS */;
INSERT INTO `log` VALUES (1,'profileForm-form','{\"message\":\"profileForm-form\",\"context\":{\"formData\":{\"id\":\"1\",\"firstname\":\"Tomáš\",\"lastname\":\"Hejč\",\"phone\":\"*********\",\"street\":\"street\",\"city\":\"city\",\"zip\":\"90210\",\"state\":61,\"ic\":\"12345678\",\"dic\":\"dic\",\"company\":\"\",\"isNewsletter\":false,\"ca_isNew\":false,\"ca_firstname\":\"\",\"ca_lastname\":\"\",\"ca_company\":\"\",\"ca_phone\":\"\",\"ca_street\":\"\",\"ca_city\":\"\",\"ca_zip\":\"\",\"ca_state\":61},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2024-03-19T11:17:13.368431+01:00\",\"extra\":{\"url\":\"/profil\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin.kelly.l\",\"referrer\":\"https://superadmin.kelly.l/profil\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2024-03-19 11:17:13'),(2,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"sk\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aa\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"397\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:27:16.921103+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"superadmin-stage.www6.superkoderi.cz\",\"referrer\":\"https://superadmin-stage.www6.superkoderi.cz/kontakt\",\"unique_id\":\"Z-qKJIqovVo6hdnOf-QJfQABFwQ\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:27:17'),(3,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"sk\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"a\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:34:38.941467+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:34:39'),(4,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"sk\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"a\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:34:53.414214+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:34:53'),(5,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"sk\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"a\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:35:26.835200+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:35:27'),(6,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"sk\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"a\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:36:14.939756+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:36:15'),(7,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"aaa\",\"surname\":\"aaa\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"ddd\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"397\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:38:26.924752+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"superadmin-stage.www6.superkoderi.cz\",\"referrer\":\"https://superadmin-stage.www6.superkoderi.cz/kontakt\",\"unique_id\":\"Z-qMwt6tvB2Q7Wi_9yZorwAAaAE\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:38:27'),(8,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"a\",\"surname\":\"a\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"397\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:40:21.427735+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"superadmin-stage.www6.superkoderi.cz\",\"referrer\":\"https://superadmin-stage.www6.superkoderi.cz/kontakt\",\"unique_id\":\"Z-qNNRxH0_EHAIpjuM47NQAAFgI\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:40:21'),(9,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"Luděk\",\"surname\":\"Hradil\",\"email\":\"<EMAIL>\",\"phone\":\"+420776708422\",\"text\":\"aaaa\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"397\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:41:06.660799+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"superadmin-stage.www6.superkoderi.cz\",\"referrer\":\"https://superadmin-stage.www6.superkoderi.cz/kontakt\",\"unique_id\":\"Z-qNYhxH0_EHAIpjuM5A3AAAJCk\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:41:07'),(10,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"ddd\",\"surname\":\"ddd\",\"email\":\"<EMAIL>\",\"phone\":\"+420776708422\",\"text\":\"dddd\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"397\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:46:46.544516+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"superadmin-stage.www6.superkoderi.cz\",\"referrer\":\"https://superadmin-stage.www6.superkoderi.cz/kontakt\",\"unique_id\":\"Z-qOtk8Bgx4oClOxfRejtgABDQk\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:46:47'),(11,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"aaaa\",\"surname\":\"aa\",\"email\":\"<EMAIL>\",\"phone\":\"+420776708422\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:50:48.235995+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:50:48'),(12,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"aaaa\",\"surname\":\"aa\",\"email\":\"<EMAIL>\",\"phone\":\"+420776708422\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T14:58:52.210074+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 14:58:52'),(13,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"ddd\",\"surname\":\"ddd\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"a5aqm9i0\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:09:39.977325+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:09:40'),(14,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"aaaa\",\"surname\":\"aaa\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:17:35.893664+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:17:36'),(15,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"ffff\",\"surname\":\"ffff\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"fff\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:17:45.514807+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:17:46'),(16,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"test\",\"surname\":\"Nováček\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:18:07.375365+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:18:07'),(17,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"aaa\",\"surname\":\"aaa\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:19:35.333002+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:19:35'),(18,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"Luděk\",\"surname\":\"Hradil\",\"email\":\"<EMAIL>\",\"phone\":\"+420776708422\",\"text\":\"aaaa\",\"agree\":true,\"antispamHash\":\"tuage6j7\",\"antispamNoJs\":\"812\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-03-31T15:19:49.385177+02:00\",\"extra\":{\"url\":\"/kontakt\",\"ip\":\"::1\",\"http_method\":\"POST\",\"server\":\"superadmin2019.ludek.l\",\"referrer\":\"https://superadmin2019.ludek.l/kontakt\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-03-31 15:19:49'),(19,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"ddd\",\"surname\":\"ddd\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaa\",\"agree\":true,\"antispamHash\":\"2jqbimjd\",\"antispamNoJs\":\"234\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-05-13T14:32:52.631028+02:00\",\"extra\":{\"url\":\"/\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"koh-i-noor-stage.www6.superkoderi.cz\",\"referrer\":\"https://koh-i-noor-stage.www6.superkoderi.cz/\",\"unique_id\":\"aCM79KRvPKsPP4-mp7VcGwABXwA\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-05-13 14:32:53'),(20,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"test\",\"surname\":\"test\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"aaaa\",\"agree\":true,\"antispamHash\":\"2jqbimjd\",\"antispamNoJs\":\"234\",\"file\":\"filled\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-05-13T14:49:34.124615+02:00\",\"extra\":{\"url\":\"/\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"koh-i-noor-stage.www6.superkoderi.cz\",\"referrer\":\"https://koh-i-noor-stage.www6.superkoderi.cz/\",\"unique_id\":\"aCM_3dr0KBtsNs32t2RGYgAAowI\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-05-13 14:49:34'),(21,'contactForm-form','{\"message\":\"contactForm-form\",\"context\":{\"formData\":{\"name\":\"ggg\",\"surname\":\"ggg\",\"email\":\"<EMAIL>\",\"phone\":\"+420123123123\",\"text\":\"ggggg\",\"agree\":true,\"antispamHash\":\"2jqbimjd\",\"antispamNoJs\":\"234\"},\"mutation\":1},\"level\":200,\"level_name\":\"INFO\",\"channel\":\"formDataSaver\",\"datetime\":\"2025-05-13T14:58:11.091346+02:00\",\"extra\":{\"url\":\"/\",\"ip\":\"************\",\"http_method\":\"POST\",\"server\":\"koh-i-noor-stage.www6.superkoderi.cz\",\"referrer\":\"https://koh-i-noor-stage.www6.superkoderi.cz/\",\"unique_id\":\"aCNB44MQoDDhnZYakt-cUgAApgY\",\"browser\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}\n','2025-05-13 14:58:11');
/*!40000 ALTER TABLE `log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `file` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `checksum` char(32) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `executed` datetime NOT NULL,
  `ready` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type_file` (`group`,`file`)
) ENGINE=InnoDB AUTO_INCREMENT=201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'core_structure','2022-04-13-111300-mutation.sql','7b67653da8bc3fc5e6ab09310943865f','2024-03-18 10:17:43',1),(2,'core_basic_data','2022-04-13-111300-mutation.sql','4c178b044468ec5a32375bc795b3e360','2024-03-18 10:17:43',1),(3,'core_structure','2022-04-13-111400-user.sql','6877957d0c47f038e7dddaf2a78dba20','2024-03-18 10:17:43',1),(4,'core_basic_data','2022-04-13-111400-user.sql','962ec54b83aca602ffe2d102350ab2be','2024-03-18 10:17:43',1),(5,'core_structure','2022-04-13-111500-user_mutation.sql','6512d7f0f234dd94600e8f63de36c05e','2024-03-18 10:17:44',1),(6,'core_basic_data','2022-04-13-111500-user_mutation.sql','aac45ea93475af1819ae2b40b4fb3a3a','2024-03-18 10:17:44',1),(7,'core_structure','2022-04-14-111329-library.sql','c5c4d69820a89cb6fc3e05a116edaed1','2024-03-18 10:17:44',1),(8,'core_basic_data','2022-04-14-111329-library.sql','18576d912ff2ced0474680e5f82b7773','2024-03-18 10:17:45',1),(9,'core_structure','2022-04-14-111330-alias.sql','0f9c0b4201dc8567e38a5175477906b9','2024-03-18 10:17:45',1),(10,'core_structure','2022-04-14-111330-alias_history.sql','562f0e6ad8b547882d27f38be5b80538','2024-03-18 10:17:45',1),(11,'core_structure','2022-04-14-111330-file.sql','0a791272621f3ee2f9102cc7bb24cd5e','2024-03-18 10:17:45',1),(12,'core_basic_data','2022-04-14-111330-file.sql','3e375549347d070ae5d1e006636f8eab','2024-03-18 10:17:45',1),(13,'core_structure','2022-04-14-111330-image.sql','23389e8b61ac929fb48d0afd59b9fc4a','2024-03-18 10:17:47',1),(14,'core_basic_data','2022-04-14-111330-image.sql','3de01f41ba0b04c484479bc416f27cf0','2024-03-18 10:17:47',1),(15,'core_structure','2022-04-14-111330-string.sql','0babb04369b94e5bf4cf71dbde7e0917','2024-03-18 10:17:49',1),(16,'core_basic_data','2022-04-14-111330-string.sql','0be1fccd1a70578edf5c36182e6b4eaf','2024-03-18 10:17:49',1),(17,'core_structure','2022-04-14-111331-elastic_search_index.sql','5fac76795bcb37303cdc3a886cdb7152','2024-03-18 10:18:14',1),(18,'core_structure','2022-04-14-111331-redirect.sql','64223d71831fbcdbeba686b7e2fb9513','2024-03-18 10:18:14',1),(19,'emailTemplate_structure','2022-04-14-121320-email_template.sql','74daf6480f26bff5557d2d7211606662','2024-03-18 10:18:15',1),(20,'emailTemplate_basic_data','2022-04-14-121320-email_template.sql','201e8bcc72c066ba1c6a58cf66d340d6','2024-03-18 10:18:15',1),(21,'emailTemplate_structure','2022-04-14-121320-email_template_file.sql','1fe68936d5cc5ee97010174f57ace499','2024-03-18 10:18:15',1),(22,'state_structure','2022-04-14-121320-state.sql','ae0457327cdb647cc8bfaaa0f9f012fa','2024-03-18 10:18:15',1),(23,'author_structure','2022-04-14-121329-author.sql','fad58859d80a70734f1ca6cba27c2d49','2024-03-18 10:18:15',1),(24,'state_structure','2022-04-14-121329-state_migration.sql','df6cf2998933a735c531592940fcb1b7','2024-03-18 10:18:15',1),(25,'author_dummy_data','2022-04-14-121330-author.sql','7fe99f1aca374ca8f2dca37d7c930100','2024-03-18 10:18:15',1),(26,'author_structure','2022-04-14-121330-author_localization.sql','12dfdcde615b1d46ba7fbd5ac66bed64','2024-03-18 10:18:16',1),(27,'author_dummy_data','2022-04-14-121330-author_localization.sql','7cdad74defaeaeeeb4456be91f8e6c94','2024-03-18 10:18:16',1),(28,'author_dummy_data','2022-04-14-121330-author_localization_alias_fix.sql','0b71ac82b2464baad0226ec1d4f78368','2024-03-18 10:18:16',1),(29,'blog_structure','2022-04-14-121330-blog.sql','589cf00974704cbc2ac1c887b2d681bc','2024-03-18 10:18:16',1),(30,'other_structure','2022-04-14-121330-newsletter_email.sql','170d07a0d84090d602af6753d350c4ff','2024-03-18 10:18:16',1),(31,'other_basic_data','2022-04-14-121330-newsletter_email.sql','b1bc7c202fa70a125745fcec1843d098','2024-03-18 10:18:16',1),(32,'page_structure','2022-04-14-121330-page.sql','6cde697d5171295617ef64d74fddbb75','2024-03-18 10:18:16',1),(33,'state_basic_data','2022-04-14-121330-state.sql','79ea384f5fd5a3a594b4e983fee05211','2024-03-18 10:18:16',1),(34,'user_structure','2022-04-14-121330-user_hash.sql','0520f8ce1677f78560151adebf491f60','2024-03-18 10:18:17',1),(35,'user_dummy_data','2022-04-14-121330-user_hash.sql','476a9ad440b051d103c0f5e45a8c3d32','2024-03-18 10:18:17',1),(36,'user_structure','2022-04-14-121330-user_image.sql','8fa6a41ee9a5fad50498db80cebaa8f8','2024-03-18 10:18:17',1),(37,'user_dummy_data','2022-04-14-121330-user_image.sql','36733a001d919038c3d434dc42394c56','2024-03-18 10:18:17',1),(38,'author_structure','2022-04-14-121331-blog_author.sql','6ffead7c62f3c605d965beb0bd9bbd80','2024-03-18 10:18:17',1),(39,'blog_structure','2022-04-14-121331-blog_tag.sql','d3f1c9fe0901d8360735cf343c8a3669','2024-03-18 10:18:17',1),(40,'blog_dummy_data','2022-04-14-121331-blog_tag.sql','4697170f5ac21fa2bf1b2ba6c41330f4','2024-03-18 10:18:17',1),(41,'blog_structure','2022-04-14-121331-blog_tree.sql','7d138e945a4b7839e41aa51cd7fd9fba','2024-03-18 10:18:17',1),(42,'blog_dummy_data','2022-04-14-121331-blog_tree.sql','e127553fb5651fb2b6896f1755e6a6a3','2024-03-18 10:18:18',1),(43,'page_basic_data','2022-04-14-121331-page.sql','2a91e9b27ab527aa909162d963dd5225','2024-03-18 10:18:18',1),(44,'page_structure','2022-04-14-121331-page_file.sql','d6d564073ec552c9787b2170a7dc3591','2024-03-18 10:18:18',1),(45,'page_structure','2022-04-14-121331-page_image.sql','5711a6997244bda926576eccb5e99f9e','2024-03-18 10:18:18',1),(46,'state_basic_data','2022-04-14-121331-state_migration.sql','347a00e12b76188009e67560f964ac88','2024-03-18 10:18:18',1),(47,'blog_structure','2022-04-14-121332-blog_blog.sql','87e5377b3263864df54ce24945e6b5b4','2024-03-18 10:18:18',1),(48,'blog_structure','2022-04-14-121332-blog_blog_tag.sql','99486d28afc7480a8d25e168fcc446ea','2024-03-18 10:18:19',1),(49,'blog_dummy_data','2022-04-14-121332-blog_blog_tag.sql','e133473eaf8ce2c4467430bdde902123','2024-03-18 10:18:19',1),(50,'page_basic_data','2022-04-14-121332-page_image.sql','f149564bab254d435d5d87706b92ff3d','2024-03-18 10:18:19',1),(51,'page_structure','2022-04-14-121332-page_parameter.sql','f5224a18cc2fcbc7a285c236ce442af8','2024-03-18 10:18:19',1),(52,'blog_basic_data','2022-04-14-121334-page.sql','0fab8137a8b9c593e8684cca0ec8dc82','2024-03-18 10:18:19',1),(53,'user_basic_data','2022-04-14-131330-page.sql','4354de150efa3ac6187a7ebdad72bcb7','2024-03-18 10:18:19',1),(54,'page_basic_data','2022-04-14-131331-page_file.sql','f13deba8fe7050605f703267baba9068','2024-03-18 10:18:19',1),(55,'page_structure','2022-04-14-131331-page_page.sql','f474763643017fb472f12181edfedede','2024-03-18 10:18:20',1),(56,'page_basic_data','2022-04-14-131331-page_page.sql','5a173950911c5272c7f8e6cdc99bb7df','2024-03-18 10:18:20',1),(57,'author_basic_data','2022-04-15-121329-page.sql','eaff490b0ca99cd9ae8f8d486d67239e','2024-03-18 10:18:20',1),(58,'blog_dummy_data','2022-04-15-121330-blog.sql','8bf5b98c20c24f3353ce4215b1660c43','2024-03-18 10:18:20',1),(59,'blog_dummy_data','2022-04-15-133330-pages.sql','1a1c312fda5d045cb4dfa4f6e45e8fe9','2024-03-18 10:18:20',1),(60,'blog_structure','2022-05-02-211330-blog_and_bloglocalization.sql','a136cd0878bdeb59ecffadf31c79390d','2024-03-18 10:18:20',1),(61,'blog_dummy_data','2022-05-02-211332-blog_and_bloglocalization.sql','1845a5e1f50a763b302f7203af83f24b','2024-03-18 10:18:21',1),(62,'core_structure','2022-08-09-134430-string.sql','93bc2f7e8eecd4cca45f62a7ed49c208','2024-03-18 10:18:21',1),(63,'page_structure','2022-08-12-1315-page_parameter.sql','fbb486d0e180e6130740f20d6d3c4084','2024-03-18 10:18:21',1),(64,'blog_structure','2022-08-12-144300-blog_tag_localization.sql','336ebfd4fcc18d8bce54af52752c95e6','2024-03-18 10:18:21',1),(65,'blog_dummy_data','2022-08-12-144500-blog_tag_bloglocalization.sql','0a72f5e5dce2082a75beeadca305cac4','2024-03-18 10:18:21',1),(66,'blog_dummy_data','2022-08-12-145000-blog_tag_bloglocalization.sql','a22fe91080c1db5303315f8775da0ee8','2024-03-18 10:18:21',1),(67,'blog_structure','2022-08-12-145000-blog_tag_localization.sql','f18db20739ab153b3d49b6f7f0969de8','2024-03-18 10:18:21',1),(68,'core_structure','2022-08-18-174430-fix_tree_cf.sql','e3d515c24409a68c2100de32e6df8845','2024-03-18 10:18:22',1),(69,'page_basic_data','2022-08-18-175000-page_fix_cf.sql','1dc11b37b854f9d0eb5aa4c616d6c454','2024-03-18 10:18:22',1),(70,'core_structure','2022-08-22-004400-es.sql','4b97c1b9d77eecab3e89e284570cf977','2024-03-18 10:18:22',1),(71,'core_structure','2022-08-22-134500-es_add_name.sql','91e287fbd44ff4ad7006c227a9d5c9bb','2024-03-18 10:18:22',1),(72,'blog_structure','2022-08-22-220000-blog_localization_tree.sql','3a435a5c7dd9bfc10abe88c057af1b83','2024-03-18 10:18:22',1),(73,'blog_structure','2022-08-25-115000-default_values.sql','211756492606cb5ac7b32cfe1cc59b43','2024-03-18 10:18:23',1),(74,'core_basic_data','2022-08-26-143134-template-migration.sql','f98388d2a04fbd684ff687fc3c66427c','2024-03-18 10:18:23',1),(75,'core_structure','2022-09-05-091945-mutation-cf.sql','59ee7be880ca46ba8af6c29a378152cd','2024-03-18 10:18:23',1),(76,'page_structure','2022-09-05-110819-page-mutation.sql','110df1af8a03b3b6194d8c53982ae604','2024-03-18 10:18:24',1),(77,'core_structure','2022-09-06-135421-en-mutation.sql','e5a93c80548de4a0b7d7c5df748b0324','2024-03-18 10:18:24',1),(78,'user_dummy_data','2022-09-07-130757-test-user.sql','28c61d4fe806e5c1b9de28f51bbe4ed6','2024-03-18 10:18:24',1),(79,'parameter_structure','2022-09-20-100000-parameter.sql','10596bbafcc365057c59cf49a266351f','2024-03-18 10:18:24',1),(80,'parameter_structure','2022-09-20-100001-parameter-value.sql','367d22a8bdeea79de6744eb78807c25f','2024-03-18 10:18:24',1),(81,'parameter_structure','2022-09-20-100002-parameter-value-image.sql','7031f6b671fbdd2296827b5f7592955c','2024-03-18 10:18:24',1),(82,'parameter_basic_data','2022-09-20-101001-parameter.sql','e822fe28a8cdb7620f69416e8ec6c2c6','2024-03-18 10:18:24',1),(83,'parameter_basic_data','2022-09-20-101002-parameter-value.sql','f0eb69ec3c65bf7e00e4e453793df8f7','2024-03-18 10:18:25',1),(84,'product_structure','2022-09-20-110000-price-level.sql','5cb7454edfd567d9978dc33e17fde73f','2024-03-18 10:18:25',1),(85,'product_structure','2022-09-20-110001-product.sql','af6e654c52a72e67f581b19298ccf033','2024-03-18 10:18:25',1),(86,'product_structure','2022-09-20-110002-product-localization.sql','348df57f1c254b30ed7ba0634137d2a4','2024-03-18 10:18:25',1),(87,'product_structure','2022-09-20-110003-product-variant.sql','b3ac531f8ac1c4721abdda702e778136','2024-03-18 10:18:26',1),(88,'product_structure','2022-09-20-110004-product-variant-localization.sql','720cc4c6f6efd03c94a5164a84b157af','2024-03-18 10:18:26',1),(89,'product_structure','2022-09-20-110005-page-product.sql','7924fb2de33e661a3066d46485692ef7','2024-03-18 10:18:26',1),(90,'product_structure','2022-09-20-110006-product-file.sql','bfeb840cc60ada461384bd616818cccb','2024-03-18 10:18:26',1),(91,'product_structure','2022-09-20-110007-product-image.sql','d81d8b69720f2a4dffe3f889c3fc4bbb','2024-03-18 10:18:26',1),(92,'product_structure','2022-09-20-110008-product-page.sql','eb63c8e297e2d10185fce0805f998a65','2024-03-18 10:18:26',1),(93,'product_structure','2022-09-20-110009-product-parameter.sql','d4e55a34105d84cdeb5a3f3a6a3506c4','2024-03-18 10:18:26',1),(94,'product_structure','2022-09-20-110010-product-product.sql','4044d316f57fb6022c2680abbf8559db','2024-03-18 10:18:26',1),(95,'product_structure','2022-09-20-110011-service.sql','1053ee0d4871e9ec19e7a811cbd9a11a','2024-03-18 10:18:26',1),(96,'product_structure','2022-09-20-110014-product-review.sql','9c06b6bf9f6d613e95efdf70caaf337d','2024-03-18 10:18:26',1),(97,'product_structure','2022-09-20-110015-product-price.sql','2792a87ab3426e3a8d3a761efea8d75a','2024-03-18 10:18:26',1),(98,'product_basic_data','2022-09-20-111001-price-level.sql','f1df23ad690ada9417660c47b706b720','2024-03-18 10:18:27',1),(99,'product_basic_data','2022-09-20-111002-product.sql','b8c212fcc4d43f36ab63201fe08d129d','2024-03-18 10:18:27',1),(100,'product_basic_data','2022-09-20-111003-product-localization.sql','50d47222b4d3aa8a01bcc990920141ba','2024-03-18 10:18:27',1),(101,'product_basic_data','2022-09-20-111004-product-variant.sql','1021814a39ff608160e4d97ef79f138f','2024-03-18 10:18:27',1),(102,'product_basic_data','2022-09-20-111005-product-variant-localization.sql','0128b5820e3e0dbd9b4bc0c89b1e16da','2024-03-18 10:18:27',1),(103,'product_basic_data','2022-09-20-111006-page-product.sql','7e30e15767af46b773f2a45d10585d87','2024-03-18 10:18:28',1),(104,'product_basic_data','2022-09-20-111007-product-file.sql','8237852c7369f93beb6c9be613050d7c','2024-03-18 10:18:28',1),(105,'product_basic_data','2022-09-20-111008-product-image.sql','5b8bc5656a7d6a62ff993ee2b75f323c','2024-03-18 10:18:29',1),(106,'product_basic_data','2022-09-20-111009-product-page.sql','d9a703d9ac302e1f02dfb7d1a2178aec','2024-03-18 10:18:29',1),(107,'product_basic_data','2022-09-20-111010-product-parameter.sql','51ddc0b1450a254b73b3e9ebc94c8a27','2024-03-18 10:18:29',1),(108,'product_basic_data','2022-09-20-111011-product-product.sql','264ed7199f2f317b63755074eb7f71ac','2024-03-18 10:18:30',1),(109,'product_basic_data','2022-09-20-111012-service.sql','b4a97c67c3241425330047e82d59304e','2024-03-18 10:18:31',1),(110,'product_basic_data','2022-09-20-111013-product-price.sql','277bebe852f662306f1c86ae3aea05df','2024-03-18 10:18:31',1),(111,'stock_structure','2022-09-20-120001-stock.sql','1e510007692115a79a1e946454a911bd','2024-03-18 10:18:32',1),(112,'stock_structure','2022-09-20-120002-stock-supplies.sql','87a40671f3a18fe62e5ce35df4f8ec9e','2024-03-18 10:18:32',1),(113,'stock_basic_data','2022-09-20-121001-stock.sql','58caee064b22a1d51f83cc154bd81838','2024-03-18 10:18:32',1),(114,'stock_basic_data','2022-09-20-121002-stock-supplies.sql','57a9a68aecd2e302685198e4544e22ea','2024-03-18 10:18:32',1),(115,'page_basic_data','2022-09-26-133733-catalog-pages.sql','7274b36edeed67c53d0604861ac80387','2024-03-18 10:18:33',1),(116,'product_structure','2022-09-26-141930-product-cc-remove-tabs.sql','a33f116036425cfcfcb29605726ece42','2024-03-18 10:18:33',1),(117,'page_structure','2022-09-26-153141-remove-cc-scheme.sql','c43fbdec5931b675f773b3b8d5bc7b91','2024-03-18 10:18:33',1),(118,'user_structure','2022-09-26-233002-price-level.sql','82ad89e1e728638cbccfd7916bc33106','2024-03-18 10:18:34',1),(119,'core_structure','2022-09-26-234114-fix-es-name-to-virtual.sql','19019f9aa9761b60fbdac585900002ee','2024-03-18 10:18:34',1),(120,'parameter_structure','2022-09-27-001925-fix-cf.sql','e900c4c902cf5d0123ab98060ece1d6f','2024-03-18 10:18:34',1),(121,'parameter_structure','2022-09-27-101343-cleaning.sql','928d0df4853ee176713d6cfa5b1ad7dd','2024-03-18 10:18:34',1),(122,'parameter_structure','2022-09-27-135501-add-value-cf.sql','e6334b3ac56c3824afeae123d1a81853','2024-03-18 10:18:34',1),(123,'product_structure','2022-09-27-153700-remove-services.sql','fb2be8728a218bb21d03a9912c3cc674','2024-03-18 10:18:34',1),(124,'core_structure','2022-09-27-153900-mutation-currency.sql','802ca38d46f70747b801eadf83af64bb','2024-03-18 10:18:35',1),(125,'state_structure','2022-09-27-153901-state-vat-rates.sql','709ec6ac71e3d671a7a52dda0be406af','2024-03-18 10:18:35',1),(126,'product_structure','2022-09-27-153902-price-as-money.sql','5af41d385c902a584d30f6bcff9d9675','2024-03-18 10:18:35',1),(127,'product_structure','2022-09-27-172617-product-vat-rates.sql','9a5e6ba5bb30798cf935750779b04d44','2024-03-18 10:18:35',1),(128,'page_structure','2022-10-03-140737-page-fix.sql','7ca37aa3be6638ce0423f00520da77ee','2024-03-18 10:18:35',1),(129,'core_basic_data','2022-10-10-101003-user_serction.sql','2d6b4b829ef976c4191ee8668a3546d5','2024-03-18 10:18:35',1),(130,'user_structure','2022-10-10-141454-uniq-email.sql','421934ef87c6955b3f10ddeff7d25e41','2024-03-18 10:18:36',1),(131,'core_structure','2022-10-11-125337-mutation-iso-code.sql','79cc64129847527f8e8db7ccf299128a','2024-03-18 10:18:36',1),(132,'core_structure','2022-10-11-130000-elastic-has-name.sql','85186d3e4c419d0ff708be0f736ae41b','2024-03-18 10:18:36',1),(133,'state_structure','2022-10-11-130412-utf8mb.sql','45d19ab08913013bbc5609bae7105b4d','2024-03-18 10:18:36',1),(134,'stock_structure','2022-10-11-130449-utf8mb.sql','860a5143127d925c22addfa0c587f5fb','2024-03-18 10:18:36',1),(135,'page_structure','2022-10-11-130704-utf8mb.sql','81665a8ff69e6486ccab389653c9c7db','2024-03-18 10:18:36',1),(136,'user_structure','2022-10-11-130926-utf8mb.sql','47b97c94d8777c3658735f8a80fca9e4','2024-03-18 10:18:37',1),(137,'author_structure','2022-10-11-134039-utf8mb.sql','467e5a01ea9c5a98ae5fd60e4dd57e91','2024-03-18 10:18:37',1),(138,'blog_structure','2022-10-11-134558-utf8mb.sql','74821bd6092ca1bb15e5bdae20652585','2024-03-18 10:18:37',1),(139,'core_structure','2022-10-11-134947-utf8mb.sql','1d900c0b3bc711d736902b36b284d7b4','2024-03-18 10:18:37',1),(140,'emailTemplate_structure','2022-10-11-135104-utf8mb.sql','df970c988bf66d3bf95dab5de5777a9d','2024-03-18 10:18:37',1),(141,'other_structure','2022-10-11-135535-utf8mb.sql','9aa0bf89f4b2812101c9c0f375297326','2024-03-18 10:18:38',1),(142,'parameter_structure','2022-10-11-135623-utf8mb.sql','92c3fe8b7da6705f6da57834f5d3c1aa','2024-03-18 10:18:38',1),(143,'product_structure','2022-10-11-135749-utf8mb.sql','b5eee6e2fbdc1bab4699d5eee9be7fff','2024-03-18 10:18:38',1),(144,'core_structure','2022-10-11-141148-setup-db.sql','64347bd6229d18ca66404e0760eae026','2024-03-18 10:18:38',1),(145,'seolink_structure','2022-10-11-144444-seolink.sql','ace588be77ea0a7f0f5ede2853a19577','2024-03-18 10:18:38',1),(146,'user_structure','2022-10-18-165656-google-login.sql','c420783adfe031966f4f6c5ea5657a38','2024-03-18 10:18:39',1),(147,'user_dummy_data','2022-10-31-114554-test-user-role.sql','a7450cfdec9b4d0ca98be757576c7437','2024-03-18 10:18:39',1),(148,'core_basic_data','2022-11-15-112317-remove-404.sql','657e6f422e9d1f28befac7b6b48931db','2024-03-18 10:18:39',1),(149,'product_basic_data','2022-11-16-132636-product-aliases.sql','c6274949588d8b759f4e79ce13ba65a3','2024-03-18 10:18:39',1),(150,'user_structure','2022-12-07-102609-remove-image.sql','5a1bec842cd7d6ad332d70d73a7f7529','2024-03-18 10:18:39',1),(151,'page_structure','2022-12-22-093531-remove-old-attrs.sql','8b0a2dde60efe1af704a18772971306e','2024-03-18 10:18:39',1),(152,'user_structure','2023-01-13-163113-api-token.sql','22929912a54870c2ed7767379a59a1d3','2024-03-18 10:18:39',1),(153,'page_structure','2023-01-25-135518-tree-parent.sql','eeecdb2ac73dda1e0cf1d48552de2139','2024-03-18 10:18:39',1),(154,'seolink_structure','2023-02-06-142110-fix-create.sql','ff6c995f325198954a6ff51d8f2d592f','2024-03-18 10:18:39',1),(155,'blog_structure','2023-02-09-154713-fix-varchars.sql','933ad0e57d589c162cc5be0c7735f786','2024-03-18 10:18:40',1),(156,'author_structure','2023-02-09-154828-fix-varchars.sql','b75ffe78dcc54b042717a964f4091c5c','2024-03-18 10:18:40',1),(157,'seolink_structure','2023-02-09-155057-fix-varchars.sql','60cd912552fc052b7bbb61c3d68766fe','2024-03-18 10:18:40',1),(158,'core_dummy_data','2023-03-01-115500-other_mutation.sql','659b496ddbcb1b2d63485d0907c62ea8','2024-03-18 10:18:40',1),(159,'user_basic_data','2023-03-01-135522-add-martina.sql','474dde299ce25a16e128da96ed405052','2024-03-18 10:18:40',1),(160,'other_structure','2023-03-02-114600-fix-newsletter-email.sql','e075aa0c0dcf95eb9cac0c3785c6e7df','2024-03-18 10:18:40',1),(161,'page_basic_data','2023-03-03-105725-pages-parent.sql','3d682d4c0665b264583355843653471a','2024-03-18 10:18:41',1),(162,'product_basic_data','2023-03-03-145217-fix-catalog.sql','8acca07d175fe93800adff61b3744d55','2024-03-18 10:18:41',1),(163,'product_structure','2023-03-09-152255-remove-old-parameter.sql','4c42302ceac26b7bb2198a7e64533d73','2024-03-18 10:18:41',1),(164,'core_basic_data','2023-03-13-084731-remove-testing-mutation.sql','e4627f227a92d084eaa494619944e8aa','2024-03-18 10:18:41',1),(165,'other_structure','2023-03-14-145241-fix-library.sql','c5fa785dd98f5b52a58306fe46c35f89','2024-03-18 10:18:41',1),(166,'blog_structure','2023-05-16-124751-is-top.sql','86c2a98b0e1a6f975d1efd9ec7e767d7','2024-03-18 10:18:41',1),(167,'core_structure','2023-07-11-135739-log.sql','130c7a64b285f3f6d459e7083d23b5b3','2024-03-18 10:18:41',1),(168,'core_structure','2023-08-10-141221-image-alts.sql','c8cc7c5f2184299d83c241f575097b0a','2024-03-18 10:18:42',1),(169,'core_structure','2023-08-21-150938-remove-tree-image.sql','7c9aac9d90eb0e04750ce20e6c817ed8','2024-03-18 10:18:42',1),(170,'product_structure','2023-08-22-150303-images.sql','0a28778abff058dc80aecd68ac19beee','2024-03-18 10:18:42',1),(171,'other_structure','2023-08-31-121654-states.sql','508b0370daecfed173a4637f8710b5d3','2024-03-18 10:18:42',1),(172,'other_structure','2023-09-01-101015-state-vats.sql','11df9b7bd43637082f288cc0391aac28','2024-03-18 10:18:47',1),(173,'order_structure','2024-02-26-091639-order.sql','7edf0ac528297fea23061a1a68295fb3','2024-03-18 10:18:47',1),(174,'core_basic_data','2024-02-28-161117-set-mutation-state.sql','a312b75fe5f83ed27bc7764c5f4f99d4','2024-03-18 10:18:48',1),(175,'order_structure','2024-02-29-132959-add-currency-to-order.sql','e6837f8f8e42fbf0941446b7262531e9','2024-03-18 10:18:48',1),(176,'page_basic_data','2024-03-01-135350-added-prebasket-and-cart-pages.sql','abf6a740011ef703256934aeba7ef791','2024-03-18 10:18:48',1),(177,'core_structure','2024-03-04-184310-added-table-holiday.sql','63d3d159801bc10f3193ce42a195315f','2024-03-18 10:18:48',1),(178,'product_structure','2024-03-05-103405-added-supply-column-deliverydelay.sql','8f981eacf7ebdbcf26046eba952e0a25','2024-03-18 10:18:48',1),(179,'order_structure','2024-03-06-141204-updated-voucher.sql','489fa1d616e5ffe9aadb1a11f2c76ed5','2024-03-18 10:18:48',1),(180,'user_basic_data','2024-03-07-111513-developer-kubomikita.sql','8f4ebcb1ab09423a016abe1499d7bba2','2024-03-18 10:18:49',1),(181,'core_basic_data','2024-03-07-145038-added-translations-to-cart-voucher.sql','8bddd33a1078aad0b327962bf199c686','2024-03-18 10:18:49',1),(182,'order_structure','2024-03-08-081809-added-properties-to-delivery-method.sql','c9a927840116026890b8be9faf5c659c','2024-03-18 10:18:49',1),(183,'order_structure','2024-03-11-121650-added-properties-to-payment-method.sql','6ddd3f173657b77420eac3ccce208c3c','2024-03-18 10:18:49',1),(184,'order_structure','2024-03-14-083938-drop-order-delivery-payment.sql','********************************','2024-03-18 10:18:49',1),(185,'order_structure','2024-03-14-110731-order-number-sequence.sql','efccd43abded906f28f705a0460b85a9','2024-03-18 10:18:50',1),(194,'core_basic_data','2024-03-20-090716-add-profile-pages.sql','bca8f16e2d4fe13d3e2bb238d33421f2','2024-04-10 08:50:04',1),(195,'order_structure','2024-03-22-134236-order-state-history.sql','f87ff76be5f821b5eeb9ac7a2051ac5b','2024-04-10 08:51:53',1),(196,'order_structure','2024-03-24-141910-card-payment.sql','0719f290c536b8f406befd0834e3ce55','2024-04-10 08:51:53',1),(198,'core_structure','2024-04-24-174059-form-failed-submission.sql','f626cb8b2aaeed24476ff71ba102723e','2024-05-15 09:44:08',1),(200,'core_structure','2025-04-04-111331-file.sql','10615b1df9378a0251b53efc3bc27c14','2025-04-04 17:42:27',1);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mutation`
--

DROP TABLE IF EXISTS `mutation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mutation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `langCode` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isoCodePrefix` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '',
  `public` int(11) DEFAULT '1',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `langMenu` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `adminEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `contactEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `orderEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `fromEmail` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `fromEmailName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `synonyms` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `heurekaOverenoKey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `langCode` (`langCode`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mutation`
--

LOCK TABLES `mutation` WRITE;
/*!40000 ALTER TABLE `mutation` DISABLE KEYS */;
INSERT INTO `mutation` VALUES (1,'cs','cs',1,'Čeština',NULL,'<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','SuperAdmin','CZK','{\"xbox\":[\"produkt 4\",\"xboxíček\"]}','','{\"mutationData\":[{\"icon\":\"cz\"}],\"footerMenu\":[{\"footer_menu_1\":[{\"title\":\"Zákaznický servis\",\"list\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":102}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":8}]}]}],\"footer_menu_2\":[{\"title\":\"Užitečné informace\",\"list\":[{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":445}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":459}]},{\"toggle\":\"systemHref\",\"systemHref\":[{\"page\":40}]}]}],\"footer_menu_3\":[{\"title\":\"O nás\",\"list\":[{\"toggle\":\"customHref\",\"customHref\":[{\"href\":\"#\",\"hrefName\":\"Externí odkaz\"}]}]}]}]}'),(2,'en','en',1,'English',NULL,'<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','SuperAdmin','EUR',NULL,NULL,'{}');
/*!40000 ALTER TABLE `mutation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mutation_state`
--

DROP TABLE IF EXISTS `mutation_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mutation_state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mutationId_stateId` (`mutationId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `mutation_state_ibfk_3` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `mutation_state_ibfk_4` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mutation_state`
--

LOCK TABLES `mutation_state` WRITE;
/*!40000 ALTER TABLE `mutation_state` DISABLE KEYS */;
INSERT INTO `mutation_state` VALUES (1,1,61),(3,1,207),(2,2,2);
/*!40000 ALTER TABLE `mutation_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newsletter_email`
--

DROP TABLE IF EXISTS `newsletter_email`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `newsletter_email` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `createdTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_mutationId` (`email`,`mutationId`),
  KEY `newsletter_email_mutation` (`mutationId`),
  CONSTRAINT `newsletter_email_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf32;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newsletter_email`
--

LOCK TABLES `newsletter_email` WRITE;
/*!40000 ALTER TABLE `newsletter_email` DISABLE KEYS */;
INSERT INTO `newsletter_email` VALUES (1,'<EMAIL>','2017-12-05 08:00:04',1);
/*!40000 ALTER TABLE `newsletter_email` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order`
--

DROP TABLE IF EXISTS `order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hash` varchar(128) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `placedAt` datetime DEFAULT NULL,
  `orderNumber` varchar(255) DEFAULT NULL,
  `userId` int(11) DEFAULT NULL,
  `mutationId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `street` varchar(255) NOT NULL,
  `city` varchar(255) NOT NULL,
  `zip` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `note` text NOT NULL,
  `countryId` int(11) NOT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `companyIdentifier` varchar(255) DEFAULT NULL,
  `vatNumber` varchar(255) DEFAULT NULL,
  `deliveryId` int(11) DEFAULT NULL,
  `paymentId` int(11) DEFAULT NULL,
  `currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `orderNumber` (`orderNumber`,`mutationId`),
  UNIQUE KEY `hash` (`hash`),
  KEY `userId` (`userId`),
  KEY `mutationId` (`mutationId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `countryId` (`countryId`),
  KEY `deliveryId` (`deliveryId`),
  KEY `paymentId` (`paymentId`),
  CONSTRAINT `order_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`),
  CONSTRAINT `order_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`),
  CONSTRAINT `order_ibfk_3` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `order_ibfk_4` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`),
  CONSTRAINT `order_ibfk_7` FOREIGN KEY (`deliveryId`) REFERENCES `order_delivery` (`id`),
  CONSTRAINT `order_ibfk_8` FOREIGN KEY (`paymentId`) REFERENCES `order_payment` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order`
--

LOCK TABLES `order` WRITE;
/*!40000 ALTER TABLE `order` DISABLE KEYS */;
INSERT INTO `order` VALUES (1,'b362fbd79a4edd9d44218282c2352c6eeff4c168','placed','2024-03-19 14:06:28','1-20240002',1,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,19,19,'CZK'),(2,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,9,9,'CZK'),(3,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,11,11,'CZK'),(4,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,13,13,'CZK'),(5,'e29c6e392a703c5542acce75810e85aef751d347','placed','2024-03-19 11:25:04','1-20240001',1,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,16,16,'CZK'),(6,'6170054ecf94736d813d1bb6265505fdafaf4e4d','placed','2024-03-20 15:03:37','1-20240003',1,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,21,21,'CZK'),(7,'d3a06ce3795fb81bf5b86f3e57ed46fab36dd948','placed','2024-04-10 09:32:52','1-20240004',11,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,25,25,'CZK'),(8,'b543432f8f84bdbf039e3d02333a0034128e73e0','placed','2024-04-10 09:34:25','1-20240005',11,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,24,24,'CZK'),(10,NULL,'draft',NULL,NULL,4,1,1,'','','','','','','',61,NULL,NULL,NULL,28,28,'CZK'),(11,'4fb728ef6802f14c71ca09e23cddaf32fe88ae8e','placed','2024-07-26 07:31:17','1-20240006',11,1,1,'<EMAIL>','Eliška Plitzová','Klíčova','Brno','61800','*********','',61,NULL,NULL,NULL,40,40,'CZK'),(12,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,42,42,'CZK'),(13,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(14,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(15,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(16,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(17,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(18,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(19,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(20,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(21,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,NULL,NULL,'CZK'),(22,NULL,'draft',NULL,NULL,11,1,1,'','','','','','','',61,NULL,NULL,NULL,43,43,'CZK'),(23,NULL,'draft',NULL,NULL,1,1,1,'','','','','','','',61,NULL,NULL,NULL,45,45,'CZK'),(24,NULL,'draft',NULL,NULL,NULL,1,1,'','','','','','','',61,NULL,NULL,NULL,44,44,'CZK'),(25,NULL,'draft',NULL,NULL,43,1,1,'','','','','','','',61,NULL,NULL,NULL,47,47,'CZK');
/*!40000 ALTER TABLE `order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_delivery`
--

DROP TABLE IF EXISTS `order_delivery`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_delivery` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deliveryMethodId` int(11) DEFAULT NULL,
  `informationId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `vatRate` varchar(255) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `dateDeliveryFrom` date DEFAULT NULL,
  `dateDeliveryTo` date DEFAULT NULL,
  `dateExpedition` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deliveryMethodId` (`deliveryMethodId`),
  KEY `informationId` (`informationId`),
  CONSTRAINT `order_delivery_ibfk_1` FOREIGN KEY (`deliveryMethodId`) REFERENCES `delivery_method` (`id`),
  CONSTRAINT `order_delivery_ibfk_3` FOREIGN KEY (`informationId`) REFERENCES `order_delivery_information` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_delivery`
--

LOCK TABLES `order_delivery` WRITE;
/*!40000 ALTER TABLE `order_delivery` DISABLE KEYS */;
INSERT INTO `order_delivery` VALUES (9,1,9,1,'standard',0.0000,'CZK','2024-03-19',NULL,'2024-03-19'),(11,1,11,1,'standard',0.0000,'CZK','2024-03-19',NULL,'2024-03-19'),(13,1,13,1,'standard',0.0000,'CZK','2024-03-19',NULL,'2024-03-19'),(16,1,16,1,'standard',0.0000,'CZK','2024-03-20',NULL,'2024-03-20'),(19,1,19,1,'standard',0.0000,'CZK','2024-03-20',NULL,'2024-03-20'),(21,1,21,1,'standard',0.0000,'CZK','2024-03-21',NULL,'2024-03-21'),(24,1,24,1,'standard',0.0000,'CZK','2024-04-10',NULL,'2024-04-10'),(25,1,25,1,'standard',0.0000,'CZK','2024-04-10',NULL,'2024-04-10'),(28,1,28,1,'standard',0.0000,'CZK','2024-05-10',NULL,'2024-05-10'),(40,1,40,1,'standard',0.0000,'CZK','2024-07-26',NULL,'2024-07-26'),(42,1,42,1,'standard',0.0000,'CZK','2024-07-26',NULL,'2024-07-26'),(43,1,43,1,'standard',0.0000,'CZK','2024-10-14',NULL,'2024-10-14'),(44,1,44,1,'standard',0.0000,'CZK','2025-01-14',NULL,'2025-01-14'),(45,1,45,1,'standard',0.0000,'CZK','2025-01-22',NULL,'2025-01-22'),(47,1,47,1,'standard',0.0000,'CZK','2025-03-05',NULL,'2025-03-05');
/*!40000 ALTER TABLE `order_delivery` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_delivery_information`
--

DROP TABLE IF EXISTS `order_delivery_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_delivery_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `street` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip` varchar(255) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phoneNumber` varchar(255) DEFAULT NULL,
  `trackingCode` varchar(255) DEFAULT NULL,
  `pickupPointId` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `countryId` (`countryId`),
  CONSTRAINT `order_delivery_information_ibfk_1` FOREIGN KEY (`countryId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_delivery_information`
--

LOCK TABLES `order_delivery_information` WRITE;
/*!40000 ALTER TABLE `order_delivery_information` DISABLE KEYS */;
INSERT INTO `order_delivery_information` VALUES (9,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(11,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(13,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(16,'Physical','Eliška Plitzová',NULL,'Klíčova','Brno','61800',61,'*********',NULL,NULL),(19,'Physical','Eliška Plitzová',NULL,'Klíčova','Brno','61800',61,'*********',NULL,NULL),(21,'Physical','Eliška Plitzová',NULL,'Klíčova','Brno','61800',61,'*********',NULL,NULL),(24,'Physical','Eliška Plitzová',NULL,'Klíčova','Brno','61800',61,'*********',NULL,NULL),(25,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(28,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(40,'Physical','Eliška Plitzová',NULL,'Klíčova','Brno','61800',61,'*********',NULL,NULL),(42,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(43,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(44,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(45,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(47,'Physical',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `order_delivery_information` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_discount`
--

DROP TABLE IF EXISTS `order_discount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `discountTypeUniqueIdentifier` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  CONSTRAINT `order_discount_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_discount`
--

LOCK TABLES `order_discount` WRITE;
/*!40000 ALTER TABLE `order_discount` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_discount` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_number_sequence`
--

DROP TABLE IF EXISTS `order_number_sequence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_number_sequence` (
  `mutationId` int(11) NOT NULL,
  `year` year(4) NOT NULL,
  `number` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`mutationId`,`year`),
  CONSTRAINT `order_number_sequence_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_number_sequence`
--

LOCK TABLES `order_number_sequence` WRITE;
/*!40000 ALTER TABLE `order_number_sequence` DISABLE KEYS */;
INSERT INTO `order_number_sequence` VALUES (1,2024,6);
/*!40000 ALTER TABLE `order_number_sequence` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_payment`
--

DROP TABLE IF EXISTS `order_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentMethodId` int(11) DEFAULT NULL,
  `informationId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT '1',
  `vatRate` varchar(255) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentMethodId` (`paymentMethodId`),
  KEY `informationId` (`informationId`),
  CONSTRAINT `order_payment_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `order_payment_ibfk_2` FOREIGN KEY (`informationId`) REFERENCES `order_payment_information` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_payment`
--

LOCK TABLES `order_payment` WRITE;
/*!40000 ALTER TABLE `order_payment` DISABLE KEYS */;
INSERT INTO `order_payment` VALUES (9,1,9,1,'standard',0.0000,'CZK'),(11,1,11,1,'standard',0.0000,'CZK'),(13,1,13,1,'standard',0.0000,'CZK'),(16,1,16,1,'standard',0.0000,'CZK'),(19,1,19,1,'standard',0.0000,'CZK'),(21,1,21,1,'standard',0.0000,'CZK'),(24,1,24,1,'standard',0.0000,'CZK'),(25,1,25,1,'standard',0.0000,'CZK'),(28,1,28,1,'standard',0.0000,'CZK'),(40,1,40,1,'standard',0.0000,'CZK'),(42,1,42,1,'standard',0.0000,'CZK'),(43,1,43,1,'standard',0.0000,'CZK'),(44,1,44,1,'standard',0.0000,'CZK'),(45,1,45,1,'standard',0.0000,'CZK'),(47,1,47,1,'standard',0.0000,'CZK');
/*!40000 ALTER TABLE `order_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_payment_information`
--

DROP TABLE IF EXISTS `order_payment_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_payment_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `variableSymbol` varchar(255) DEFAULT NULL,
  `dueDate` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_payment_information`
--

LOCK TABLES `order_payment_information` WRITE;
/*!40000 ALTER TABLE `order_payment_information` DISABLE KEYS */;
INSERT INTO `order_payment_information` VALUES (9,'BankTransfer','Pending',NULL,NULL),(11,'BankTransfer','Pending',NULL,NULL),(13,'BankTransfer','Pending',NULL,NULL),(16,'BankTransfer','Pending',NULL,NULL),(19,'BankTransfer','Pending','*********',NULL),(21,'BankTransfer','Pending','*********',NULL),(24,'BankTransfer','Pending','*********','2024-04-24'),(25,'BankTransfer','Pending',NULL,NULL),(28,'BankTransfer','Pending',NULL,NULL),(40,'BankTransfer','Pending','*********','2024-08-09'),(42,'BankTransfer','Pending',NULL,NULL),(43,'BankTransfer','Pending',NULL,NULL),(44,'BankTransfer','Pending',NULL,NULL),(45,'BankTransfer','Pending',NULL,NULL),(47,'BankTransfer','Pending',NULL,NULL);
/*!40000 ALTER TABLE `order_payment_information` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_product`
--

DROP TABLE IF EXISTS `order_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `variantId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  KEY `variantId` (`variantId`),
  CONSTRAINT `order_product_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
  CONSTRAINT `order_product_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_product`
--

LOCK TABLES `order_product` WRITE;
/*!40000 ALTER TABLE `order_product` DISABLE KEYS */;
INSERT INTO `order_product` VALUES (1,1,2,50.0000,'CZK','standard',52),(2,2,2,50.0000,'CZK','standard',52),(3,3,1,50.0000,'CZK','standard',52),(4,4,1,50.0000,'CZK','standard',52),(5,5,1,50.0000,'CZK','standard',52),(6,6,12,50.0000,'CZK','standard',52),(10,8,1,5281.0000,'CZK','standard',57),(12,10,1,5281.0000,'CZK','standard',57),(13,11,2,180.0000,'CZK','standard',44),(14,11,1,100.0000,'CZK','standard',65),(15,12,1,180.0000,'CZK','standard',44),(16,13,1,200.0000,'CZK','standard',67),(17,14,1,1000.0000,'CZK','standard',53),(18,15,1,200.0000,'CZK','standard',67),(19,16,1,200.0000,'CZK','standard',67),(20,17,1,1000.0000,'CZK','standard',53),(21,18,1,1000.0000,'CZK','standard',53),(22,19,1,1000.0000,'CZK','standard',53),(23,20,1,1000.0000,'CZK','standard',53),(24,21,1,1000.0000,'CZK','standard',53),(25,22,1,1000.0000,'CZK','standard',53),(26,23,2,180.0000,'CZK','standard',44),(27,24,1,200.0000,'CZK','standard',67),(28,25,1,180.0000,'CZK','standard',44);
/*!40000 ALTER TABLE `order_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_state_change`
--

DROP TABLE IF EXISTS `order_state_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_state_change` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `changedAt` datetime NOT NULL,
  `from` varchar(255) DEFAULT NULL,
  `to` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  CONSTRAINT `order_state_change_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_state_change`
--

LOCK TABLES `order_state_change` WRITE;
/*!40000 ALTER TABLE `order_state_change` DISABLE KEYS */;
INSERT INTO `order_state_change` VALUES (1,8,'2024-04-10 09:31:43',NULL,'draft'),(2,8,'2024-04-10 09:34:25','draft','placed'),(4,10,'2024-05-07 10:14:50',NULL,'draft'),(5,11,'2024-06-12 17:17:40',NULL,'draft'),(6,11,'2024-07-26 07:31:17','draft','placed'),(7,12,'2024-07-26 10:09:18',NULL,'draft'),(8,13,'2024-09-02 08:44:50',NULL,'draft'),(9,14,'2024-09-02 08:45:03',NULL,'draft'),(10,15,'2024-09-02 08:46:11',NULL,'draft'),(11,16,'2024-09-02 08:46:57',NULL,'draft'),(12,17,'2024-09-02 08:47:23',NULL,'draft'),(13,18,'2024-09-02 08:49:31',NULL,'draft'),(14,21,'2024-09-02 08:55:45',NULL,'draft'),(15,22,'2024-09-02 08:56:04',NULL,'draft'),(16,23,'2025-01-13 13:28:59',NULL,'draft'),(17,24,'2025-01-13 13:41:20',NULL,'draft'),(18,25,'2025-03-05 03:31:16',NULL,'draft');
/*!40000 ALTER TABLE `order_state_change` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_voucher`
--

DROP TABLE IF EXISTS `order_voucher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `unitPrice_amount` decimal(18,4) NOT NULL,
  `unitPrice_currency` char(3) NOT NULL,
  `vatRate` varchar(255) NOT NULL,
  `voucherCodeId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `orderId` (`orderId`),
  KEY `voucherCodeId` (`voucherCodeId`),
  CONSTRAINT `order_voucher_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `order` (`id`),
  CONSTRAINT `order_voucher_ibfk_3` FOREIGN KEY (`voucherCodeId`) REFERENCES `voucher_code` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_voucher`
--

LOCK TABLES `order_voucher` WRITE;
/*!40000 ALTER TABLE `order_voucher` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_voucher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `parameter`
--

DROP TABLE IF EXISTS `parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parameter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `uid` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `type` enum('text','textarea','wysiwyg','number','bool','select','multiselect') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `sort` int(11) NOT NULL,
  `variantParameter` tinyint(4) NOT NULL,
  `isInFilter` int(11) NOT NULL DEFAULT '0',
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `isProtected` tinyint(4) DEFAULT '0',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`uid`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `parameter`
--

LOCK TABLES `parameter` WRITE;
/*!40000 ALTER TABLE `parameter` DISABLE KEYS */;
INSERT INTO `parameter` VALUES (113,'Select','select','select',2,1,1,'{}',0,NULL),(114,'Multiselect','multiselect','multiselect',1,0,1,'{}',0,NULL),(115,'Tag','tag','multiselect',1,0,0,'{}',0,NULL),(116,'Bool','bool','bool',3,0,1,'{}',0,NULL),(117,'Text','text','text',4,0,0,'{}',0,NULL),(118,'Textarea','textarea','textarea',5,0,0,'{}',0,NULL),(119,'Číslo','cislo','number',6,0,1,'{}',0,NULL),(120,'Wysiwyg','wysiwyg','wysiwyg',7,0,0,'{}',0,NULL),(121,'Velikost','velikost','select',8,0,1,'{}',0,NULL);
/*!40000 ALTER TABLE `parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `parameter_value`
--

DROP TABLE IF EXISTS `parameter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `parameter_value` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parameterId` int(11) NOT NULL,
  `internalValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `internalAlias` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `sort` int(11) NOT NULL,
  `parameterSort` int(11) NOT NULL,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`),
  KEY `parameterId` (`parameterId`),
  CONSTRAINT `parameter_value_ibfk_1` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6598 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `parameter_value`
--

LOCK TABLES `parameter_value` WRITE;
/*!40000 ALTER TABLE `parameter_value` DISABLE KEYS */;
INSERT INTO `parameter_value` VALUES (6564,113,'Hodnota 1','hodnota-1',1,1,NULL,NULL),(6565,113,'Hodnota 2','hodnota-2',2,1,NULL,NULL),(6566,113,'Hodnota 3','hodnota-3',3,1,NULL,NULL),(6567,114,'Hodnota 1','hodnota-1',1,1,NULL,NULL),(6568,115,'Tag 1','tag-1',1,1,NULL,NULL),(6569,115,'Tag 2','tag-2',2,1,NULL,NULL),(6570,114,'Hodnota 2','hodnota-2',2,1,NULL,NULL),(6571,114,'Hodnota 3','hodnota-3',3,1,NULL,NULL),(6572,119,'10','6572',1,1,NULL,NULL),(6573,119,'20','6573',2,1,NULL,NULL),(6574,119,'88','6574',3,1,NULL,NULL),(6575,119,'120','6575',4,1,NULL,NULL),(6576,119,'2','6576',5,1,NULL,NULL),(6577,117,'text','6577',0,0,NULL,NULL),(6578,118,'textarea','6578',0,0,NULL,NULL),(6579,120,'<p>Wysiwyg</p>','6579',0,0,NULL,NULL),(6580,116,'1','6580',0,0,NULL,NULL),(6581,118,'textarea.   gafsdfa','6581',0,0,NULL,NULL),(6582,121,'A4','a4',1,8,NULL,NULL),(6583,121,'A3','a3',2,8,NULL,NULL),(6585,119,'200','6585',0,0,NULL,NULL),(6593,114,'h4','h4',4,1,NULL,NULL),(6597,121,'A5','a5',3,8,NULL,'{}');
/*!40000 ALTER TABLE `parameter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method`
--

DROP TABLE IF EXISTS `payment_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `externalId` int(11) DEFAULT NULL,
  `paymentMethodUniqueIdentifier` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `desc` text NOT NULL,
  `tooltip` text,
  `sort` int(11) NOT NULL,
  `public` tinyint(1) NOT NULL,
  `isRecommended` tinyint(1) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `vats` text NOT NULL,
  `customFieldsJson` longtext,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `payment_method_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method`
--

LOCK TABLES `payment_method` WRITE;
/*!40000 ALTER TABLE `payment_method` DISABLE KEYS */;
INSERT INTO `payment_method` VALUES (1,NULL,'BankTransfer','Credit card','poips','oijoij',0,1,0,1,'{\"61\":\"standard\",\"207\":\"standard\"}','{\"deliveryPayment\":[{\"icon\":[\"107\"]}],\"paymentAccount\":[{\"bban\":\"1234\",\"iban\":\"CZ111111111111\",\"swift\":\"1234\"}]}');
/*!40000 ALTER TABLE `payment_method` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method_price`
--

DROP TABLE IF EXISTS `payment_method_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method_price` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentMethodId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paymentMethodId` (`paymentMethodId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `payment_method_price_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `payment_method_price_ibfk_2` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`),
  CONSTRAINT `payment_method_price_ibfk_3` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method_price`
--

LOCK TABLES `payment_method_price` WRITE;
/*!40000 ALTER TABLE `payment_method_price` DISABLE KEYS */;
INSERT INTO `payment_method_price` VALUES (1,1,1,61,0.0000,'CZK');
/*!40000 ALTER TABLE `payment_method_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_method_x_state`
--

DROP TABLE IF EXISTS `payment_method_x_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_method_x_state` (
  `paymentMethodId` int(11) NOT NULL,
  `stateId` int(11) NOT NULL,
  PRIMARY KEY (`paymentMethodId`,`stateId`),
  KEY `stateId` (`stateId`),
  CONSTRAINT `payment_method_x_state_ibfk_1` FOREIGN KEY (`paymentMethodId`) REFERENCES `payment_method` (`id`),
  CONSTRAINT `payment_method_x_state_ibfk_2` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_method_x_state`
--

LOCK TABLES `payment_method_x_state` WRITE;
/*!40000 ALTER TABLE `payment_method_x_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_method_x_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `price_level`
--

DROP TABLE IF EXISTS `price_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `price_level`
--

LOCK TABLES `price_level` WRITE;
/*!40000 ALTER TABLE `price_level` DISABLE KEYS */;
INSERT INTO `price_level` VALUES (1,'default','Základní (MO)'),(2,'wholesale','Velkoobchodní (VO)');
/*!40000 ALTER TABLE `price_level` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product`
--

DROP TABLE IF EXISTS `product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Product:detail',
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `hideFirstImage` tinyint(1) NOT NULL,
  `availability` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isSet` tinyint(4) DEFAULT NULL,
  `isOld` tinyint(4) DEFAULT NULL,
  `isInPrepare` tinyint(4) DEFAULT NULL,
  `isNew` tinyint(4) NOT NULL,
  `notSoldSeparately` tinyint(4) DEFAULT NULL,
  `discount` float(10,2) DEFAULT NULL,
  `discountType` tinyint(1) DEFAULT NULL,
  `reviewAverage` float(10,2) NOT NULL DEFAULT '0.00',
  `isFreeTransport` tinyint(1) DEFAULT '0',
  `soldCount` int(11) DEFAULT '0',
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `editedTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `vats` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product`
--

LOCK TABLES `product` WRITE;
/*!40000 ALTER TABLE `product` DISABLE KEYS */;
INSERT INTO `product` VALUES (2,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','SK sportovní tričko',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"},\"article\":{\"tree\":\"\"}}','2024-07-26 09:47:46',11,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(4,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','Sada vizitek',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"}}','2024-07-26 09:40:04',11,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(5,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','Energy drink',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{\"feeds\":{\"zbozi\":\"\",\"heureka\":\"\",\"google\":\"\"}}','2025-04-09 17:23:20',4,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(7,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','produkt 7',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'[]','2025-04-10 10:30:12',42,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(9,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','Diář',0,'',NULL,0,0,1,0,NULL,NULL,0.00,1,0,'','2025-04-09 17:23:03',4,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(10,NULL,':Front:Product:detail','2018-04-11 15:28:00','2120-04-11 15:28:00','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1',0,'',NULL,0,0,1,0,NULL,NULL,0.00,0,0,'{}','2025-04-09 14:29:35',42,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(16,NULL,':Front:Product:detail','2024-07-19 09:56:00','2124-07-19 09:56:00','Produkt test',0,'',NULL,0,0,0,0,NULL,NULL,0.00,0,0,'{}','2024-07-26 09:49:47',11,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL),(18,NULL,':Front:Product:detail','2024-07-25 16:38:00','2124-07-25 16:38:00','SK tričko',0,'',NULL,0,0,1,0,NULL,NULL,0.00,1,0,'{}','2024-07-26 09:47:55',11,'{\"9\":\"standard\",\"10\":\"standard\",\"11\":\"standard\",\"12\":\"standard\",\"13\":\"standard\",\"14\":\"standard\",\"15\":\"standard\",\"16\":\"standard\",\"17\":\"standard\",\"18\":\"standard\",\"19\":\"standard\",\"20\":\"standard\",\"21\":\"standard\",\"22\":\"standard\",\"23\":\"standard\",\"24\":\"standard\",\"25\":\"standard\",\"26\":\"standard\",\"27\":\"standard\",\"28\":\"standard\",\"29\":\"standard\",\"30\":\"standard\",\"31\":\"standard\",\"32\":\"standard\",\"33\":\"standard\",\"34\":\"standard\",\"35\":\"standard\",\"36\":\"standard\",\"37\":\"standard\",\"38\":\"standard\",\"39\":\"standard\",\"40\":\"standard\",\"41\":\"standard\",\"42\":\"standard\",\"43\":\"standard\",\"44\":\"standard\",\"45\":\"standard\",\"46\":\"standard\",\"47\":\"standard\",\"48\":\"standard\",\"49\":\"standard\",\"50\":\"standard\",\"51\":\"standard\",\"52\":\"standard\",\"53\":\"standard\",\"54\":\"standard\",\"55\":\"standard\",\"56\":\"standard\",\"57\":\"standard\",\"58\":\"standard\",\"59\":\"standard\",\"60\":\"standard\",\"61\":\"standard\",\"62\":\"standard\",\"63\":\"standard\",\"64\":\"standard\",\"65\":\"standard\",\"66\":\"standard\",\"67\":\"standard\",\"68\":\"standard\",\"69\":\"standard\",\"70\":\"standard\",\"71\":\"standard\",\"72\":\"standard\",\"73\":\"standard\",\"74\":\"standard\",\"75\":\"standard\",\"76\":\"standard\",\"77\":\"standard\",\"78\":\"standard\",\"79\":\"standard\",\"80\":\"standard\",\"81\":\"standard\",\"82\":\"standard\",\"83\":\"standard\",\"84\":\"standard\",\"85\":\"standard\",\"86\":\"standard\",\"87\":\"standard\",\"88\":\"standard\",\"89\":\"standard\",\"90\":\"standard\",\"91\":\"standard\",\"92\":\"standard\",\"93\":\"standard\",\"94\":\"standard\",\"95\":\"standard\",\"96\":\"standard\",\"97\":\"standard\",\"98\":\"standard\",\"99\":\"standard\",\"100\":\"standard\",\"101\":\"standard\",\"102\":\"standard\",\"103\":\"standard\",\"104\":\"standard\",\"105\":\"standard\",\"106\":\"standard\",\"107\":\"standard\",\"108\":\"standard\",\"109\":\"standard\",\"110\":\"standard\",\"111\":\"standard\",\"112\":\"standard\",\"113\":\"standard\",\"114\":\"standard\",\"115\":\"standard\",\"116\":\"standard\",\"117\":\"standard\",\"118\":\"standard\",\"119\":\"standard\",\"120\":\"standard\",\"121\":\"standard\",\"122\":\"standard\",\"123\":\"standard\",\"124\":\"standard\",\"125\":\"standard\",\"126\":\"standard\",\"127\":\"standard\",\"128\":\"standard\",\"129\":\"standard\",\"130\":\"standard\",\"131\":\"standard\",\"132\":\"standard\",\"133\":\"standard\",\"134\":\"standard\",\"135\":\"standard\",\"136\":\"standard\",\"137\":\"standard\",\"138\":\"standard\",\"139\":\"standard\",\"140\":\"standard\",\"141\":\"standard\",\"142\":\"standard\",\"143\":\"standard\",\"144\":\"standard\",\"145\":\"standard\",\"146\":\"standard\",\"147\":\"standard\",\"148\":\"standard\",\"149\":\"standard\",\"150\":\"standard\",\"151\":\"standard\",\"152\":\"standard\",\"153\":\"standard\",\"154\":\"standard\",\"155\":\"standard\",\"156\":\"standard\",\"157\":\"standard\",\"158\":\"standard\",\"159\":\"standard\",\"160\":\"standard\",\"161\":\"standard\",\"162\":\"standard\",\"163\":\"standard\",\"164\":\"standard\",\"165\":\"standard\",\"166\":\"standard\",\"167\":\"standard\",\"168\":\"standard\",\"169\":\"standard\",\"170\":\"standard\",\"171\":\"standard\",\"172\":\"standard\",\"173\":\"standard\",\"174\":\"standard\",\"175\":\"standard\",\"176\":\"standard\",\"177\":\"standard\",\"178\":\"standard\",\"179\":\"standard\",\"180\":\"standard\",\"181\":\"standard\",\"182\":\"standard\",\"183\":\"standard\",\"184\":\"standard\",\"185\":\"standard\",\"186\":\"standard\",\"187\":\"standard\",\"188\":\"standard\",\"189\":\"standard\",\"190\":\"standard\",\"191\":\"standard\",\"192\":\"standard\",\"193\":\"standard\",\"194\":\"standard\",\"195\":\"standard\",\"196\":\"standard\",\"197\":\"standard\",\"198\":\"standard\",\"199\":\"standard\",\"200\":\"standard\",\"201\":\"standard\",\"202\":\"standard\",\"203\":\"standard\",\"204\":\"standard\",\"205\":\"standard\",\"206\":\"standard\",\"207\":\"standard\",\"208\":\"standard\",\"209\":\"standard\",\"210\":\"standard\",\"211\":\"standard\",\"212\":\"standard\",\"213\":\"standard\",\"214\":\"standard\",\"215\":\"standard\",\"216\":\"standard\",\"217\":\"standard\",\"218\":\"standard\",\"219\":\"standard\",\"220\":\"standard\",\"221\":\"standard\",\"222\":\"standard\",\"223\":\"standard\",\"224\":\"standard\",\"225\":\"standard\",\"226\":\"standard\",\"227\":\"standard\",\"228\":\"standard\",\"229\":\"standard\",\"230\":\"standard\",\"231\":\"standard\",\"232\":\"standard\",\"233\":\"standard\",\"234\":\"standard\",\"235\":\"standard\",\"236\":\"standard\",\"237\":\"standard\",\"238\":\"standard\",\"239\":\"standard\",\"240\":\"standard\",\"241\":\"standard\",\"242\":\"standard\",\"243\":\"standard\",\"244\":\"standard\",\"245\":\"standard\",\"246\":\"standard\",\"247\":\"standard\",\"248\":\"standard\",\"249\":\"standard\",\"250\":\"standard\",\"251\":\"standard\",\"252\":\"standard\",\"253\":\"standard\"}',NULL);
/*!40000 ALTER TABLE `product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_file`
--

DROP TABLE IF EXISTS `product_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productLocalizationId` int(11) NOT NULL,
  `fileId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_fileId` (`productLocalizationId`,`fileId`),
  KEY `fileId` (`fileId`),
  CONSTRAINT `product_file_ibfk_4` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_file_ibfk_5` FOREIGN KEY (`productLocalizationId`) REFERENCES `product_localization` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_file`
--

LOCK TABLES `product_file` WRITE;
/*!40000 ALTER TABLE `product_file` DISABLE KEYS */;
INSERT INTO `product_file` VALUES (1,6,9,'hezké jméno','/data/files/9-image-10.png',0,9141),(2,6,11,'Eu0gZ5_XMAABywS.jfif','/data/files/11-eu0gz5-xmaabyws.jpeg',1,63479),(3,5,9,'hezké jméno','/data/files/9-image-10.png',0,9141),(4,5,11,'Eu0gZ5_XMAABywS.jfif','/data/files/11-eu0gz5-xmaabyws.jpeg',1,63479),(6,5,12,'en name','/data/files/12-image-11.png',2,20042),(8,16,38,'Ceník','/data/files/38-kamenivo-cenik-2021.pdf',0,4785772),(20,20,67,'2.4936_Hammerli Hunter Force 900 Combo-bullet.pdf','/data/files/67-2.4936-hammerli-hunter-force-900-combo-bullet.pdf',0,382049),(21,20,126,'2020-Scrum-Guide-US.pdf','/data/files/126-2020-scrum-guide-us.pdf',1,254353),(25,6,132,'2020-Scrum-Guide-US.pdf','/data/files/132-2020-scrum-guide-us.pdf',2,254353);
/*!40000 ALTER TABLE `product_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_image`
--

DROP TABLE IF EXISTS `product_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_image` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `libraryImageId` int(11) NOT NULL COMMENT 'idfile',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` tinyint(4) DEFAULT NULL,
  `variants` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_imageId` (`productId`,`libraryImageId`) USING BTREE,
  UNIQUE KEY `productId_extId` (`productId`,`extId`),
  KEY `imageId` (`libraryImageId`) USING BTREE,
  CONSTRAINT `product_image_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_image_ibfk_4` FOREIGN KEY (`libraryImageId`) REFERENCES `image` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_image`
--

LOCK TABLES `product_image` WRITE;
/*!40000 ALTER TABLE `product_image` DISABLE KEYS */;
INSERT INTO `product_image` VALUES (7,10,33,NULL,0,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(8,10,34,NULL,1,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(9,10,36,NULL,2,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(10,10,32,NULL,3,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(11,10,39,NULL,4,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(12,10,38,NULL,5,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(13,10,35,NULL,6,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(14,10,37,NULL,7,'','{\"en\":{\"name\":\"\"},\"cs\":{\"name\":\"\"}}',NULL),(36,9,105,NULL,0,'','{\"en\":{\"name\":\"56850390_2263721606982027_1673383008721174528_n\"},\"cs\":{\"name\":\"56850390_2263721606982027_1673383008721174528_n\"}}',NULL),(37,9,104,NULL,1,'','{\"en\":{\"name\":\"56869901_2263721450315376_1107564755880509440_n\"},\"cs\":{\"name\":\"56869901_2263721450315376_1107564755880509440_n\"}}',NULL),(38,18,108,NULL,0,'','{\"en\":{\"name\":\"57154488_2263721566982031_6147697470303371264_n\"},\"cs\":{\"name\":\"57154488_2263721566982031_6147697470303371264_n\"}}',NULL),(39,2,109,NULL,0,'','{\"en\":{\"name\":\"57267652_2263721586982029_1189501496253743104_n\"},\"cs\":{\"name\":\"57267652_2263721586982029_1189501496253743104_n\"}}',NULL),(40,4,104,NULL,0,'','{\"en\":{\"name\":\"56869901_2263721450315376_1107564755880509440_n\"},\"cs\":{\"name\":\"56869901_2263721450315376_1107564755880509440_n\"}}',NULL),(41,5,110,NULL,0,'','{\"en\":{\"name\":\"391593655_811856410945792_7878251948170040325_n\"},\"cs\":{\"name\":\"391593655_811856410945792_7878251948170040325_n\"}}',NULL);
/*!40000 ALTER TABLE `product_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_localization`
--

DROP TABLE IF EXISTS `product_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `public` int(11) DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `setup` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mutationId_productId` (`mutationId`,`productId`),
  KEY `productId` (`productId`),
  CONSTRAINT `product_localization_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_localization_ibfk_2` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_localization`
--

LOCK TABLES `product_localization` WRITE;
/*!40000 ALTER TABLE `product_localization` DISABLE KEYS */;
INSERT INTO `product_localization` VALUES (5,7,2,0,'test product','test product','test product','',NULL,'annotation','<p>content</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(6,7,1,0,'produkt 7','produkt 7','produkt 7','',NULL,'anotace','<p>obsah</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(9,9,2,1,'Diary','Diary','Diary','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(10,9,1,1,'Diář','Diář','Diář','',NULL,'Diář 2024 - Váš osobní plánovač pro dokonalou organizaci','<p>Představujeme Vám Diář 2024, perfektní nástroj pro ty, kteří chtějí mít své dny plně pod kontrolou. Tento elegantní a funkční diář je navržen tak, aby vám pomohl snadno sledovat vaše úkoly, schůzky a osobní cíle.</p>\n<p><strong>Klíčové vlastnosti:</strong></p>\n<ul>\n<li><strong>Týdenní a měsíční přehledy:</strong> Získejte rychlý přehled o svých povinnostech a plánech na celý rok.</li>\n<li><strong>Dostatek místa na poznámky:</strong> Extra stránky pro vaše poznámky, nápady a seznamy úkolů.</li>\n<li><strong>Stylový design:</strong> Kvalitní obálka a moderní grafika, která vypadá skvěle na každém stole.</li>\n<li><strong>Motivační citáty:</strong> Inspirativní citáty na každé stránce, které vám dodají energii a pozitivní náladu.</li>\n<li><strong>Kvalitní papír:</strong> Píšete hladce a bez rozmazávání, ideální pro pero nebo tužku.</li>\n</ul>\n<p>S Diářem 2024 budete mít každý den pod kontrolou a vaše organizace dosáhne nové úrovně. Perfektní pro studenty, profesionály i všechny, kdo chtějí žít produktivněji a s menším stresem. Pořiďte si svůj diář ještě dnes a začněte plánovat úspěšný rok!</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(13,5,2,1,'Energy drink','Energy drink','Energy drink','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(14,5,1,1,'Energy drink','Energy drink','Energy drink','',NULL,'Energetické nápoje PowerBoost - Energie, kterou potřebujete pro maximální výkon','<p>Představujeme vám Energetické nápoje PowerBoost, dokonalý společník pro ty, kteří potřebují extra dávku energie k dosažení svých cílů. Naše nápoje jsou pečlivě formulovány, aby vám poskytly okamžitou a trvalou energii, zlepšily soustředění a výkon, a to vše s osvěžující chutí, která vás povzbudí kdykoliv během dne.</p>\n<p><strong>Klíčové vlastnosti:</strong></p>\n<ul>\n<li><strong>Rychlá a dlouhotrvající energie:</strong> Naše speciální směs kofeinu, taurinu a dalších přírodních stimulantů zajišťuje rychlé zvýšení energie a udržení vysoké hladiny výkonu po delší dobu.</li>\n<li><strong>Zlepšení soustředění a výkonnosti:</strong> Kombinace vitamínů B-komplexu a aminokyselin podporuje mentální jasnost a fyzickou výdrž, ideální pro náročné pracovní dny nebo intenzivní tréninky.</li>\n<li><strong>Osvěžující chuť:</strong> Dostupné v různých lahodných příchutích, které nejenže uspokojí vaše chuťové buňky, ale také vás povzbudí a osvěží.</li>\n<li><strong>Nízký obsah kalorií:</strong> Naše nápoje jsou navrženy tak, aby poskytovaly maximální energii s minimálním množstvím kalorií, což je perfektní pro ty, kteří sledují svůj příjem.</li>\n<li><strong>Praktické balení:</strong> Kompaktní a snadno přenosné plechovky, které si můžete vzít kamkoliv s sebou - do kanceláře, do posilovny nebo na cesty.</li>\n<li><strong>Bez umělých barviv a konzervantů:</strong> PowerBoost nápoje jsou vyrobeny z kvalitních surovin bez zbytečných chemikálií, což zaručuje čistou a přirozenou energii.</li>\n</ul>\n<p><strong>Proč zvolit Energetické nápoje PowerBoost?</strong> Energetické nápoje PowerBoost jsou ideální volbou pro každého, kdo potřebuje dodat svému dni extra šťávu. Ať už jste student, profesionál, sportovec, nebo jen někdo, kdo chce zůstat aktivní a soustředěný, naše nápoje vám poskytnou potřebnou podporu. Naše unikátní složení zaručuje, že získáte nejen rychlou dávku energie, ale také trvalý účinek, který vás udrží v chodu po celý den.</p>\n<p>Vyzkoušejte PowerBoost a zažijte, jaké to je mít energii na dosah ruky, kdykoliv ji potřebujete. Připojte se k tisícům spokojených zákazníků, kteří si již užívají výhod našich energetických nápojů, a objevte svůj plný potenciál. Objednejte si PowerBoost ještě dnes a vstupte do světa neomezené energie!</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(15,4,2,1,'Bussiness cards pack','Bussiness cards pack','Bussiness cards pack','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(16,4,1,1,'Sada vizitek','Sada vizitek','Sada vizitek','',NULL,'První dojem, který vydrží','<p>Představujeme naši Firemní sadu vizitek, dokonalý nástroj pro profesionální prezentaci vaší společnosti. Tyto vizitky kombinují moderní design s vysokou kvalitou tisku, což zaručuje, že zanecháte nezapomenutelný dojem na každého, komu je předáte.</p>\n<p><strong>Klíčové vlastnosti:</strong></p>\n<ul>\n<li><strong>Prémiový materiál:</strong> Vysoce kvalitní papír, který je pevný a odolný, zajišťuje dlouhou životnost a luxusní pocit při každém doteku.</li>\n<li><strong>Profesionální design:</strong> Možnost přizpůsobení s vaším firemním logem, barvami a kontaktními údaji, které dokonale reprezentují vaši značku.</li>\n<li><strong>Vysoká kvalita tisku:</strong> Ostrý a jasný tisk, který zaručuje, že vaše informace budou snadno čitelné a profesionální.</li>\n<li><strong>Různé formáty:</strong> Dostupné v různých velikostech a stylech, aby vyhověly vašim specifickým potřebám a preferencím.</li>\n<li><strong>Rychlé dodání:</strong> Efektivní výroba a rychlé dodání, abyste měli své vizitky vždy včas k dispozici.</li>\n</ul>\n<p>Naše Firemní sada vizitek je ideální pro obchodní schůzky, konference a další profesionální příležitosti. Podpořte image své společnosti a zajistěte, že váš první dojem bude vždy pozitivní. Objednejte si svou sadu ještě dnes a ukážte světu, že vaše firma je tou správnou volbou!</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(17,2,2,1,'Sport tee','Sport tee','Sport tee','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(18,2,1,1,'SK sportovní tričko','SK sportovní tričko','SK sportovní tričko','',NULL,'Komfort a styl pro aktivní tým','<p>Představujeme naše Firemní sportovní tričko, ideální volbu pro společnosti, které chtějí podpořit týmového ducha a zároveň zajistit maximální komfort během sportovních aktivit. Toto tričko spojuje moderní design s funkčními vlastnostmi, které ocení každý člen vašeho týmu.</p>\n<p><strong>Klíčové vlastnosti:</strong></p>\n<ul>\n<li><strong>Prodyšný materiál:</strong> Vysoce kvalitní tkanina, která odvádí pot a udržuje vás v suchu i při intenzivním cvičení.</li>\n<li><strong>Ergonomický střih:</strong> Navrženo pro maximální pohodlí a volnost pohybu, což zaručuje, že se budete cítit skvěle po celý den.</li>\n<li><strong>Stylový design:</strong> Moderní a elegantní vzhled s možností firemního loga, který podtrhne profesionální image vaší firmy.</li>\n<li><strong>Rychleschnoucí:</strong> Materiál, který rychle schne, což oceníte při každodenním používání.</li>\n<li><strong>Odolnost:</strong> Vyrobeno z materiálů, které jsou odolné vůči opotřebení, zaručující dlouhou životnost trička.</li>\n</ul>\n<p>Naše Firemní sportovní tričko je perfektní pro teambuildingové akce, firemní sportovní turnaje nebo jako součást každodenního pracovního oděvu. Podpořte jednotu svého týmu a současně zajistěte pohodlí a styl. Objednejte ještě dnes a zažijte rozdíl, který může kvalitní sportovní tričko udělat!</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{\"base\":{\"0\":{\"mainImage\":\"90\"}}}','{}'),(19,10,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(20,10,1,1,'VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','VZDUCHOVKA HAMMERLI HUNTER FORCE 900 COMBO 4,5MM1','',NULL,'Nejslavnější model pružinovky se spodním nátahem od německého výrobce Umarex. Mohutná pažba s ozdobnou rytinou často připomíná reálnou pušku. Hledí je vybaveno TRU-GLO systémem, což jsou světlovodné vlákna, které pomáhají se zamířením při zhoršených podmínkách.a','<p><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Nejslavnější model pružinovky se spodním nátahem od německého výrobce </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Umarex</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">. Mohutná pažba s ozdobnou rytinou často připomíná reálnou pušku. Hledí je vybaveno </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">TRU-GLO</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> systémem, což jsou světlovodné vlákna, které </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">pomáhají se zamířením</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> při zhoršených podmínkách.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Vzduchovka je vybavena a </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">automatickou pojistkou</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> proti náhodnému výstřelu. Tělo vzduchovky je vybaveno </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">11mm rybinou</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> pro usazení puškohledu. Výrobce v setu dodává svůj </span><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">puškohled 6x42</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Spodní nátah</strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\"> zaručí přesnost i po delší době používání.</span><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><br style=\"box-sizing: border-box; caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\" /><strong style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: bold; font-stretch: inherit; line-height: inherit; vertical-align: baseline; caret-color: #3e3e3e; color: #3e3e3e; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; text-decoration: none;\">Naše zkušenost: </strong><span style=\"caret-color: #3e3e3e; color: #3e3e3e; font-family: \'Open Sans\'; font-size: 16px; font-style: normal; font-variant-caps: normal; font-weight: normal; letter-spacing: normal; orphans: auto; text-align: start; text-indent: 0px; text-transform: none; white-space: normal; widows: auto; word-spacing: 0px; -webkit-text-size-adjust: auto; -webkit-text-stroke-width: 0px; background-color: #ffffff; text-decoration: none; display: inline !important; float: none;\">Opravdu jedna z nejoblíbenějších vzduchovek u nás. Vzduchovka má sice větší hmotnost, ale pro pořádné chlapy to není žádný problém.</span></p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(31,16,2,0,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(32,16,1,0,'Produkt test','Produkt test','Produkt test','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(35,18,2,1,'','','','',NULL,'','','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}'),(36,18,1,1,'SK tričko','SK tričko','SK tričko','',NULL,'Styl a pohodlí pro každý den','<p>Představujeme naše Firemní tričko, ideální volbu pro společnosti, které chtějí podpořit týmového ducha a zároveň zajistit maximální pohodlí během celého dne. Toto tričko spojuje moderní design s kvalitními materiály, což ocení každý člen vašeho týmu.</p>\n<p><strong>Klíčové vlastnosti:</strong></p>\n<ul>\n<li><strong>Kvalitní materiál:</strong> Měkká a pohodlná tkanina, která je příjemná na dotek a ideální pro celodenní nošení.</li>\n<li><strong>Ergonomický střih:</strong> Navrženo pro maximální pohodlí a elegantní vzhled, který se hodí do každého prostředí.</li>\n<li><strong>Stylový design:</strong> Moderní a vkusný vzhled s možností firemního loga, který podtrhne profesionální image vaší firmy.</li>\n<li><strong>Odolnost:</strong> Vyrobeno z materiálů, které jsou odolné vůči opotřebení a praní, zaručující dlouhou životnost trička.</li>\n<li><strong>Různé velikosti a barvy:</strong> Dostupné v široké škále velikostí a barevných variant, aby vyhovělo každému vkusu.</li>\n</ul>\n<p>Naše Firemní tričko je perfektní pro každodenní nošení, firemní akce nebo jako součást uniformy. Podpořte jednotu svého týmu a současně zajistěte pohodlí a styl. Objednejte ještě dnes a zažijte rozdíl, který může kvalitní tričko udělat!</p>','{\"inheritCategories\":false,\"inheritFiles\":false,\"inheritPages\":false}','{}','{}');
/*!40000 ALTER TABLE `product_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_parameter`
--

DROP TABLE IF EXISTS `product_parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_parameter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `parameterId` int(11) NOT NULL,
  `parameterValueId` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_parameterId_parameterValueId` (`productId`,`parameterId`,`parameterValueId`),
  KEY `parameterId` (`parameterId`),
  KEY `parameterValueId` (`parameterValueId`),
  CONSTRAINT `product_parameter_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_parameter_ibfk_5` FOREIGN KEY (`parameterId`) REFERENCES `parameter` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `product_parameter_ibfk_6` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14743 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_parameter`
--

LOCK TABLES `product_parameter` WRITE;
/*!40000 ALTER TABLE `product_parameter` DISABLE KEYS */;
INSERT INTO `product_parameter` VALUES (14222,2,113,6564),(14218,4,113,6565),(14219,4,114,6570),(14220,4,114,6571),(14265,4,116,6580),(14258,4,117,6577),(14259,4,118,6578),(14509,4,119,6585),(14261,4,120,6579),(14214,5,113,6566),(14215,5,114,6570),(14216,5,114,6571),(14217,5,119,6575),(14141,7,113,6565),(14142,7,114,6570),(14225,7,119,6572),(14201,9,113,6564),(14202,9,114,6567),(14233,9,117,6577),(14367,9,118,6581),(14203,9,119,6572),(14742,9,121,6583),(14512,10,121,6583),(14733,18,121,6582);
/*!40000 ALTER TABLE `product_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_product`
--

DROP TABLE IF EXISTS `product_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mainProductId` int(11) NOT NULL,
  `attachedProductId` int(11) NOT NULL,
  `type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productMainId_productId_type` (`mainProductId`,`attachedProductId`,`type`),
  KEY `productId` (`attachedProductId`),
  KEY `mainProductId` (`mainProductId`),
  CONSTRAINT `product_product_ibfk_1` FOREIGN KEY (`mainProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_product_ibfk_4` FOREIGN KEY (`attachedProductId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_product`
--

LOCK TABLES `product_product` WRITE;
/*!40000 ALTER TABLE `product_product` DISABLE KEYS */;
INSERT INTO `product_product` VALUES (2,16,16,'normal',0),(3,2,18,'normal',0),(4,18,2,'normal',0);
/*!40000 ALTER TABLE `product_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_review`
--

DROP TABLE IF EXISTS `product_review`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_review` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `userId` int(11) DEFAULT NULL,
  `isWebMaster` tinyint(1) NOT NULL DEFAULT '0',
  `isMain` tinyint(1) NOT NULL COMMENT 'main review for user',
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `email` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `stars` int(11) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `idtree_idx` (`productId`),
  KEY `userId` (`userId`),
  CONSTRAINT `product_review_ibfk_3` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_review_ibfk_4` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_review`
--

LOCK TABLES `product_review` WRITE;
/*!40000 ALTER TABLE `product_review` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_review` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_tree`
--

DROP TABLE IF EXISTS `product_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_treeId` (`productId`,`treeId`),
  KEY `treeId` (`treeId`),
  CONSTRAINT `product_tree_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_tree_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13716 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_tree`
--

LOCK TABLES `product_tree` WRITE;
/*!40000 ALTER TABLE `product_tree` DISABLE KEYS */;
INSERT INTO `product_tree` VALUES (13693,7,94,0),(13700,4,94,0),(13701,2,94,0),(13703,4,77,1),(13709,16,21,0),(13713,5,460,0),(13714,9,77,0),(13715,18,78,0);
/*!40000 ALTER TABLE `product_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant`
--

DROP TABLE IF EXISTS `product_variant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `param1ValueId` int(11) DEFAULT NULL,
  `param2ValueId` int(11) DEFAULT NULL,
  `ean` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` datetime DEFAULT NULL,
  `createdBy` int(11) DEFAULT NULL,
  `edited` datetime DEFAULT NULL,
  `editedBy` int(11) DEFAULT NULL,
  `sort` int(11) NOT NULL DEFAULT '0',
  `soldCount` int(11) NOT NULL,
  `isInDiscount` int(11) DEFAULT NULL,
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_param1ValueId_param2ValueId` (`productId`,`param1ValueId`,`param2ValueId`),
  UNIQUE KEY `extId` (`extId`),
  KEY `param2ValueId` (`param2ValueId`),
  KEY `param1ValueId` (`param1ValueId`),
  CONSTRAINT `product_variant_ibfk_13` FOREIGN KEY (`param2ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
  CONSTRAINT `product_variant_ibfk_14` FOREIGN KEY (`param1ValueId`) REFERENCES `parameter_value` (`id`) ON DELETE NO ACTION ON UPDATE CASCADE,
  CONSTRAINT `product_variant_ibfk_2` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant`
--

LOCK TABLES `product_variant` WRITE;
/*!40000 ALTER TABLE `product_variant` DISABLE KEYS */;
INSERT INTO `product_variant` VALUES (44,9,6564,NULL,'0','','2021-06-10 16:04:45',12,'2025-04-09 17:23:03',4,0,0,NULL,NULL),(47,7,NULL,NULL,'0','','2021-06-10 22:40:43',3,'2025-04-10 10:30:12',42,0,0,NULL,NULL),(50,9,6565,NULL,'','','2021-06-23 09:38:47',1,'2025-04-09 17:23:03',4,1,0,NULL,NULL),(51,5,NULL,NULL,'0','','2021-06-28 08:34:05',3,'2025-04-09 17:23:20',4,0,0,NULL,NULL),(52,4,NULL,NULL,'*********','Kód','2021-06-28 08:35:18',3,'2024-07-26 09:40:04',11,0,0,NULL,NULL),(53,2,NULL,NULL,'0','','2021-06-28 08:36:31',3,'2024-07-26 09:47:46',11,0,0,NULL,NULL),(56,4,6564,NULL,'','','2021-07-01 06:45:10',12,'2024-07-26 09:40:04',11,1,0,NULL,NULL),(57,10,NULL,NULL,'0','','2021-07-01 17:22:44',1,'2025-04-09 14:29:35',42,0,0,NULL,NULL),(59,9,6566,NULL,'','','2021-07-06 10:23:26',3,'2025-04-09 17:23:03',4,2,0,NULL,NULL),(65,16,NULL,NULL,'0','','2024-07-19 09:56:14',NULL,'2024-07-26 09:49:48',11,0,0,NULL,NULL),(67,18,NULL,NULL,'0','','2024-07-25 16:38:55',NULL,'2024-07-26 09:47:55',11,0,0,NULL,NULL);
/*!40000 ALTER TABLE `product_variant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant_localization`
--

DROP TABLE IF EXISTS `product_variant_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `variantId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `active` int(11) NOT NULL DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `variantId_mutationId` (`variantId`,`mutationId`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `product_variant_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_localization_ibfk_2` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant_localization`
--

LOCK TABLES `product_variant_localization` WRITE;
/*!40000 ALTER TABLE `product_variant_localization` DISABLE KEYS */;
INSERT INTO `product_variant_localization` VALUES (72,44,1,1,NULL),(73,44,2,1,NULL),(78,47,1,1,NULL),(79,47,2,1,NULL),(84,50,1,1,NULL),(85,50,2,0,NULL),(86,51,1,1,NULL),(87,51,2,1,NULL),(88,52,1,1,NULL),(89,52,2,1,NULL),(90,53,1,1,NULL),(91,53,2,1,NULL),(96,56,1,0,NULL),(97,56,2,0,NULL),(98,57,1,1,NULL),(99,57,2,0,NULL),(102,59,1,1,NULL),(103,59,2,1,NULL),(114,65,1,1,NULL),(115,65,2,1,NULL),(118,67,1,1,NULL),(119,67,2,1,NULL);
/*!40000 ALTER TABLE `product_variant_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variant_price`
--

DROP TABLE IF EXISTS `product_variant_price`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variant_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `priceLevelId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `productVariantId` int(11) NOT NULL,
  `price_amount` decimal(18,4) NOT NULL,
  `price_currency` char(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  KEY `productVariantId` (`productVariantId`),
  KEY `priceLevelId` (`priceLevelId`),
  KEY `productId` (`productId`),
  CONSTRAINT `product_variant_price_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_2` FOREIGN KEY (`productVariantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_3` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `product_variant_price_ibfk_4` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variant_price`
--

LOCK TABLES `product_variant_price` WRITE;
/*!40000 ALTER TABLE `product_variant_price` DISABLE KEYS */;
INSERT INTO `product_variant_price` VALUES (13,1,1,9,44,180.0000,'CZK'),(14,1,2,9,44,130.0000,'CZK'),(15,2,1,9,44,20.0000,'EUR'),(16,2,2,9,44,19.0000,'EUR'),(25,1,1,9,50,150.0000,'CZK'),(26,1,2,9,50,1000.0000,'CZK'),(27,2,1,9,50,10.0000,'EUR'),(28,2,2,9,50,10.0000,'EUR'),(33,1,1,7,47,2000.0000,'CZK'),(34,1,2,7,47,1980.0000,'CZK'),(35,2,1,7,47,10.0000,'EUR'),(36,2,2,7,47,20.0000,'EUR'),(37,1,1,5,51,2500.0000,'CZK'),(38,1,2,5,51,2400.0000,'CZK'),(39,2,1,5,51,0.0000,'EUR'),(40,2,2,5,51,0.0000,'EUR'),(41,1,1,4,52,50.0000,'CZK'),(42,1,2,4,52,40.0000,'CZK'),(43,2,1,4,52,0.0000,'EUR'),(44,2,2,4,52,0.0000,'EUR'),(49,1,1,2,53,1000.0000,'CZK'),(50,1,2,2,53,900.0000,'CZK'),(51,2,1,2,53,0.0000,'EUR'),(52,2,2,2,53,0.0000,'EUR'),(61,1,1,4,56,10.0000,'CZK'),(62,1,2,4,56,10.0000,'CZK'),(63,2,1,4,56,0.0000,'EUR'),(64,2,2,4,56,0.0000,'EUR'),(65,1,1,9,59,100.0000,'CZK'),(66,1,2,9,59,100.0000,'CZK'),(67,2,1,9,59,10.0000,'EUR'),(68,2,2,9,59,10.0000,'EUR'),(73,1,1,10,57,5281.0000,'CZK'),(74,1,2,10,57,0.0000,'CZK'),(75,2,1,10,57,0.0000,'EUR'),(76,2,2,10,57,0.0000,'EUR'),(97,1,1,16,65,100.0000,'CZK'),(98,1,2,16,65,0.0000,'CZK'),(99,2,1,16,65,100.0000,'EUR'),(100,2,2,16,65,0.0000,'EUR'),(105,1,1,18,67,200.0000,'CZK'),(106,1,2,18,67,250.0000,'CZK'),(107,2,1,18,67,20.0000,'EUR'),(108,2,2,18,67,25.0000,'EUR');
/*!40000 ALTER TABLE `product_variant_price` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redirect`
--

DROP TABLE IF EXISTS `redirect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `redirect` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `oldUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `newUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `code` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `oldUrl` (`oldUrl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redirect`
--

LOCK TABLES `redirect` WRITE;
/*!40000 ALTER TABLE `redirect` DISABLE KEYS */;
/*!40000 ALTER TABLE `redirect` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink`
--

DROP TABLE IF EXISTS `seolink`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFieldsJson` longtext COLLATE utf8mb4_unicode_520_ci,
  `parameterValuesIds` varchar(768) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `parameterValuesIds` (`parameterValuesIds`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink`
--

LOCK TABLES `seolink` WRITE;
/*!40000 ALTER TABLE `seolink` DISABLE KEYS */;
INSERT INTO `seolink` VALUES (1,'','{}',NULL);
/*!40000 ALTER TABLE `seolink` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink_localization`
--

DROP TABLE IF EXISTS `seolink_localization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `seoLinkId` int(11) NOT NULL,
  `name` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameAnchor` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `nameTitle` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_520_ci,
  `keywords` text COLLATE utf8mb4_unicode_520_ci,
  `title` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customFieldsJson` longtext COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `mutationId` (`mutationId`) USING BTREE,
  KEY `seoLinkId` (`seoLinkId`) USING BTREE,
  CONSTRAINT `seolink_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `seolink_localization_ibfk_2` FOREIGN KEY (`seoLinkId`) REFERENCES `seolink` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink_localization`
--

LOCK TABLES `seolink_localization` WRITE;
/*!40000 ALTER TABLE `seolink_localization` DISABLE KEYS */;
INSERT INTO `seolink_localization` VALUES (1,1,1,'','','','','',NULL,'{}','{}');
/*!40000 ALTER TABLE `seolink_localization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seolink_x_parameter_value`
--

DROP TABLE IF EXISTS `seolink_x_parameter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seolink_x_parameter_value` (
  `seolinkId` int(11) NOT NULL,
  `parameterValueId` int(11) NOT NULL,
  PRIMARY KEY (`seolinkId`,`parameterValueId`) USING BTREE,
  KEY `seolinkId` (`seolinkId`) USING BTREE,
  KEY `parameterValueId` (`parameterValueId`) USING BTREE,
  CONSTRAINT `seolink_x_parameter_value_ibfk_1` FOREIGN KEY (`seolinkId`) REFERENCES `seolink` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `seolink_x_parameter_value_ibfk_2` FOREIGN KEY (`parameterValueId`) REFERENCES `parameter_value` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seolink_x_parameter_value`
--

LOCK TABLES `seolink_x_parameter_value` WRITE;
/*!40000 ALTER TABLE `seolink_x_parameter_value` DISABLE KEYS */;
/*!40000 ALTER TABLE `seolink_x_parameter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `state`
--

DROP TABLE IF EXISTS `state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` tinyint(1) NOT NULL DEFAULT '1',
  `vatRates_standard` decimal(4,2) DEFAULT NULL,
  `vatRates_reduced` decimal(4,2) DEFAULT NULL,
  `vatRates_secondReduced` decimal(4,2) DEFAULT NULL,
  `vatRates_superReduced` decimal(4,2) DEFAULT NULL,
  `vatRates_parking` decimal(4,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=254 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `state`
--

LOCK TABLES `state` WRITE;
/*!40000 ALTER TABLE `state` DISABLE KEYS */;
INSERT INTO `state` VALUES (9,'AD','Andorra',1,21.00,15.00,10.00,NULL,NULL),(10,'AE','United Arab Emirates',1,21.00,15.00,10.00,NULL,NULL),(11,'AF','Afghanistan',1,21.00,15.00,10.00,NULL,NULL),(12,'AG','Antigua and Barbuda',1,21.00,15.00,10.00,NULL,NULL),(13,'AI','Anguilla',1,21.00,15.00,10.00,NULL,NULL),(14,'AL','Albania',1,21.00,15.00,10.00,NULL,NULL),(15,'AM','Armenia',1,21.00,15.00,10.00,NULL,NULL),(16,'AN','Netherlands Antilles',1,21.00,15.00,10.00,NULL,NULL),(17,'AO','Angola',1,21.00,15.00,10.00,NULL,NULL),(18,'AQ','Antarctica',1,21.00,15.00,10.00,NULL,NULL),(19,'AR','Argentina',1,21.00,15.00,10.00,NULL,NULL),(20,'AS','American Samoa',1,21.00,15.00,10.00,NULL,NULL),(21,'AT','Austria',1,21.00,15.00,10.00,NULL,NULL),(22,'AU','Australia',1,21.00,15.00,10.00,NULL,NULL),(23,'AW','Aruba',1,21.00,15.00,10.00,NULL,NULL),(24,'AZ','Azerbaijan',1,21.00,15.00,10.00,NULL,NULL),(25,'BA','Bosnia and Herzegovina',1,21.00,15.00,10.00,NULL,NULL),(26,'BB','Barbados',1,21.00,15.00,10.00,NULL,NULL),(27,'BD','Bangladesh',1,21.00,15.00,10.00,NULL,NULL),(28,'BE','Belgium',1,21.00,15.00,10.00,NULL,NULL),(29,'BF','Burkina Faso',1,21.00,15.00,10.00,NULL,NULL),(30,'BG','Bulgaria',1,21.00,15.00,10.00,NULL,NULL),(31,'BH','Bahrain',1,21.00,15.00,10.00,NULL,NULL),(32,'BI','Burundi',1,21.00,15.00,10.00,NULL,NULL),(33,'BJ','Benin',1,21.00,15.00,10.00,NULL,NULL),(34,'BM','Bermuda',1,21.00,15.00,10.00,NULL,NULL),(35,'BN','Brunei',1,21.00,15.00,10.00,NULL,NULL),(36,'BO','Bolivia',1,21.00,15.00,10.00,NULL,NULL),(37,'BR','Brazil',1,21.00,15.00,10.00,NULL,NULL),(38,'BS','Bahamas',1,21.00,15.00,10.00,NULL,NULL),(39,'BT','Bhutan',1,21.00,15.00,10.00,NULL,NULL),(40,'BV','Bouvet Island',1,21.00,15.00,10.00,NULL,NULL),(41,'BW','Botswana',1,21.00,15.00,10.00,NULL,NULL),(42,'BY','Belarus',1,21.00,15.00,10.00,NULL,NULL),(43,'BZ','Belize',1,21.00,15.00,10.00,NULL,NULL),(44,'CA','Canada',1,21.00,15.00,10.00,NULL,NULL),(45,'CC','Cocos [Keeling] Islands',1,21.00,15.00,10.00,NULL,NULL),(46,'CD','Congo [DRC]',1,21.00,15.00,10.00,NULL,NULL),(47,'CF','Central African Republic',1,21.00,15.00,10.00,NULL,NULL),(48,'CG','Congo [Republic]',1,21.00,15.00,10.00,NULL,NULL),(49,'CH','Switzerland',1,21.00,15.00,10.00,NULL,NULL),(50,'CI','Côte d\'Ivoire',1,21.00,15.00,10.00,NULL,NULL),(51,'CK','Cook Islands',1,21.00,15.00,10.00,NULL,NULL),(52,'CL','Chile',1,21.00,15.00,10.00,NULL,NULL),(53,'CM','Cameroon',1,21.00,15.00,10.00,NULL,NULL),(54,'CN','China',1,21.00,15.00,10.00,NULL,NULL),(55,'CO','Colombia',1,21.00,15.00,10.00,NULL,NULL),(56,'CR','Costa Rica',1,21.00,15.00,10.00,NULL,NULL),(57,'CU','Cuba',1,21.00,15.00,10.00,NULL,NULL),(58,'CV','Cape Verde',1,21.00,15.00,10.00,NULL,NULL),(59,'CX','Christmas Island',1,21.00,15.00,10.00,NULL,NULL),(60,'CY','Cyprus',1,21.00,15.00,10.00,NULL,NULL),(61,'CZ','Czech Republic',1,21.00,15.00,10.00,NULL,NULL),(62,'DE','Germany',1,21.00,15.00,10.00,NULL,NULL),(63,'DJ','Djibouti',1,21.00,15.00,10.00,NULL,NULL),(64,'DK','Denmark',1,21.00,15.00,10.00,NULL,NULL),(65,'DM','Dominica',1,21.00,15.00,10.00,NULL,NULL),(66,'DO','Dominican Republic',1,21.00,15.00,10.00,NULL,NULL),(67,'DZ','Algeria',1,21.00,15.00,10.00,NULL,NULL),(68,'EC','Ecuador',1,21.00,15.00,10.00,NULL,NULL),(69,'EE','Estonia',1,21.00,15.00,10.00,NULL,NULL),(70,'EG','Egypt',1,21.00,15.00,10.00,NULL,NULL),(71,'EH','Western Sahara',1,21.00,15.00,10.00,NULL,NULL),(72,'ER','Eritrea',1,21.00,15.00,10.00,NULL,NULL),(73,'ES','Spain',1,21.00,15.00,10.00,NULL,NULL),(74,'ET','Ethiopia',1,21.00,15.00,10.00,NULL,NULL),(75,'FI','Finland',1,21.00,15.00,10.00,NULL,NULL),(76,'FJ','Fiji',1,21.00,15.00,10.00,NULL,NULL),(77,'FK','Falkland Islands [Islas Malvinas]',1,21.00,15.00,10.00,NULL,NULL),(78,'FM','Micronesia',1,21.00,15.00,10.00,NULL,NULL),(79,'FO','Faroe Islands',1,21.00,15.00,10.00,NULL,NULL),(80,'FR','France',1,21.00,15.00,10.00,NULL,NULL),(81,'GA','Gabon',1,21.00,15.00,10.00,NULL,NULL),(82,'GB','United Kingdom',1,21.00,15.00,10.00,NULL,NULL),(83,'GD','Grenada',1,21.00,15.00,10.00,NULL,NULL),(84,'GE','Georgia',1,21.00,15.00,10.00,NULL,NULL),(85,'GF','French Guiana',1,21.00,15.00,10.00,NULL,NULL),(86,'GG','Guernsey',1,21.00,15.00,10.00,NULL,NULL),(87,'GH','Ghana',1,21.00,15.00,10.00,NULL,NULL),(88,'GI','Gibraltar',1,21.00,15.00,10.00,NULL,NULL),(89,'GL','Greenland',1,21.00,15.00,10.00,NULL,NULL),(90,'GM','Gambia',1,21.00,15.00,10.00,NULL,NULL),(91,'GN','Guinea',1,21.00,15.00,10.00,NULL,NULL),(92,'GP','Guadeloupe',1,21.00,15.00,10.00,NULL,NULL),(93,'GQ','Equatorial Guinea',1,21.00,15.00,10.00,NULL,NULL),(94,'GR','Greece',1,21.00,15.00,10.00,NULL,NULL),(95,'GS','South Georgia and the South Sandwich Islands',1,21.00,15.00,10.00,NULL,NULL),(96,'GT','Guatemala',1,21.00,15.00,10.00,NULL,NULL),(97,'GU','Guam',1,21.00,15.00,10.00,NULL,NULL),(98,'GW','Guinea-Bissau',1,21.00,15.00,10.00,NULL,NULL),(99,'GY','Guyana',1,21.00,15.00,10.00,NULL,NULL),(100,'GZ','Gaza Strip',1,21.00,15.00,10.00,NULL,NULL),(101,'HK','Hong Kong',1,21.00,15.00,10.00,NULL,NULL),(102,'HM','Heard Island and McDonald Islands',1,21.00,15.00,10.00,NULL,NULL),(103,'HN','Honduras',1,21.00,15.00,10.00,NULL,NULL),(104,'HR','Croatia',1,21.00,15.00,10.00,NULL,NULL),(105,'HT','Haiti',1,21.00,15.00,10.00,NULL,NULL),(106,'HU','Hungary',1,21.00,15.00,10.00,NULL,NULL),(107,'ID','Indonesia',1,21.00,15.00,10.00,NULL,NULL),(108,'IE','Ireland',1,21.00,15.00,10.00,NULL,NULL),(109,'IL','Israel',1,21.00,15.00,10.00,NULL,NULL),(110,'IM','Isle of Man',1,21.00,15.00,10.00,NULL,NULL),(111,'IN','India',1,21.00,15.00,10.00,NULL,NULL),(112,'IO','British Indian Ocean Territory',1,21.00,15.00,10.00,NULL,NULL),(113,'IQ','Iraq',1,21.00,15.00,10.00,NULL,NULL),(114,'IR','Iran',1,21.00,15.00,10.00,NULL,NULL),(115,'IS','Iceland',1,21.00,15.00,10.00,NULL,NULL),(116,'IT','Italy',1,21.00,15.00,10.00,NULL,NULL),(117,'JE','Jersey',1,21.00,15.00,10.00,NULL,NULL),(118,'JM','Jamaica',1,21.00,15.00,10.00,NULL,NULL),(119,'JO','Jordan',1,21.00,15.00,10.00,NULL,NULL),(120,'JP','Japan',1,21.00,15.00,10.00,NULL,NULL),(121,'KE','Kenya',1,21.00,15.00,10.00,NULL,NULL),(122,'KG','Kyrgyzstan',1,21.00,15.00,10.00,NULL,NULL),(123,'KH','Cambodia',1,21.00,15.00,10.00,NULL,NULL),(124,'KI','Kiribati',1,21.00,15.00,10.00,NULL,NULL),(125,'KM','Comoros',1,21.00,15.00,10.00,NULL,NULL),(126,'KN','Saint Kitts and Nevis',1,21.00,15.00,10.00,NULL,NULL),(127,'KP','North Korea',1,21.00,15.00,10.00,NULL,NULL),(128,'KR','South Korea',1,21.00,15.00,10.00,NULL,NULL),(129,'KW','Kuwait',1,21.00,15.00,10.00,NULL,NULL),(130,'KY','Cayman Islands',1,21.00,15.00,10.00,NULL,NULL),(131,'KZ','Kazakhstan',1,21.00,15.00,10.00,NULL,NULL),(132,'LA','Laos',1,21.00,15.00,10.00,NULL,NULL),(133,'LB','Lebanon',1,21.00,15.00,10.00,NULL,NULL),(134,'LC','Saint Lucia',1,21.00,15.00,10.00,NULL,NULL),(135,'LI','Liechtenstein',1,21.00,15.00,10.00,NULL,NULL),(136,'LK','Sri Lanka',1,21.00,15.00,10.00,NULL,NULL),(137,'LR','Liberia',1,21.00,15.00,10.00,NULL,NULL),(138,'LS','Lesotho',1,21.00,15.00,10.00,NULL,NULL),(139,'LT','Lithuania',1,21.00,15.00,10.00,NULL,NULL),(140,'LU','Luxembourg',1,21.00,15.00,10.00,NULL,NULL),(141,'LV','Latvia',1,21.00,15.00,10.00,NULL,NULL),(142,'LY','Libya',1,21.00,15.00,10.00,NULL,NULL),(143,'MA','Morocco',1,21.00,15.00,10.00,NULL,NULL),(144,'MC','Monaco',1,21.00,15.00,10.00,NULL,NULL),(145,'MD','Moldova',1,21.00,15.00,10.00,NULL,NULL),(146,'ME','Montenegro',1,21.00,15.00,10.00,NULL,NULL),(147,'MG','Madagascar',1,21.00,15.00,10.00,NULL,NULL),(148,'MH','Marshall Islands',1,21.00,15.00,10.00,NULL,NULL),(149,'MK','Macedonia [FYROM]',1,21.00,15.00,10.00,NULL,NULL),(150,'ML','Mali',1,21.00,15.00,10.00,NULL,NULL),(151,'MM','Myanmar [Burma]',1,21.00,15.00,10.00,NULL,NULL),(152,'MN','Mongolia',1,21.00,15.00,10.00,NULL,NULL),(153,'MO','Macau',1,21.00,15.00,10.00,NULL,NULL),(154,'MP','Northern Mariana Islands',1,21.00,15.00,10.00,NULL,NULL),(155,'MQ','Martinique',1,21.00,15.00,10.00,NULL,NULL),(156,'MR','Mauritania',1,21.00,15.00,10.00,NULL,NULL),(157,'MS','Montserrat',1,21.00,15.00,10.00,NULL,NULL),(158,'MT','Malta',1,21.00,15.00,10.00,NULL,NULL),(159,'MU','Mauritius',1,21.00,15.00,10.00,NULL,NULL),(160,'MV','Maldives',1,21.00,15.00,10.00,NULL,NULL),(161,'MW','Malawi',1,21.00,15.00,10.00,NULL,NULL),(162,'MX','Mexico',1,21.00,15.00,10.00,NULL,NULL),(163,'MY','Malaysia',1,21.00,15.00,10.00,NULL,NULL),(164,'MZ','Mozambique',1,21.00,15.00,10.00,NULL,NULL),(165,'NA','Namibia',1,21.00,15.00,10.00,NULL,NULL),(166,'NC','New Caledonia',1,21.00,15.00,10.00,NULL,NULL),(167,'NE','Niger',1,21.00,15.00,10.00,NULL,NULL),(168,'NF','Norfolk Island',1,21.00,15.00,10.00,NULL,NULL),(169,'NG','Nigeria',1,21.00,15.00,10.00,NULL,NULL),(170,'NI','Nicaragua',1,21.00,15.00,10.00,NULL,NULL),(171,'NL','Netherlands',1,21.00,15.00,10.00,NULL,NULL),(172,'NO','Norway',1,21.00,15.00,10.00,NULL,NULL),(173,'NP','Nepal',1,21.00,15.00,10.00,NULL,NULL),(174,'NR','Nauru',1,21.00,15.00,10.00,NULL,NULL),(175,'NU','Niue',1,21.00,15.00,10.00,NULL,NULL),(176,'NZ','New Zealand',1,21.00,15.00,10.00,NULL,NULL),(177,'OM','Oman',1,21.00,15.00,10.00,NULL,NULL),(178,'PA','Panama',1,21.00,15.00,10.00,NULL,NULL),(179,'PE','Peru',1,21.00,15.00,10.00,NULL,NULL),(180,'PF','French Polynesia',1,21.00,15.00,10.00,NULL,NULL),(181,'PG','Papua New Guinea',1,21.00,15.00,10.00,NULL,NULL),(182,'PH','Philippines',1,21.00,15.00,10.00,NULL,NULL),(183,'PK','Pakistan',1,21.00,15.00,10.00,NULL,NULL),(184,'PL','Poland',1,21.00,15.00,10.00,NULL,NULL),(185,'PM','Saint Pierre and Miquelon',1,21.00,15.00,10.00,NULL,NULL),(186,'PN','Pitcairn Islands',1,21.00,15.00,10.00,NULL,NULL),(187,'PR','Puerto Rico',1,21.00,15.00,10.00,NULL,NULL),(188,'PS','Palestinian Territories',1,21.00,15.00,10.00,NULL,NULL),(189,'PT','Portugal',1,21.00,15.00,10.00,NULL,NULL),(190,'PW','Palau',1,21.00,15.00,10.00,NULL,NULL),(191,'PY','Paraguay',1,21.00,15.00,10.00,NULL,NULL),(192,'QA','Qatar',1,21.00,15.00,10.00,NULL,NULL),(193,'RE','Réunion',1,21.00,15.00,10.00,NULL,NULL),(194,'RO','Romania',1,21.00,15.00,10.00,NULL,NULL),(195,'RS','Serbia',1,21.00,15.00,10.00,NULL,NULL),(196,'RU','Russia',1,21.00,15.00,10.00,NULL,NULL),(197,'RW','Rwanda',1,21.00,15.00,10.00,NULL,NULL),(198,'SA','Saudi Arabia',1,21.00,15.00,10.00,NULL,NULL),(199,'SB','Solomon Islands',1,21.00,15.00,10.00,NULL,NULL),(200,'SC','Seychelles',1,21.00,15.00,10.00,NULL,NULL),(201,'SD','Sudan',1,21.00,15.00,10.00,NULL,NULL),(202,'SE','Sweden',1,21.00,15.00,10.00,NULL,NULL),(203,'SG','Singapore',1,21.00,15.00,10.00,NULL,NULL),(204,'SH','Saint Helena',1,21.00,15.00,10.00,NULL,NULL),(205,'SI','Slovenia',1,21.00,15.00,10.00,NULL,NULL),(206,'SJ','Svalbard and Jan Mayen',1,21.00,15.00,10.00,NULL,NULL),(207,'SK','Slovakia',1,21.00,15.00,10.00,NULL,NULL),(208,'SL','Sierra Leone',1,21.00,15.00,10.00,NULL,NULL),(209,'SM','San Marino',1,21.00,15.00,10.00,NULL,NULL),(210,'SN','Senegal',1,21.00,15.00,10.00,NULL,NULL),(211,'SO','Somalia',1,21.00,15.00,10.00,NULL,NULL),(212,'SR','Suriname',1,21.00,15.00,10.00,NULL,NULL),(213,'ST','São Tomé and Príncipe',1,21.00,15.00,10.00,NULL,NULL),(214,'SV','El Salvador',1,21.00,15.00,10.00,NULL,NULL),(215,'SY','Syria',1,21.00,15.00,10.00,NULL,NULL),(216,'SZ','Swaziland',1,21.00,15.00,10.00,NULL,NULL),(217,'TC','Turks and Caicos Islands',1,21.00,15.00,10.00,NULL,NULL),(218,'TD','Chad',1,21.00,15.00,10.00,NULL,NULL),(219,'TF','French Southern Territories',1,21.00,15.00,10.00,NULL,NULL),(220,'TG','Togo',1,21.00,15.00,10.00,NULL,NULL),(221,'TH','Thailand',1,21.00,15.00,10.00,NULL,NULL),(222,'TJ','Tajikistan',1,21.00,15.00,10.00,NULL,NULL),(223,'TK','Tokelau',1,21.00,15.00,10.00,NULL,NULL),(224,'TL','Timor-Leste',1,21.00,15.00,10.00,NULL,NULL),(225,'TM','Turkmenistan',1,21.00,15.00,10.00,NULL,NULL),(226,'TN','Tunisia',1,21.00,15.00,10.00,NULL,NULL),(227,'TO','Tonga',1,21.00,15.00,10.00,NULL,NULL),(228,'TR','Turkey',1,21.00,15.00,10.00,NULL,NULL),(229,'TT','Trinidad and Tobago',1,21.00,15.00,10.00,NULL,NULL),(230,'TV','Tuvalu',1,21.00,15.00,10.00,NULL,NULL),(231,'TW','Taiwan',1,21.00,15.00,10.00,NULL,NULL),(232,'TZ','Tanzania',1,21.00,15.00,10.00,NULL,NULL),(233,'UA','Ukraine',1,21.00,15.00,10.00,NULL,NULL),(234,'UG','Uganda',1,21.00,15.00,10.00,NULL,NULL),(235,'UM','U.S. Minor Outlying Islands',1,21.00,15.00,10.00,NULL,NULL),(236,'US','United States',1,21.00,15.00,10.00,NULL,NULL),(237,'UY','Uruguay',1,21.00,15.00,10.00,NULL,NULL),(238,'UZ','Uzbekistan',1,21.00,15.00,10.00,NULL,NULL),(239,'VA','Vatican City',1,21.00,15.00,10.00,NULL,NULL),(240,'VC','Saint Vincent and the Grenadines',1,21.00,15.00,10.00,NULL,NULL),(241,'VE','Venezuela',1,21.00,15.00,10.00,NULL,NULL),(242,'VG','British Virgin Islands',1,21.00,15.00,10.00,NULL,NULL),(243,'VI','U.S. Virgin Islands',1,21.00,15.00,10.00,NULL,NULL),(244,'VN','Vietnam',1,21.00,15.00,10.00,NULL,NULL),(245,'VU','Vanuatu',1,21.00,15.00,10.00,NULL,NULL),(246,'WF','Wallis and Futuna',1,21.00,15.00,10.00,NULL,NULL),(247,'WS','Samoa',1,21.00,15.00,10.00,NULL,NULL),(248,'XK','Kosovo',1,21.00,15.00,10.00,NULL,NULL),(249,'YE','Yemen',1,21.00,15.00,10.00,NULL,NULL),(250,'YT','Mayotte',1,21.00,15.00,10.00,NULL,NULL),(251,'ZA','South Africa',1,21.00,15.00,10.00,NULL,NULL),(252,'ZM','Zambia',1,21.00,15.00,10.00,NULL,NULL),(253,'ZW','Zimbabwe',1,21.00,15.00,10.00,NULL,NULL);
/*!40000 ALTER TABLE `state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stock`
--

DROP TABLE IF EXISTS `stock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `alias` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `address` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `deliveryHour` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '11:00',
  `extId` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `extId` (`extId`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stock`
--

LOCK TABLES `stock` WRITE;
/*!40000 ALTER TABLE `stock` DISABLE KEYS */;
INSERT INTO `stock` VALUES (1,'prodejna','shop',NULL,NULL,'11:00',NULL),(2,'u dodavatele','supplier_store',NULL,NULL,'11:00',NULL);
/*!40000 ALTER TABLE `stock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stock_supplies`
--

DROP TABLE IF EXISTS `stock_supplies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stock_supplies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stockId` int(11) NOT NULL,
  `variantId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL,
  `lastImport` datetime DEFAULT NULL,
  `deliveryDelay` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `stockId_variantId` (`stockId`,`variantId`),
  KEY `productVariantId` (`variantId`),
  KEY `stockId` (`stockId`),
  CONSTRAINT `stock_supplies_ibfk_3` FOREIGN KEY (`stockId`) REFERENCES `stock` (`id`) ON DELETE CASCADE,
  CONSTRAINT `stock_supplies_ibfk_4` FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1761 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stock_supplies`
--

LOCK TABLES `stock_supplies` WRITE;
/*!40000 ALTER TABLE `stock_supplies` DISABLE KEYS */;
INSERT INTO `stock_supplies` VALUES (1715,1,44,1,NULL,0),(1716,2,44,10,NULL,0),(1717,1,50,10,NULL,0),(1718,2,50,100,NULL,0),(1723,1,47,0,NULL,0),(1724,2,47,0,NULL,0),(1725,1,51,0,NULL,0),(1726,2,51,0,NULL,0),(1727,1,52,0,NULL,0),(1728,2,52,0,NULL,0),(1731,1,53,5,NULL,0),(1732,2,53,0,NULL,0),(1737,1,56,0,NULL,0),(1738,2,56,0,NULL,0),(1739,1,59,0,NULL,0),(1740,2,59,0,NULL,0),(1743,1,57,9,NULL,0),(1744,2,57,10,NULL,0),(1755,1,65,9,NULL,0),(1756,2,65,0,NULL,0),(1759,1,67,10,NULL,0),(1760,2,67,15,NULL,0);
/*!40000 ALTER TABLE `stock_supplies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `string`
--

DROP TABLE IF EXISTS `string`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `string` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lg` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'cs',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `usedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lg_name` (`lg`,`name`),
  KEY `usedAt` (`usedAt`)
) ENGINE=InnoDB AUTO_INCREMENT=9772 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `string`
--

LOCK TABLES `string` WRITE;
/*!40000 ALTER TABLE `string` DISABLE KEYS */;
INSERT INTO `string` VALUES (5,'cs','skip_main','Přejít k obsahu',NULL),(6,'en','skip_main','Skip to content',NULL),(7,'cs','skip_menu','Přejít na hlavní menu',NULL),(8,'en','skip_menu','Skip to main menu',NULL),(9,'cs','skip_search','Přejít na vyhledávání',NULL),(10,'en','skip_search','Skip to search',NULL),(13,'cs','search_placeholder','Co hledáte?',NULL),(14,'en','search_placeholder','What are you looking for?',NULL),(15,'cs','btn_search','Hledat',NULL),(16,'en','btn_search','Search',NULL),(19,'cs','login_title','Přihlášení',NULL),(20,'en','login_title','Login',NULL),(23,'cs','email','E-mail',NULL),(24,'en','email','##email',NULL),(25,'cs','form_enter_username','Zadejte uživatelské jméno',NULL),(26,'en','form_enter_username','##form_enter_username',NULL),(31,'cs','form_enter_password','Zadejte heslo',NULL),(32,'en','form_enter_password','##form_enter_password',NULL),(33,'cs','link_lost_password','Zapomenuté heslo',NULL),(34,'en','link_lost_password','##link_lost_password',NULL),(35,'cs','link_registration','Registrace',NULL),(36,'en','link_registration','##link_registration',NULL),(37,'cs','btn_login','Přihlásit se',NULL),(38,'en','btn_login','##btn_login',NULL),(45,'cs','please_rewrite_value','Prosím opište hodnotu',NULL),(46,'en','please_rewrite_value','##please_rewrite_value',NULL),(49,'cs','enter_email','Zadejte e-mail',NULL),(50,'en','enter_email','##enter_email',NULL),(55,'cs','profile_title','Můj účet',NULL),(56,'en','profile_title','My account',NULL),(59,'cs','breadcrumb_title','Naházíte se zde:',NULL),(60,'en','breadcrumb_title','You are here:',NULL),(105,'cs','price_tax','s DPH',NULL),(106,'en','price_tax','with VAT',NULL),(113,'cs','product_flag_delivery','Doprava zdarma',NULL),(114,'en','product_flag_delivery','Free delivery',NULL),(115,'cs','stock_one_piece','Poslední kus',NULL),(116,'en','stock_one_piece','Last piece',NULL),(119,'cs','stock_zero','Není skladem',NULL),(120,'en','stock_zero','##stock_zero',NULL),(127,'cs','price_without_tax','bez DPH',NULL),(128,'en','price_without_tax','##price_without_tax',NULL),(137,'cs','title_parameters','Parametry',NULL),(138,'en','title_parameters','##title_parameters',NULL),(147,'cs','day_2','úterý',NULL),(148,'en','day_2','##day_2',NULL),(151,'cs','day_3','středu',NULL),(152,'en','day_3','##day_3',NULL),(153,'cs','day_4','čtvrtek',NULL),(154,'en','day_4','##day_4',NULL),(167,'cs','form_valid_email','E-mailová adresa není platná.',NULL),(168,'en','form_valid_email','##form_valid_email',NULL),(199,'cs','form_files','Soubory',NULL),(200,'en','form_files','##form_files',NULL),(209,'cs','form_btn_send','Odeslat formulář',NULL),(210,'en','form_btn_send','##form_btn_send',NULL),(215,'cs','error_voucher_bad_using_min_order','Slevový poukaz nelze uplatnit, minimální hodnota nákupu pro uplatnění slevy je %minOrderPrice%',NULL),(216,'cs','server_error_500_msg','Na našem serveru došlo k neočekávané chybě.<br>Mějte s námi strpení a zkuste to, prosím, znovu.',NULL),(217,'en','server_error_500_msg','We\'re sorry! The server encountered an internal error and was unable to complete your request. <br>Please try again later.',NULL),(218,'cs','title_personal_info','Osobní údaje',NULL),(219,'en','title_personal_info','##title_personal_info',NULL),(268,'cs','title_invoice_address','Fakturační adresa',NULL),(269,'en','title_invoice_address','##title_invoice_address',NULL),(320,'cs','title_delivery_address','Dodací adresa',NULL),(321,'en','title_delivery_address','##title_delivery_address',NULL),(322,'cs','add_address','Přidat další adresu',NULL),(323,'en','add_address','##add_address',NULL),(326,'cs','btn_change','Změnit',NULL),(327,'en','btn_change','Change',NULL),(328,'cs','title_password_change','Změnit heslo',NULL),(329,'en','title_password_change','##title_password_change',NULL),(332,'cs','form_password_not_same','Hesla se neshodují.',NULL),(333,'en','form_password_not_same','##form_password_not_same',NULL),(334,'cs','btn_change_password','Změnit heslo',NULL),(335,'en','btn_change_password','##btn_change_password',NULL),(7573,'cs','form_label_name','Jméno',NULL),(7574,'en','form_label_name','##form_label_name',NULL),(7575,'cs','form_label_surname','Příjmení',NULL),(7576,'en','form_label_surname','##form_label_surname',NULL),(7577,'cs','form_label_email','E-mail',NULL),(7578,'en','form_label_email','##form_label_email',NULL),(7579,'cs','form_label_phone','Telefon',NULL),(7580,'en','form_label_phone','##form_label_phone',NULL),(7581,'cs','form_label_text','Zpráva',NULL),(7582,'en','form_label_text','##form_label_text',NULL),(7583,'cs','form_label_file','Soubor',NULL),(7584,'en','form_label_file','##form_label_file',NULL),(7585,'cs','form_label_password','Heslo',NULL),(7586,'en','form_label_password','##form_label_password',NULL),(7589,'cs','day_5','pátek',NULL),(7590,'en','day_5','##day_5',NULL),(7591,'cs','day_1','pondělí',NULL),(7592,'en','day_1','##day_1',NULL),(7593,'cs','form_label_message','Zpráva',NULL),(7594,'en','form_label_message','##form_label_message',NULL),(7597,'cs','price_not_determined','Cena nestanovena',NULL),(7598,'en','price_not_determined','Price not determined',NULL),(7599,'cs','availability_soldout','Vyprodáno',NULL),(7600,'en','availability_soldout','Soldout',NULL),(7601,'cs','btn_watch_availability','Sledovat dostupnost',NULL),(7602,'en','btn_watch_availability','##btn_watch_availability',NULL),(7603,'cs','form_label_agree_text','Souhlasím se',NULL),(7604,'en','form_label_agree_text','##form_label_agree_text',NULL),(7605,'cs','form_label_agree_link','zpracováním osobních údajů',NULL),(7606,'en','form_label_agree_link','##form_label_agree_link',NULL),(7609,'cs','title_files','Soubory ke stažení',NULL),(7610,'en','title_files','##title_files',NULL),(7635,'cs','shopping_basket','Nákupní košík',NULL),(7636,'en','shopping_basket','##shopping_basket',NULL),(7637,'cs','shipping_payment','Doprava a platba',NULL),(7638,'en','shipping_payment','##shipping_payment',NULL),(7639,'cs','personal_info','Doručovací údaje',NULL),(7640,'en','personal_info','##personal_info',NULL),(7641,'cs','order_success','Odeslání',NULL),(7642,'en','order_success','##order_success',NULL),(7645,'cs','availability','Dostupnost',NULL),(7646,'en','availability','##availability',NULL),(7647,'cs','amount','Množství',NULL),(7648,'en','amount','##amount',NULL),(7649,'cs','unit_price_vat','Cena za kus',NULL),(7650,'en','unit_price_vat','##unit_price_vat',NULL),(7651,'cs','total_price_vat','Cena vč. DPH',NULL),(7652,'en','total_price_vat','##total_price_vat',NULL),(7653,'cs','not_available','Nedostupný',NULL),(7654,'en','not_available','##not_available',NULL),(7663,'cs','total_price','Cena celkem bez DPH',NULL),(7664,'en','total_price','##total_price',NULL),(7683,'cs','free','zdarma',NULL),(7684,'en','free','##free',NULL),(7689,'cs','btn_back','Zpět',NULL),(7690,'en','btn_back','##btn_back',NULL),(7691,'cs','order_sum_products_title','Shrnutí objednávky',NULL),(7692,'en','order_sum_products_title','##order_sum_products_title',NULL),(7709,'cs','form_label_street','Ulice',NULL),(7710,'en','form_label_street','##form_label_street',NULL),(7711,'cs','form_label_city','Město',NULL),(7712,'en','form_label_city','##form_label_city',NULL),(7713,'cs','form_label_zip','PSČ',NULL),(7714,'en','form_label_zip','##form_label_zip',NULL),(7715,'cs','note','Poznámka',NULL),(7716,'en','note','##note',NULL),(7717,'cs','title_company_form','Vyplnit firemní údaje',NULL),(7718,'en','title_company_form','##title_company_form',NULL),(7719,'cs','form_label_company','Firma',NULL),(7720,'en','form_label_company','##form_label_company',NULL),(7721,'cs','form_label_ic','IČ',NULL),(7722,'en','form_label_ic','##form_label_ic',NULL),(7723,'cs','form_label_dic','DIČ',NULL),(7724,'en','form_label_dic','##form_label_dic',NULL),(7725,'cs','title_delivery_form','Jiná dodací adresa',NULL),(7726,'en','title_delivery_form','##title_delivery_form',NULL),(7727,'cs','form_label_state','Stát',NULL),(7728,'en','form_label_state','##form_label_state',NULL),(7741,'cs','btn_send_order','Odeslat objednávku',NULL),(7742,'en','btn_send_order','##btn_send_order',NULL),(7743,'cs','delivery','Doprava',NULL),(7744,'en','delivery','##delivery',NULL),(7745,'cs','payment','Platba',NULL),(7746,'en','payment','##payment',NULL),(7755,'cs','title_personal_recap','Fakturační údaje',NULL),(7756,'en','title_personal_recap','##title_personal_recap',NULL),(7763,'cs','btn_watch_order','Kliknutím zobrazíte <a href=\"%link\">stav objednávky</a>.',NULL),(7764,'en','btn_watch_order','##btn_watch_order',NULL),(7775,'cs','form_label_newsletter','Souhlasím s odběrem newsletteru',NULL),(7776,'en','form_label_newsletter','##form_label_newsletter',NULL),(7777,'cs','form_label_password2','Heslo znovu',NULL),(7778,'en','form_label_password2','##form_label_password2',NULL),(7781,'cs','registration_agree','Registrací souhlasíte se',NULL),(7782,'en','registration_agree','##registration_agree',NULL),(7785,'cs','btn_register','Registrovat',NULL),(7786,'en','btn_register','##btn_register',NULL),(7803,'cs','form_profil_ok','Změny byly uloženy.',NULL),(7804,'en','form_profil_ok','##form_profil_ok',NULL),(7805,'cs','registration_email','E-mail',NULL),(7806,'en','registration_email','##registration_email',NULL),(7807,'cs','btn_lost_pwd','Odeslat',NULL),(7808,'en','btn_lost_pwd','##btn_lost_pwd',NULL),(7809,'cs','registration_agree_personal_data_link','zpracováním osobních údajů',NULL),(7810,'en','registration_agree_personal_data_link','##registration_agree_personal_data_link',NULL),(7811,'cs','form_send_reset_password','Na Váš e-mail byl odeslán odkaz pro nastavení nového hesla.',NULL),(7812,'en','form_send_reset_password','##form_send_reset_password',NULL),(7813,'cs','btn_save','Uložit',NULL),(7814,'en','btn_save','##btn_save',NULL),(7819,'cs','pname_113','Select CZ',NULL),(7820,'en','pname_113','Select',NULL),(7821,'cs','pvalue_6564','Hodnota 1',NULL),(7822,'en','pvalue_6564','Hodnota 1',NULL),(7823,'cs','pvalue_6565','Hodnota 2',NULL),(7824,'en','pvalue_6565','Hodnota 2',NULL),(7825,'cs','pvalue_6566','Hodnota 3',NULL),(7826,'en','pvalue_6566','Hodnota 3',NULL),(7831,'cs','pvalue_alias_6564','hodnota-1',NULL),(7832,'en','pvalue_alias_6564','hodnota-1',NULL),(7833,'cs','pvalue_alias_6565','hodnota-2',NULL),(7834,'en','pvalue_alias_6565','hodnota-2',NULL),(7835,'cs','pvalue_alias_6566','hodnota-3',NULL),(7836,'en','pvalue_alias_6566','hodnota-3',NULL),(7837,'cs','pvalue_6567','Hodnota 1',NULL),(7838,'en','pvalue_6567','Hodnota 1',NULL),(7839,'cs','pvalue_alias_6567','hodnota-1',NULL),(7840,'en','pvalue_alias_6567','hodnota-1',NULL),(7841,'cs','pvalue_6570','Hodnota 2',NULL),(7842,'en','pvalue_6570','Hodnota 2',NULL),(7843,'cs','pvalue_alias_6570','hodnota-2',NULL),(7844,'en','pvalue_alias_6570','hodnota-2',NULL),(7845,'cs','pvalue_6571','Hodnota 3',NULL),(7846,'en','pvalue_6571','Hodnota 3',NULL),(7847,'cs','pvalue_alias_6571','hodnota-3',NULL),(7848,'en','pvalue_alias_6571','hodnota-3',NULL),(7849,'cs','pname_114','Multiselect',NULL),(7850,'en','pname_114','Multiselect',NULL),(7851,'cs','catalog_seo_filter_and','a',NULL),(7852,'en','catalog_seo_filter_and','and',NULL),(7853,'cs','pname_tooltip_113','Popisek',NULL),(7854,'en','pname_tooltip_113','',NULL),(7855,'cs','pvalue_filter_6564','Hodnota 1 (Název ve filtru)',NULL),(7856,'en','pvalue_filter_6564','',NULL),(7857,'cs','pname_119','Číslo',NULL),(7858,'en','pname_119','Číslo',NULL),(7859,'cs','pname_tooltip_119','_',NULL),(7860,'en','pname_tooltip_119','_',NULL),(7861,'cs','pname_unit_119','Px',NULL),(7862,'en','pname_unit_119','',NULL),(7863,'cs','bad_login','Neplatný e-mail a/nebo heslo.',NULL),(7864,'en','bad_login','##bad_login',NULL),(7865,'cs','pname_unit_113','',NULL),(7866,'en','pname_unit_113','',NULL),(7881,'cs','pvalue_filter_6565','',NULL),(7882,'en','pvalue_filter_6565','',NULL),(7883,'cs','pvalue_filter_6566','',NULL),(7884,'en','pvalue_filter_6566','',NULL),(7909,'cs','form_label_firstname','Jméno',NULL),(7910,'en','form_label_firstname','##form_label_firstname',NULL),(7911,'cs','form_label_lastname','Příjmení',NULL),(7912,'en','form_label_lastname','##form_label_lastname',NULL),(7913,'cs','availability_on_request','Na dotaz',NULL),(7914,'en','availability_on_request','##availability_on_request',NULL),(7915,'cs','stock_over_10','Více než 10 kusů',NULL),(7916,'en','stock_over_10','##stock_over_10',NULL),(7917,'cs','delivery_date_tomorrow','zítra',NULL),(7918,'en','delivery_date_tomorrow','##delivery_date_tomorrow',NULL),(7919,'cs','delivery_date_at_yours','u vás',NULL),(7920,'en','delivery_date_at_yours','##delivery_date_at_yours',NULL),(7921,'cs','form_profile_password_changed','Vaše heslo bylo změněno.',NULL),(7922,'en','form_profile_password_changed','##form_profile_password_changed',NULL),(7923,'cs','delivery_date_today','již dnes',NULL),(7924,'en','delivery_date_today','##delivery_date_today',NULL),(7925,'cs','order_status_progress','Objednávka se kompletuje',NULL),(7926,'en','order_status_progress','##order_status_progress',NULL),(7927,'cs','pvalue_filter_6567','',NULL),(7928,'en','pvalue_filter_6567','',NULL),(7929,'cs','pvalue_filter_6570','',NULL),(7930,'en','pvalue_filter_6570','',NULL),(7931,'cs','pvalue_filter_6571','',NULL),(7932,'en','pvalue_filter_6571','',NULL),(7933,'cs','pname_tooltip_114','',NULL),(7934,'en','pname_tooltip_114','',NULL),(7935,'cs','pname_unit_114','',NULL),(7936,'en','pname_unit_114','',NULL),(7937,'cs','title_company_info','Firemní údaje',NULL),(7938,'en','title_company_info','##title_company_info',NULL),(7939,'cs','order_status_new','Přijatá',NULL),(7940,'en','order_status_new','##order_status_new',NULL),(7941,'cs','form_reset_password','Vaše heslo bylo nastaveno.',NULL),(7942,'cs','reset_password_expired_link','Odkaz pro reset hesla není platný',NULL),(7943,'en','reset_password_expired_link','Password reset link is not valid',NULL),(8346,'cs','availability_in_stock','Skladem',NULL),(8347,'en','availability_in_stock','##availability_in_stock',NULL),(8348,'cs','stock_name_alias_shop','Na prodejně',NULL),(8349,'en','stock_name_alias_shop','##stock_name_alias_shop',NULL),(8350,'cs','stock_name_alias_supplier_store','U dodavatele',NULL),(8351,'en','stock_name_alias_supplier_store','##stock_name_alias_supplier_store',NULL),(8352,'cs','title_delivery_info','Doručení',NULL),(8353,'en','title_delivery_info','##title_delivery_info',NULL),(8354,'cs','stock_piece','ks',NULL),(8355,'en','stock_piece','##stock_piece',NULL),(8358,'cs','delivery_date_at','v',NULL),(8359,'en','delivery_date_at','##delivery_date_at',NULL),(8360,'cs','stock_over_2','Více než 2 kusy',NULL),(8361,'en','stock_over_2','##stock_over_2',NULL),(8362,'cs','stock_over_5','Více než 5 kusů',NULL),(8363,'en','stock_over_5','##stock_over_5',NULL),(8364,'cs','filter_range_price','Cena',NULL),(8365,'en','filter_range_price','##filter_range_price',NULL),(8416,'cs','pvalue_6572','10',NULL),(8417,'en','pvalue_6572','10',NULL),(8418,'cs','pvalue_alias_6572','6572',NULL),(8419,'en','pvalue_alias_6572','6572',NULL),(8420,'cs','pvalue_filter_6572','',NULL),(8421,'en','pvalue_filter_6572','',NULL),(8422,'cs','pvalue_6573','20',NULL),(8423,'en','pvalue_6573','20',NULL),(8424,'cs','pvalue_alias_6573','6573',NULL),(8425,'en','pvalue_alias_6573','6573',NULL),(8426,'cs','pvalue_filter_6573','',NULL),(8427,'en','pvalue_filter_6573','',NULL),(8428,'cs','pvalue_6574','88',NULL),(8429,'en','pvalue_6574','88',NULL),(8430,'cs','pvalue_alias_6574','6574',NULL),(8431,'en','pvalue_alias_6574','6574',NULL),(8432,'cs','pvalue_filter_6574','',NULL),(8433,'en','pvalue_filter_6574','',NULL),(8434,'cs','pvalue_6575','120',NULL),(8435,'en','pvalue_6575','120',NULL),(8436,'cs','pvalue_alias_6575','6575',NULL),(8437,'en','pvalue_alias_6575','6575',NULL),(8438,'cs','pvalue_filter_6575','',NULL),(8439,'en','pvalue_filter_6575','',NULL),(8440,'cs','pvalue_6576','2',NULL),(8441,'en','pvalue_6576','2',NULL),(8442,'cs','pvalue_alias_6576','6576',NULL),(8443,'en','pvalue_alias_6576','6576',NULL),(8444,'cs','pvalue_filter_6576','',NULL),(8445,'en','pvalue_filter_6576','',NULL),(8592,'cs','form_label_add_address','Přidat adresu',NULL),(8593,'en','form_label_add_address','##form_label_add_address',NULL),(8594,'cs','form_label_remove_address','Odebrat adresu',NULL),(8595,'en','form_label_remove_address','##form_label_remove_address',NULL),(8596,'cs','delivery_date_at_inf','ve',NULL),(8597,'en','delivery_date_at_inf','##delivery_date_at_inf',NULL),(8600,'cs','title_product_main_category','Hlavní kategorie',NULL),(8601,'en','title_product_main_category','##title_product_main_category',NULL),(8602,'cs','title_product_all_variants','Ostatní varianty',NULL),(8603,'en','title_product_all_variants','##title_product_all_variants',NULL),(8604,'cs','stock_two_pieces','Poslední 2 kusy',NULL),(8605,'en','stock_two_pieces','##stock_two_pieces',NULL),(8630,'cs','pname_116','pname_116',NULL),(8631,'en','pname_116','pname_116',NULL),(8632,'cs','pname_tooltip_116','pname_tooltip_116',NULL),(8633,'en','pname_tooltip_116','pname_tooltip_116',NULL),(8634,'cs','pname_unit_116','pname_unit_116',NULL),(8635,'en','pname_unit_116','pname_unit_116',NULL),(8696,'cs','product_ean','EAN',NULL),(8697,'en','product_ean','##product_ean',NULL),(8698,'cs','product_code','Kód produktu',NULL),(8699,'en','product_code','##product_code',NULL),(8710,'cs','filter_range_price_unit','v Kč',NULL),(8711,'en','filter_range_price_unit','##filter_range_price_unit',NULL),(8712,'cs','filter_range_price_description','Zvolte rozmezí ceny hledaného produktu',NULL),(8714,'en','filter_range_price_description','##filter_range_price_description',NULL),(8718,'cs','btn_filter','Filtrovat',NULL),(8719,'en','btn_filter','##btn_filter',NULL),(8730,'cs','btn_add_to_basket','Vložit do košíku',NULL),(8732,'en','btn_add_to_basket','##btn_add_to_basket',NULL),(8734,'cs','btn_filter_remove','Resetovat',NULL),(8735,'en','btn_filter_remove','Reset',NULL),(8748,'cs','pvalue_6580','##1',NULL),(8749,'en','pvalue_6580','##1',NULL),(8750,'cs','pvalue_alias_6580','##6580',NULL),(8751,'en','pvalue_alias_6580','##6580',NULL),(8752,'cs','pvalue_filter_6580','##',NULL),(8753,'en','pvalue_filter_6580','##',NULL),(8756,'cs','pname_unit_117','##',NULL),(8757,'en','pname_unit_117','##',NULL),(8758,'cs','pname_unit_118','##',NULL),(8759,'en','pname_unit_118','##',NULL),(8760,'cs','pname_unit_120','##',NULL),(8761,'en','pname_unit_120','##',NULL),(8762,'cs','pname_tooltip_120','##',NULL),(8763,'en','pname_tooltip_120','##',NULL),(8764,'cs','pname_120','##Wysiwyg',NULL),(8765,'en','pname_120','##Wysiwyg',NULL),(8766,'cs','pname_tooltip_118','##',NULL),(8767,'en','pname_tooltip_118','##',NULL),(8768,'cs','pname_118','##Textarea',NULL),(8769,'en','pname_118','##Textarea',NULL),(8770,'cs','pname_tooltip_117','##',NULL),(8771,'en','pname_tooltip_117','##',NULL),(8772,'cs','pname_117','##Text',NULL),(8773,'en','pname_117','##Text',NULL),(8776,'cs','pname_1','##Parametry',NULL),(8777,'en','pname_1','##Parametry',NULL),(8778,'cs','pname_tooltip_1','##',NULL),(8779,'en','pname_tooltip_1','##',NULL),(8780,'cs','pname_121','Velikost',NULL),(8781,'en','pname_121','Size',NULL),(8782,'cs','pname_tooltip_121','',NULL),(8783,'en','pname_tooltip_121','',NULL),(8784,'cs','pname_unit_121','',NULL),(8785,'en','pname_unit_121','',NULL),(8798,'cs','pvalue_6582','A4',NULL),(8799,'en','pvalue_6582','A4',NULL),(8800,'cs','pvalue_alias_6582','a4',NULL),(8801,'en','pvalue_alias_6582','a4',NULL),(8802,'cs','pvalue_filter_6582','',NULL),(8803,'en','pvalue_filter_6582','',NULL),(8804,'cs','pvalue_6583','A3',NULL),(8805,'en','pvalue_6583','A3',NULL),(8806,'cs','pvalue_alias_6583','a3',NULL),(8807,'en','pvalue_alias_6583','a3',NULL),(8812,'cs','pvalue_filter_6583','',NULL),(8813,'en','pvalue_filter_6583','',NULL),(8832,'cs','pvalue_6584','##x',NULL),(8833,'en','pvalue_6584','##x',NULL),(8834,'cs','pname_122','##Enter a new name',NULL),(8835,'en','pname_122','##Enter a new name',NULL),(8836,'cs','pname_123','##Enter a new name',NULL),(8837,'en','pname_123','##Enter a new name',NULL),(8838,'cs','pname_124','##Enter a new name',NULL),(8839,'en','pname_124','##Enter a new name',NULL),(8840,'cs','pname_125','##Enter a new name',NULL),(8841,'en','pname_125','##Enter a new name',NULL),(8842,'cs','pname_tooltip_124','##',NULL),(8843,'en','pname_tooltip_124','##',NULL),(8844,'cs','pname_tooltip_122','##',NULL),(8845,'en','pname_tooltip_122','##',NULL),(8846,'cs','pname_unit_122','##',NULL),(8847,'en','pname_unit_122','##',NULL),(8848,'cs','pname_tooltip_123','##',NULL),(8849,'en','pname_tooltip_123','##',NULL),(8850,'cs','pname_unit_123','##',NULL),(8851,'en','pname_unit_123','##',NULL),(8852,'cs','pname_unit_124','##',NULL),(8853,'en','pname_unit_124','##',NULL),(8854,'cs','pname_tooltip_125','##',NULL),(8855,'en','pname_tooltip_125','##',NULL),(8856,'cs','pname_unit_125','##',NULL),(8857,'en','pname_unit_125','##',NULL),(8858,'cs','pname_126','##Enter a new name',NULL),(8859,'en','pname_126','##Enter a new name',NULL),(8860,'cs','pname_tooltip_126','##',NULL),(8861,'en','pname_tooltip_126','##',NULL),(8862,'cs','pname_unit_126','##',NULL),(8863,'en','pname_unit_126','##',NULL),(8864,'cs','price_from','cena od',NULL),(8865,'en','price_from','##price_from',NULL),(8866,'cs','availability_only_some_variants','jen některé varianty',NULL),(8867,'en','availability_only_some_variants','##availability_only_some_variants',NULL),(8870,'cs','another_address_form','Vložit nové údaje',NULL),(8871,'en','another_address_form','##another_address_form',NULL),(8902,'cs','pvalue_alias_6584','##x',NULL),(8903,'en','pvalue_alias_6584','##x',NULL),(8904,'cs','pvalue_filter_6584','##',NULL),(8905,'en','pvalue_filter_6584','##',NULL),(8924,'cs','pname_127','Ráže',NULL),(8925,'en','pname_127','Enter a new name',NULL),(8926,'cs','pname_tooltip_127','Tooltip obsah',NULL),(8927,'en','pname_tooltip_127','Tooltip content',NULL),(8928,'cs','pname_unit_127','',NULL),(8929,'en','pname_unit_127','',NULL),(8936,'cs','pvalue_6586','4,5 mm',NULL),(8937,'en','pvalue_6586','4,5 mm',NULL),(8938,'cs','pvalue_alias_6586','4-5-mm',NULL),(8939,'en','pvalue_alias_6586','4-5-mm',NULL),(8940,'cs','pvalue_filter_6586','',NULL),(8941,'en','pvalue_filter_6586','',NULL),(8942,'cs','pvalue_6587','5 mm',NULL),(8943,'en','pvalue_6587','5 mm',NULL),(8944,'cs','pvalue_alias_6587','5-mm',NULL),(8945,'en','pvalue_alias_6587','5-mm',NULL),(8946,'cs','pvalue_filter_6587','',NULL),(8947,'en','pvalue_filter_6587','',NULL),(9020,'cs','pvalue_6588','5,5 mm',NULL),(9021,'en','pvalue_6588','5,5 mm',NULL),(9022,'cs','pvalue_alias_6588','5-5-mm',NULL),(9023,'en','pvalue_alias_6588','5-5-mm',NULL),(9024,'cs','pvalue_filter_6588','',NULL),(9025,'en','pvalue_filter_6588','',NULL),(9026,'cs','pname_128','Délka hlavně',NULL),(9027,'en','pname_128','Enter a new name',NULL),(9028,'cs','pname_tooltip_128','',NULL),(9029,'en','pname_tooltip_128','',NULL),(9030,'cs','pname_unit_128','mm',NULL),(9031,'en','pname_unit_128','',NULL),(9050,'cs','pname_112','##Stránky',NULL),(9051,'en','pname_112','##Stránky',NULL),(9052,'cs','pname_tooltip_112','##',NULL),(9053,'en','pname_tooltip_112','##',NULL),(9054,'cs','pname_unit_112','##',NULL),(9055,'en','pname_unit_112','##',NULL),(9057,'cs','pvalue_6568','##Tag 1',NULL),(9058,'en','pvalue_6568','##Tag 1',NULL),(9059,'cs','pvalue_alias_6568','##tag-1',NULL),(9060,'en','pvalue_alias_6568','##tag-1',NULL),(9061,'cs','pvalue_filter_6568','##',NULL),(9062,'en','pvalue_filter_6568','##',NULL),(9063,'cs','pvalue_6569','##Tag 2',NULL),(9064,'en','pvalue_6569','##Tag 2',NULL),(9065,'cs','pvalue_alias_6569','##tag-2',NULL),(9066,'en','pvalue_alias_6569','##tag-2',NULL),(9067,'cs','pvalue_filter_6569','##',NULL),(9068,'en','pvalue_filter_6569','##',NULL),(9069,'cs','pname_115','##Tag',NULL),(9070,'en','pname_115','##Tag',NULL),(9071,'cs','pname_tooltip_115','##',NULL),(9072,'en','pname_tooltip_115','##',NULL),(9073,'cs','pname_unit_115','##',NULL),(9074,'en','pname_unit_115','##',NULL),(9085,'cs','message_buy_to_free_transport','Nakupte ještě za %priceToFreeTransport% a máte dopravu zdarma!',NULL),(9086,'en','message_buy_to_free_transport','##message_buy_to_free_transport',NULL),(9096,'cs','billing_info_title','Fakturační údaje',NULL),(9097,'en','billing_info_title','##billing_info_title',NULL),(9130,'cs','form_label_heureka_disable','Nesouhlasím se zasláním dotazníku spokojenosti',NULL),(9188,'cs','pname_130','##Enter a new name',NULL),(9189,'en','pname_130','##Enter a new name',NULL),(9190,'cs','pname_tooltip_130','##',NULL),(9191,'en','pname_tooltip_130','##',NULL),(9192,'cs','pname_unit_130','##',NULL),(9193,'en','pname_unit_130','##',NULL),(9244,'cs','pvalue_6593','##h4',NULL),(9245,'en','pvalue_6593','##h4',NULL),(9246,'cs','pvalue_alias_6593','##h4',NULL),(9247,'en','pvalue_alias_6593','##h4',NULL),(9248,'cs','pvalue_filter_6593','##',NULL),(9249,'en','pvalue_filter_6593','##',NULL),(9250,'cs','pname_131','##Enter a new name',NULL),(9251,'en','pname_131','##Enter a new name',NULL),(9252,'cs','pname_tooltip_131','##',NULL),(9253,'en','pname_tooltip_131','##',NULL),(9254,'cs','pname_unit_131','##',NULL),(9255,'en','pname_unit_131','##',NULL),(9266,'cs','order_status','Stav objednávky',NULL),(9267,'en','order_status','##order_status',NULL),(9268,'cs','order_number','Číslo objednávky',NULL),(9269,'en','order_number','##order_number',NULL),(9270,'cs','order_amount','Počet produktů',NULL),(9271,'en','order_amount','##order_amount',NULL),(9272,'cs','order_created','Vytvořeno',NULL),(9273,'en','order_created','##order_created',NULL),(9274,'cs','order_delivery','Způsob dopravy',NULL),(9275,'en','order_delivery','##order_delivery',NULL),(9276,'cs','order_payment','Způsob platby',NULL),(9277,'en','order_payment','##order_payment',NULL),(9282,'cs','order_detail_title','Detail objednávky č.',NULL),(9283,'en','order_detail_title','##order_detail_title',NULL),(9298,'cs','pname_filter_prefix_114','##',NULL),(9299,'en','pname_filter_prefix_114','##',NULL),(9304,'cs','pname_filter_prefix_113','##',NULL),(9305,'en','pname_filter_prefix_113','##',NULL),(9308,'en','user_not_found','Account not found',NULL),(9309,'cs','user_not_found','Účet nenalezen',NULL),(9310,'en','message_ok_login','Successfully logged in',NULL),(9311,'cs','message_ok_login','Úspěšně přihlášen',NULL),(9312,'en','message_bad_login','Login failed',NULL),(9313,'cs','message_bad_login','Přihlášení se nepovedlo',NULL),(9314,'cs','voucher_error_already_in_cart','Slevový kód se už nacházi v nákupném košíku.',NULL),(9315,'cs','voucher_error_voucher_bad_using_min_order','Slevový poukaz nelze uplatnit, minimální hodnota nákupu pro uplatnění slevy je %minOrderPrice%',NULL),(9316,'cs','voucher_error_voucher_expired','Slevový kód je neplatný.',NULL),(9317,'cs','voucher_error_voucher_no_exists','Slevový kód neexistuje.',NULL),(9318,'cs','voucher_error_voucher_used','Slevový kód již byl použit.',NULL),(9319,'cs','voucher_error_voucher_combination','Není možné kombinovat tyto slevové kódy.',NULL),(9320,'cs','cart_message_voucher_added','Slevový kód byl přidán do nákupného košíku.',NULL),(9321,'cs','cart_message_voucher_removed','Slevový kód (%code%) byl odebrán z nákupného košíku. Důvod: %reason%',NULL),(9322,'cs','cart_delivery_title','Vyberte způsob dopravy',NULL),(9323,'en','cart_delivery_title','##cart_delivery_title',NULL),(9324,'cs','cart_payment_title','Vyberte způsob platby',NULL),(9325,'en','cart_payment_title','##cart_payment_title',NULL),(9326,'cs','payment_for_delivery_not_set','Nebyla zvolena žádná doprava',NULL),(9327,'en','payment_for_delivery_not_set','##payment_for_delivery_not_set',NULL),(9334,'cs','personal_info_title','Osobní údaje',NULL),(9335,'en','personal_info_title','##personal_info_title',NULL),(9336,'cs','btn_step2_continue_without_registration','Pokračovat bez registrace',NULL),(9337,'en','btn_step2_continue_without_registration','##btn_step2_continue_without_registration',NULL),(9338,'cs','btn_order_continue','Pokračovat v objednávce',NULL),(9339,'en','btn_order_continue','##btn_order_continue',NULL),(9340,'cs','btn_confirm','Potvrdit',NULL),(9341,'en','btn_confirm','##btn_confirm',NULL),(9342,'cs','cart_summary_title','Shrnutí objednávky',NULL),(9343,'en','cart_summary_title','##cart_summary_title',NULL),(9344,'cs','btn_step2_continue_without_signin','Pokračovat bez přihlášení',NULL),(9345,'en','btn_step2_continue_without_signin','##btn_step2_continue_without_signin',NULL),(9346,'cs','note_step2_forgot_password','Stačí už jen zadat heslo a my vás přihlásíme. Pokud jste své heslo zapomněli, můžete si <a href=\"%link\">vytvořit nové</a>.',NULL),(9347,'en','note_step2_forgot_password','##note_step2_forgot_password',NULL),(9348,'cs','required_agree_title','Povinné souhlasy',NULL),(9349,'en','required_agree_title','##required_agree_title',NULL),(9350,'cs','edit_page','Upravit',NULL),(9351,'en','edit_page','Edit',NULL),(9356,'cs','billing_info_choose_title','Vyberte si z vámi uložených údajů',NULL),(9357,'en','billing_info_choose_title','##billing_info_choose_title',NULL),(9358,'cs','delivery_invoice_address_title','Fakturační a dodací údaje',NULL),(9359,'en','delivery_invoice_address_title','##delivery_invoice_address_title',NULL),(9360,'cs','billing_info_new_title','Nové fakturační údaje',NULL),(9361,'en','billing_info_new_title','##billing_info_new_title',NULL),(9362,'cs','btn_go_to_cart','Přejít do košíku',NULL),(9363,'en','btn_go_to_cart','##btn_go_to_cart',NULL),(9364,'cs','prebasket_added_text','Produkt %productName byl přidaný do košíku.',NULL),(9365,'en','prebasket_added_text','##prebasket_added_text',NULL),(9368,'cs','prebasket_added_less','(Zvolené množství %quantity ks není k dispozici. Do košíku bylo vloženo pouze %added ks)',NULL),(9369,'en','prebasket_added_less','##prebasket_added_less',NULL),(9370,'cs','btn_close','Zavřít',NULL),(9371,'en','btn_close','##btn_close',NULL),(9372,'cs','prebasket_error','Nepodařilo se přidat %quantity ks do košíku: Důvod: není na skladě.',NULL),(9373,'en','prebasket_error','##prebasket_error',NULL),(9374,'cs','btn_cart_back_to_eshop','Zpět do obchodu',NULL),(9375,'en','btn_cart_back_to_eshop','##btn_cart_back_to_eshop',NULL),(9376,'cs','btn_cart_continue','Pokračovat v objednávce',NULL),(9377,'en','btn_cart_continue','##btn_cart_continue',NULL),(9378,'cs','title_order_success','Objednávka č. %orderNumber proběhla úspěšně',NULL),(9379,'en','title_order_success','##title_order_success',NULL),(9380,'cs','bank_payment_text','Platební údaje',NULL),(9381,'en','bank_payment_text','##bank_payment_text',NULL),(9382,'cs','account_number','Číslo účtu',NULL),(9383,'en','account_number','##account_number',NULL),(9384,'cs','variable_symbol','Variabilní symbol',NULL),(9385,'en','variable_symbol','##variable_symbol',NULL),(9386,'cs','amount_to_be_payed','Částka k úhradě',NULL),(9387,'en','amount_to_be_payed','##amount_to_be_payed',NULL),(9388,'cs','tel_label','Tel.',NULL),(9389,'en','tel_label','##tel_label',NULL),(9390,'cs','invoice_address_title','Fakturační údaje',NULL),(9391,'en','invoice_address_title','##invoice_address_title',NULL),(9392,'cs','btn_delete','Smazat',NULL),(9393,'en','btn_delete','##btn_delete',NULL),(9396,'cs','order_status_placed','Objednávka vytvořena',NULL),(9397,'en','order_status_placed','##order_status_placed',NULL),(9398,'cs','order_status_canceled','Stornovaná',NULL),(9399,'en','order_status_canceled','##order_status_canceled',NULL),(9400,'cs','order_status_declined','Odmítnutá',NULL),(9401,'en','order_status_declined','##order_status_declined',NULL),(9402,'cs','order_status_prepared','Připravená',NULL),(9403,'en','order_status_prepared','##order_status_prepared',NULL),(9404,'cs','order_status_dispatched','Odeslaná',NULL),(9405,'en','order_status_dispatched','##order_status_dispatched',NULL),(9412,'cs','order_status_payment','Stav zaplacení',NULL),(9413,'en','order_status_payment','##order_status_payment',NULL),(9414,'cs','order_date_expedition','Datum expirace',NULL),(9415,'en','order_date_expedition','##order_date_expedition',NULL),(9416,'cs','order_note','Poznámka',NULL),(9417,'en','order_note','##order_note',NULL),(9418,'cs','order_billing_address','Fakturační adresa',NULL),(9419,'en','order_billing_address','##order_billing_address',NULL),(9420,'cs','order_delivery_address','Dodací adresa',NULL),(9421,'en','order_delivery_address','##order_delivery_address',NULL),(9422,'cs','order_address_same_as_billing','Totožná jako fakturační',NULL),(9423,'en','order_address_same_as_billing','##order_address_same_as_billing',NULL),(9424,'cs','order_history_title','Historie',NULL),(9425,'en','order_history_title','##order_history_title',NULL),(9428,'cs','item_name','Název položky',NULL),(9429,'en','item_name','Item name',NULL),(9438,'cs','title','SUPERKODERS',NULL),(9439,'en','title','SUPERKODERS',NULL),(9440,'cs','logo','SUPERKODERS',NULL),(9441,'en','logo','SUPERKODERS',NULL),(9442,'cs','please-enter-a-valid-integer','Zadejte platné číslo',NULL),(9443,'en','please-enter-a-valid-integer','Enter a valid number',NULL),(9444,'cs','btn_go_to_basket','Přejít do košíku',NULL),(9445,'en','btn_go_to_basket','Go to basket',NULL),(9446,'cs','newsletter_title','Newsletter',NULL),(9447,'en','newsletter_title','Newsletter',NULL),(9448,'cs','copyright','© %year Provozovatelem je společnost SUPERKODERS',NULL),(9449,'en','copyright','© %year The operator is SUPERKODERS',NULL),(9456,'cs','breadcrumb','Nacházíte se zde:',NULL),(9457,'en','breadcrumb','You are here:',NULL),(9458,'cs','contact_form_title','Kontaktní formulář',NULL),(9459,'en','contact_form_title','Contact form',NULL),(9462,'cs','the-size-of-the-uploaded-file-can-be-up-to-d-bytes','Velikost nahrávaného souboru může být maximálně %d bytů',NULL),(9463,'en','the-size-of-the-uploaded-file-can-be-up-to-d-bytes','The size of the uploaded file can be up to %d bytes.',NULL),(9464,'cs','btn_send','Odeslat',NULL),(9465,'en','btn_send','Send',NULL),(9466,'cs','page_404_name','Stránka 404',NULL),(9467,'en','page_404_name','Page 404',NULL),(9468,'cs','page_404_annotation','Tato stránka neexistuje',NULL),(9469,'en','page_404_annotation','This page doesn\'t exists',NULL),(9470,'cs','page_404_description','##page_404_description',NULL),(9471,'en','page_404_description','##page_404_description',NULL),(9472,'cs','showing','Zobrazeno',NULL),(9473,'en','showing','Showing',NULL),(9474,'cs','of','z',NULL),(9475,'en','of','of',NULL),(9476,'cs','paging_next','Následující',NULL),(9477,'en','paging_next','Next',NULL),(9478,'cs','paging_prev','Předchozí',NULL),(9479,'en','paging_prev','Previous',NULL),(9480,'cs','btn_show_more_count','Zobrazit více (%count)',NULL),(9481,'en','btn_show_more_count','Show more (%count)',NULL),(9482,'cs','empty_smallbasket_text','Váš košík je prázdný',NULL),(9483,'en','empty_smallbasket_text','Your basket is empty',NULL),(9484,'cs','product_flag_new','Novinka',NULL),(9485,'en','product_flag_new','New',NULL),(9486,'cs','title_tags','Štítky',NULL),(9487,'en','title_tags','Tags',NULL),(9488,'cs','btn_filter_products','##btn_filter_products',NULL),(9489,'en','btn_filter_products','##btn_filter_products',NULL),(9490,'cs','filter_title_price','Cena',NULL),(9491,'en','filter_title_price','Price',NULL),(9492,'cs','value_is_in_store','Skladem',NULL),(9493,'en','value_is_in_store','In store',NULL),(9494,'cs','value_is_new','Novinka',NULL),(9495,'en','value_is_new','News',NULL),(9496,'cs','sort_cheapest','Nejlevnější',NULL),(9497,'en','sort_cheapest','Cheapest',NULL),(9498,'cs','sort_name','A-Z',NULL),(9499,'en','sort_name','A-Z',NULL),(9500,'cs','btn_filter_cancel','##btn_filter_cancel',NULL),(9501,'en','btn_filter_cancel','##btn_filter_cancel',NULL),(9502,'cs','select_file','##select_file',NULL),(9503,'en','select_file','##select_file',NULL),(9504,'cs','add_to_cart','##add_to_cart',NULL),(9505,'en','add_to_cart','##add_to_cart',NULL),(9506,'cs','form_password_empty','##form_password_empty',NULL),(9507,'en','form_password_empty','##form_password_empty',NULL),(9508,'cs','czech-republic','##Czech Republic',NULL),(9509,'en','czech-republic','##Czech Republic',NULL),(9510,'cs','slovakia','##Slovakia',NULL),(9511,'en','slovakia','##Slovakia',NULL),(9512,'cs','please-enter-no-more-than-d-characters','##Please enter no more than %d characters.',NULL),(9513,'en','please-enter-no-more-than-d-characters','##Please enter no more than %d characters.',NULL),(9514,'cs','pname_filter_prefix_116','##pname_filter_prefix_116',NULL),(9515,'en','pname_filter_prefix_116','##pname_filter_prefix_116',NULL),(9518,'cs','pname_filter_prefix_119','##pname_filter_prefix_119',NULL),(9519,'en','pname_filter_prefix_119','##pname_filter_prefix_119',NULL),(9520,'cs','pname_filter_prefix_121','',NULL),(9521,'en','pname_filter_prefix_121','',NULL),(9522,'cs','pname_filter_prefix_127','',NULL),(9523,'en','pname_filter_prefix_127','',NULL),(9524,'cs','pname_filter_prefix_128','##pname_filter_prefix_128',NULL),(9525,'en','pname_filter_prefix_128','##pname_filter_prefix_128',NULL),(9526,'cs','article_reading_time','Délka čtení:',NULL),(9527,'en','article_reading_time','Reading time:',NULL),(9528,'cs','title_categories','Kategorie',NULL),(9529,'en','title_categories','Categories',NULL),(9530,'cs','title_authors','Autoři',NULL),(9531,'en','title_authors','Authors',NULL),(9532,'cs','minute_1','minuta',NULL),(9533,'en','minute_1','minute',NULL),(9534,'cs','minute_2','minuty',NULL),(9535,'en','minute_2','minutes',NULL),(9536,'cs','minute_3','minut',NULL),(9537,'en','minute_3','minutes',NULL),(9538,'cs','form_valid_filled','Toto pole je povinné',NULL),(9539,'en','form_valid_filled','This field is required',NULL),(9540,'cs','title_authors_other','Další autoři',NULL),(9541,'en','title_authors_other','Other authors',NULL),(9542,'cs','btn_show_all','Zobrazit vše',NULL),(9543,'en','btn_show_all','Show all',NULL),(9544,'cs','btn_play','Přehrát video',NULL),(9545,'en','btn_play','Play video',NULL),(9598,'cs','filter_title_new','Novinky',NULL),(9599,'en','filter_title_new','News',NULL),(9600,'cs','filter_title_store','Skladem',NULL),(9601,'en','filter_title_store','Available at store',NULL),(9602,'cs','pname_filter_prefix_131','##pname_filter_prefix_131',NULL),(9603,'en','pname_filter_prefix_131','##pname_filter_prefix_131',NULL),(9604,'cs','pname_filter_prefix_130','##pname_filter_prefix_130',NULL),(9605,'en','pname_filter_prefix_130','##pname_filter_prefix_130',NULL),(9606,'cs','pname_129','##pname_129',NULL),(9607,'en','pname_129','##pname_129',NULL),(9608,'cs','pname_tooltip_129','##pname_tooltip_129',NULL),(9609,'en','pname_tooltip_129','##pname_tooltip_129',NULL),(9610,'cs','pname_unit_129','##pname_unit_129',NULL),(9611,'en','pname_unit_129','##pname_unit_129',NULL),(9612,'cs','pname_filter_prefix_129','##pname_filter_prefix_129',NULL),(9613,'en','pname_filter_prefix_129','##pname_filter_prefix_129',NULL),(9634,'cs','pvalue_6594','L',NULL),(9635,'en','pvalue_6594','L',NULL),(9636,'cs','pvalue_alias_6594','l',NULL),(9637,'en','pvalue_alias_6594','l',NULL),(9638,'cs','pvalue_filter_6594','',NULL),(9639,'en','pvalue_filter_6594','',NULL),(9640,'cs','pvalue_6595','XL',NULL),(9641,'en','pvalue_6595','XL',NULL),(9642,'cs','pvalue_alias_6595','xl',NULL),(9643,'en','pvalue_alias_6595','xl',NULL),(9644,'cs','pvalue_filter_6595','',NULL),(9645,'en','pvalue_filter_6595','',NULL),(9646,'cs','pvalue_6596','XXL',NULL),(9647,'en','pvalue_6596','XXL',NULL),(9648,'cs','pvalue_alias_6596','xxl',NULL),(9649,'en','pvalue_alias_6596','xxl',NULL),(9650,'cs','pvalue_filter_6596','',NULL),(9651,'en','pvalue_filter_6596','',NULL),(9690,'cs','filter_title_velikost','Velikost',NULL),(9691,'en','filter_title_velikost','Size',NULL),(9692,'cs','btn_show','##btn_show',NULL),(9693,'en','btn_show','##btn_show',NULL),(9696,'cs','sort_expensive','Nejdražší',NULL),(9697,'en','sort_expensive','Most expensive',NULL),(9698,'cs','btn_product_detail','Detail produktu',NULL),(9699,'en','btn_product_detail','Product detail',NULL),(9700,'cs','btn_choose_variant','Vybrat variantu',NULL),(9701,'en','btn_choose_variant','Choose variant',NULL),(9702,'cs','title_selected_filters','Použité filtry:',NULL),(9703,'en','title_selected_filters','Selected filters:',NULL),(9704,'cs','btn_cookies_setting','Nastavení cookies',NULL),(9705,'en','btn_cookies_setting','Cookies settings',NULL),(9706,'cs','s','##S',NULL),(9707,'en','s','##S',NULL),(9708,'cs','pname_filter_prefix_115','##pname_filter_prefix_115',NULL),(9709,'en','pname_filter_prefix_115','##pname_filter_prefix_115',NULL),(9730,'cs','pvalue_6597','##pvalue_6597',NULL),(9731,'en','pvalue_6597','##pvalue_6597',NULL),(9732,'cs','pvalue_alias_6597','##pvalue_alias_6597',NULL),(9733,'en','pvalue_alias_6597','##pvalue_alias_6597',NULL),(9734,'cs','pvalue_filter_6597','##pvalue_filter_6597',NULL),(9735,'en','pvalue_filter_6597','##pvalue_filter_6597',NULL),(9736,'cs','a4','##A4',NULL),(9737,'en','a4','##A4',NULL),(9738,'cs','a3','##A3',NULL),(9739,'en','a3','##A3',NULL),(9740,'cs','yes','##yes',NULL),(9741,'en','yes','##yes',NULL),(9742,'cs','btn_prev','##btn_prev',NULL),(9743,'en','btn_prev','##btn_prev',NULL),(9744,'cs','btn_next','##btn_next',NULL),(9745,'en','btn_next','##btn_next',NULL),(9746,'cs','error_invalid_phone','##error_invalid_phone',NULL),(9747,'en','error_invalid_phone','##error_invalid_phone',NULL),(9748,'cs','btn_step2_email_confirm','##btn_step2_email_confirm',NULL),(9749,'en','btn_step2_email_confirm','##btn_step2_email_confirm',NULL),(9750,'cs','cart_empty_title','Váš košík je prázdný',NULL),(9751,'en','cart_empty_title','Your basket is empty',NULL),(9752,'cs','search_tab_trees','##search_tab_trees',NULL),(9753,'en','search_tab_trees','##search_tab_trees',NULL),(9754,'cs','search_tab_blogs','##search_tab_blogs',NULL),(9755,'en','search_tab_blogs','##search_tab_blogs',NULL),(9756,'cs','search_tab_products','##search_tab_products',NULL),(9757,'en','search_tab_products','##search_tab_products',NULL),(9758,'cs','contact_ok','##contact_ok',NULL),(9759,'en','contact_ok','##contact_ok',NULL),(9760,'cs','message_antispam_error','##message_antispam_error',NULL),(9761,'en','message_antispam_error','##message_antispam_error',NULL),(9762,'cs','message_antispam_error_no_js','##message_antispam_error_no_js',NULL),(9763,'en','message_antispam_error_no_js','##message_antispam_error_no_js',NULL),(9764,'cs','search_pages','##search_pages',NULL),(9765,'en','search_pages','##search_pages',NULL),(9766,'cs','search_empty','##search_empty',NULL),(9767,'en','search_empty','##search_empty',NULL),(9768,'cs','search_title','##search_title',NULL),(9769,'en','search_title','##search_title',NULL),(9770,'cs','search_nothing','##search_nothing',NULL),(9771,'en','search_nothing','##search_nothing',NULL);
/*!40000 ALTER TABLE `string` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree`
--

DROP TABLE IF EXISTS `tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL DEFAULT '1',
  `rootId` int(11) NOT NULL,
  `treeParentId` int(11) DEFAULT NULL,
  `parentId` int(11) DEFAULT NULL,
  `level` tinyint(4) DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) DEFAULT NULL,
  `last` tinyint(1) DEFAULT NULL,
  `uid` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` int(11) NOT NULL,
  `createdTime` datetime NOT NULL,
  `createdTimeOrder` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `template` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `type` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `forceNoIndex` int(11) NOT NULL DEFAULT '0',
  `hideInSearch` int(11) NOT NULL DEFAULT '0',
  `hideInSitemap` int(11) NOT NULL DEFAULT '0',
  `annotation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `hideFirstImage` tinyint(1) DEFAULT NULL,
  `links` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoTitleFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoAnnotationFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `seoDescriptionFilter` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `videos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customFieldsJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `productAttachedId` int(11) DEFAULT NULL COMMENT 'produkt voucher napojený na skoleni ',
  `hasLinkedCategories` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idParent` (`parentId`),
  KEY `uid` (`uid`),
  KEY `public` (`public`),
  KEY `FK_tree_mutation` (`mutationId`),
  KEY `FK_tree_tree_parent` (`treeParentId`),
  CONSTRAINT `FK_tree_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_tree_tree_parent` FOREIGN KEY (`treeParentId`) REFERENCES `tree_parent` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `tree_ibfk_1` FOREIGN KEY (`parentId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=461 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree`
--

LOCK TABLES `tree` WRITE;
/*!40000 ALTER TABLE `tree` DISABLE KEYS */;
INSERT INTO `tree` VALUES (1,1,1,1,NULL,0,NULL,1,0,'title',0,'2017-01-27 14:53:52','2017-01-27 14:53:00',4,'2024-11-01 13:39:34',':Front:Homepage:default','common','2013-10-07 23:44:00','2100-01-01 00:00:00','CZ','Úvod','Superadmin','','',1,0,0,0,'','',1,'','','','','','{}','{}',NULL,NULL),(2,2,2,1,NULL,0,NULL,12,0,'title',0,'2022-09-04 20:02:34','2022-09-04 20:02:34',3,'2022-09-05 10:06:22',':Front:Homepage:default','common','2022-09-04 20:02:34','2122-09-04 20:02:34','En','En','En','','',1,0,0,0,'','',NULL,'',NULL,NULL,NULL,'','{}','{}',NULL,NULL),(8,1,1,8,1,1,'1|',5,1,'contact',40,'2017-01-27 14:53:52','2017-01-27 14:53:00',4,'2025-04-04 15:09:21',':Front:Page:contact','common','2021-07-08 17:00:00','2100-01-01 00:00:00','Kontakt','Kontakt','Kontaktujte nás','','',1,0,0,0,'','',1,'','','','','https://www.youtube.com/watch?v=nUwTnJ8yFXY|Tacos','{}','{}',NULL,NULL),(12,1,1,12,43,2,'1|43|',2,1,'cookie',5,'2017-01-24 14:19:44','2017-01-24 14:19:00',11,'2024-07-02 13:31:03',':Front:Page:default','common','2017-01-24 14:19:00','2100-01-01 00:00:00','Prohlášení o používání cookies','Prohlášení o používání cookies','Prohlášení o používání cookies','','',1,0,0,0,'Prohlášení o používání cookies společností ...\r\n','<h2 class=\"left\">Co jsou cookies</h2>\r\n<p class=\"left\">Cookies jsou krátké textové soubory vytvářené webovým serverem a ukládané ve Vašem počítači prostřednictvím prohlížeče. Když se později vrátíte na stejný web, prohlížeč pošle uloženou cookie zpět a server tak získá všechny informace, které si u vás předtím uložil. Cookies využívá pro svou činnost naprostá většina webových stránek.</p>\r\n<h2 class=\"left\">Jak se dělí cookies</h2>\r\n<p class=\"left\">Cookies lze rozdělit podle toho, kdo je k Vám na web umisťuje, tj. na:</p>\r\n<ul class=\"left\">\r\n<li>Cookie první strany (first party cookie) – jejich platnost je omezena na doménu webu, který prohlížíte. Tyto cookies jsou považovány za bezpečnější.</li>\r\n<li>Cookie třetí strany (third party cookie) – jsou umístěny pomocí skriptu z jiné domény. Uživatele tak lze sledovat napříč doménami. Používají se často pro vyhodnocení účinnosti reklamních kanálů.</li>\r\n</ul>\r\n<p class=\"left\">Podle trvanlivosti lze cookies rozdělit na:</p>\r\n<ul>\r\n<li class=\"left\">Krátkodobé (session cookie) – vymažou se z vašeho počítače po zavření prohlížeče.</li>\r\n<li class=\"left\">Dlouhodobé (persistent cookie) – po zavření prohlížeče zůstávají zachovány, vymažou se teprve po uplynutí velmi dlouhé doby (ta záleží na nastavení Vašeho prohlížeče a nastavení cookie). Můžete je také ručně odstranit.</li>\r\n</ul>\r\n<h2 class=\"left\">K čemu cookies používáme</h2>\r\n<p class=\"left\">Na našem webu používáme tyto cookies:</p>\r\n<ul class=\"left\">\r\n<li>Technické – první strany, krátkodobé. Zajišťují základní technickou funkčnost webu, tj. přihlašování, využívání služeb apod.</li>\r\n<li>Google Analytics – první strany, dlouhodobé. Jsou využity ke generování anonymních statistik o používání webu.</li>\r\n<li>\r\n<p>Hotjar – první strany, krátkodobé i dlouhodobé. Pro analýzu návštěvnosti a zlepšení ovladatelnosti tohoto webu používáme nástroj Hotjar.</p>\r\n</li>\r\n</ul>\r\n<p class=\"left\">Do cookies nikdy neumisťujeme citlivá nebo osobní data.</p>\r\n<h2 class=\"left\">Jak lze upravit využívání cookies</h2>\r\n<h3 class=\"left\">Vymazání</h3>\r\n<p class=\"left\">Vymazat můžete cookies ve Vašem prohlížeči – zpravidla bývá umístěno v „Historii“ navštívených stránek.</p>\r\n<h3 class=\"left\">Blokování</h3>\r\n<p class=\"left\">Prohlížeče umožňují umísťování cookies na Vás počítač zablokovat. V takovém případě bude ale funkcionalita těchto stránek omezena. Informace o nastavení ukládání souborů cookies ve Vašem prohlížeči najdete na stránkách poskytovatele konkrétního prohlížeče:</p>\r\n<ul class=\"left\">\r\n<li><a href=\"https://support.google.com/accounts/answer/61416?hl=cs\">Chrome</a></li>\r\n<li><a href=\"https://support.mozilla.org/cs/kb/Práce%20s%20cookies\">Firefox</a></li>\r\n<li><a href=\"http://support.microsoft.com/gp/cookies/cs\">Internet Explorer</a></li>\r\n<li><a href=\"https://support.google.com/xoom/answer/169022?rd=1\">Android</a></li>\r\n</ul>\r\n<p class=\"left\">Další informace o cookies a jejich využití najdete na stránkách <a href=\"http://aboutcookies.org\">AboutCookies.org</a></p>\r\n<h2 class=\"left\">Tento web používá Google Analytics</h2>\r\n<p class=\"left\">Tato stránka používá službu Google Analytics, poskytovanou společností Google, Inc. (dále jen \"Google\"). Služba Google Analytics používá souborů cookies. Informace o užívání stránky spolu s obsahem souboru <a href=\"#cookies\">cookie</a> bude společností Google přenesen a uložen na serverech ve Spojených státech. Google bude užívat těchto informací pro účely vyhodnocování užívání stránky a vytváření zpráv o její aktivitě, určených pro její provozovatele, a pro poskytování dalších služeb týkajících se činností na stránce a užívání internetu vůbec. Google může také poskytnout tyto informace třetím osobám, bude-li to požadováno zákonem nebo budu-li takovéto třetí osoby zpracovávat tyto informace pro Google.</p>\r\n<p class=\"left\">Služba Google Analytics je rozšířena o související reklamní funkce poskytované společností Google, a to:</p>\r\n<ul class=\"left\">\r\n<li>přehledy zobrazení v reklamní síti Google,</li>\r\n<li>remarketing (zobrazování reklam v obsahové síti na základě zhlédnutých produktů),</li>\r\n<li>rozšířené demografické přehledy (reportování anonymních demografických dat).</li>\r\n</ul>\r\n<p class=\"left\">Více informací o zpracování a využití dat najdete ve <a href=\"http://www.google.com/intl/cs/policies/privacy/partners/\">smluvních podmínkách společnosti Google</a></p>\r\n<h2 class=\"left\">Jak zakázat sledování Google Analytics</h2>\r\n<p class=\"left\">Pokud nechcete poskytovat anonymní data o používání webu službě Google Analytics, můžete použít <a href=\"https://tools.google.com/dlpage/gaoptout\">plugin poskytovaný společností Google</a>. Po nainstalování do Vašeho prohlížeče a aktivaci nebudou dále data odesílána.</p>',0,'','','','','','{\"cookies\":[{\"title\":\"Cookies\",\"text\":\"<p>Společnost <span>SPOLEČNOST s.r.o.</span> na svých webových stránkách používá cookies. K použití některých cookies je vyžadován Váš souhlas. Souhlas udělíte klinknutím na tlačítko „OK“. Použití cookies můžete také odmítnout. Povolení pouze vybraných cookies nebo jejich změnu provedete prostřednictvím tlačítka „Nastavení“.</p>\",\"btnSetPreferences\":\"Nastavení\",\"btnReject\":\"Odmítnout\",\"btnConsentAndContinuation\":\"Souhlas\",\"consentsTitle\":\"Nastavení preferencí\",\"necessarilyLink\":\"Technické cookies\",\"necessarilyText\":\"<p><span>Technické cookies jsou nezbytné pro správné fungování webu SPOLEČNOST s.r.o. a všech funkcí, které nabízí. Nepožadujeme Váš souhlas s využitím technických cookies na našem webu. Z tohoto důvodu technické cookies nemohou být individuálně deaktivovány nebo aktivovány.</span></p>\",\"preferenceslLink\":\"Statistické cookies\",\"preferencesText\":\"<p><span>Tyto cookies sbírají a analyzují provoz webu a používání stránky SPOLEČNOST s.r.o. uživateli. Tyto cookies jsou slouží ke zlepšování fungování stránky.</span></p>\",\"analyticsLink\":\"Analytické cookies\",\"analyticsText\":\"<p><span>Tyto cookies sbírají a analyzují provoz webu a používání stránky SPOLEČNOST s.r.o. v reálném čase. Data chování v reálném čase umožňuji vždy nabízet relevantní obsah a předcházet specifickým chybám zobrazení.</span></p>\",\"marketingLink\":\"Marketingové cookies\",\"marketingText\":\"<p><span>SPOLEČNOST s.r.o. potřebuje Váš souhlas s užitím statistických a analytických cookies. Při neudělení souhlasu nebudou statistické a analytické cookies do Vašeho koncového zařízení uloženy. Stisknutím tlačítka“ Povolit vše“ udělujete souhlas k užití všech cookies na webu SPOLEČNOST s.r.o. Stisknutém tlačítka „Povolit vybrané“ souhlasíte s užitím Vámi vybraných cookies. Stisknutím tlačítka „Odmítnout vše“ odmítáte užití všech cookies umístěných na webu SPOLEČNOST s.r.o. s výjimkou technických cookies, které nemohou být individuálně deaktivovány nebo aktivovány.</span></p>\",\"btnConfirmSelected\":\"Potvrdit vybrané\",\"btnAcceptEverything\":\"Přijmout vše\"}]}','{}',NULL,NULL),(21,1,1,21,1,1,'1|',1,0,'eshop',0,'2018-09-05 17:11:03','2018-09-05 17:11:00',11,'2024-07-25 21:42:14',':Front:Catalog:default','catalog','2018-09-05 17:11:00','2118-09-05 17:11:00','E-shop','E-shop','E-shop a půjčovna','','',1,0,0,0,'Anotace stránky e-shop','',1,'','','','','','{\"parameterForFilter\":[{\"visibleParameters\":[{\"indexable\":true,\"visibleCount\":\"3\",\"parameter\":121}]}]}','{}',NULL,0),(26,1,1,26,72,2,'1|72|',8,1,'resetPassword',0,'2018-02-08 02:46:27','2018-02-08 02:46:27',12,'2021-06-21 18:22:30',':Front:User:resetPassword','common','2018-02-08 02:46:27','2118-02-08 02:46:27','Reset hesla','Změna hesla','Změna hesla','','',1,0,0,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(37,1,1,37,72,2,'1|72|',6,1,'userLogin',36,'2017-03-15 15:05:46','2017-03-15 15:05:46',2319,'2018-09-05 17:42:28',':Front:User:login','common','2017-03-15 15:05:46','2100-01-01 00:00:00','Přihlášení','Přihlášení','Přihlášení','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'',NULL,NULL,NULL,NULL),(40,1,1,40,43,2,'1|43|',1,1,'conditions',5,'2017-01-24 13:44:14','2017-01-24 13:44:14',4,'2019-01-14 10:40:50',':Front:Page:default','common','2017-01-24 13:44:14','2100-01-01 00:00:00','Obchodní podmínky','Obchodní podmínky','Obchodní podmínky','','',1,0,0,0,'Anotace','<p>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Nunc dapibus tortor vel mi dapibus sollicitudin. Integer malesuada. Nullam justo enim, consectetuer nec, ullamcorper ac, vestibulum in, elit. Fusce tellus. Nunc dapibus tortor vel mi dapibus sollicitudin. Nulla est. Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Fusce tellus odio, dapibus id fermentum quis, suscipit id erat. Curabitur sagittis hendrerit ante. Sed convallis magna eu sem. Fusce suscipit libero eget elit. Nullam sit amet magna in magna gravida vehicula. Etiam neque. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Curabitur bibendum justo non orci. Phasellus faucibus molestie nisl.</p>\r\n<p>Maecenas sollicitudin. Curabitur sagittis hendrerit ante. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Ut tempus purus at lorem. Etiam commodo dui eget wisi. Aenean placerat. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Morbi imperdiet, mauris ac auctor dictum, nisl ligula egestas nulla, et sollicitudin sem purus in lacus. Fusce consectetuer risus a nunc. Nunc dapibus tortor vel mi dapibus sollicitudin. Nunc tincidunt ante vitae massa. Duis risus. Praesent in mauris eu tortor porttitor accumsan. Nulla non arcu lacinia neque faucibus fringilla. Praesent id justo in neque elementum ultrices. In laoreet, magna id viverra tincidunt, sem odio bibendum justo, vel imperdiet sapien wisi sed libero. Nunc dapibus tortor vel mi dapibus sollicitudin.</p>\r\n<p>Etiam bibendum elit eget erat. Nunc auctor. Nullam dapibus fermentum ipsum. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Maecenas lorem. Fusce suscipit libero eget elit. In dapibus augue non sapien. Fusce wisi. Nunc tincidunt ante vitae massa. Mauris tincidunt sem sed arcu. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Aliquam ante. Nulla pulvinar eleifend sem. Duis sapien nunc, commodo et, interdum suscipit, sollicitudin et, dolor. Duis bibendum, lectus ut viverra rhoncus, dolor nunc faucibus libero, eget facilisis enim ipsum id lacus. Mauris tincidunt sem sed arcu. Curabitur vitae diam non enim vestibulum interdum. Morbi leo mi, nonummy eget tristique non, rhoncus non leo.</p>\r\n<p>Cras elementum. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. Aenean vel massa quis mauris vehicula lacinia. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Integer rutrum, orci vestibulum ullamcorper ultricies, lacus quam ultricies odio, vitae placerat pede sem sit amet enim. Nulla quis diam. Nulla est. Aliquam erat volutpat. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nulla accumsan, elit sit amet varius semper, nulla mauris mollis quam, tempor suscipit diam nulla vel leo. Vivamus porttitor turpis ac leo. Etiam dui sem, fermentum vitae, sagittis id, malesuada in, quam. Fusce consectetuer risus a nunc. Nullam eget nisl. Mauris suscipit, ligula sit amet pharetra semper, nibh ante cursus purus, vel sagittis velit mauris vel metus. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>\r\n<p>Maecenas ipsum velit, consectetuer eu lobortis ut, dictum at dui. Etiam egestas wisi a erat. Nulla turpis magna, cursus sit amet, suscipit a, interdum id, felis. Nunc auctor. Proin in tellus sit amet nibh dignissim sagittis. Integer vulputate sem a nibh rutrum consequat. In dapibus augue non sapien. Proin pede metus, vulputate nec, fermentum fringilla, vehicula vitae, justo. Nunc auctor. Etiam ligula pede, sagittis quis, interdum ultricies, scelerisque eu. Etiam dictum tincidunt diam. Fusce dui leo, imperdiet in, aliquam sit amet, feugiat eu, orci.</p>',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(42,1,1,42,43,2,'1|43|',3,1,'search',1,'2015-07-29 09:42:10','2015-07-29 09:42:10',4,'2018-10-30 08:07:58',':Front:Search:default','common','2015-07-29 09:42:10','2100-01-01 00:00:00','Výsledky vyhledávání','Výsledky vyhledávání','Výsledky vyhledávání','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(43,1,1,43,1,1,'1|',7,0,'',0,'2018-09-05 17:18:45','2018-09-05 17:18:45',2319,'2018-09-05 17:20:46',':Front:Page:default','common','2018-09-05 17:18:45','2118-09-05 17:18:45','Stránky mimo hlavní navigaci','Stránky mimo hlavní navigaci','Stránky mimo hlavní navigaci','','',0,0,0,0,'','',0,'',NULL,NULL,NULL,'',NULL,NULL,NULL,NULL),(72,1,1,72,1,1,'1|',6,0,'userSection',1,'2013-11-06 15:34:00','2013-11-06 15:34:00',1,'2024-04-09 14:29:22',':Front:User:default','common','2013-11-06 15:34:00','2100-01-01 00:00:00','Uživatelská sekce','Uživatelská sekce','Uživatelská sekce','','',1,0,0,0,'','<h2 class=\"text-center\">Děkujeme za přihlášení</h2>\r\n<p class=\"text-center\">Vítejte ve svém účtu.</p>',0,'http://wwww|www|','','','','https://www.youtube.com/watch?v=CPjyJiPNnmk|','{\"userMenuUnloggedUser\":[{\"tree\":37},{\"tree\":76},{\"tree\":74},{}],\"userMenuLoggedUser\":[{\"tree\":72},{\"tree\":75},{\"tree\":455},{\"tree\":456},{\"tree\":405}],\"userSideMenu\":[{\"tree\":72},{\"tree\":413},{\"tree\":75},{\"tree\":455},{\"tree\":456},{\"tree\":405}]}','{}',NULL,NULL),(74,1,1,74,72,2,'1|72|',7,1,'lostPassword',1,'2013-11-06 15:36:28','2013-11-06 15:36:28',9,'2021-06-25 11:31:04',':Front:User:lostPassword','common','2013-11-06 15:36:28','2100-01-01 00:00:00','Zapomenuté heslo','Zapomenuté heslo','Zapomenuté heslo','','',1,0,0,0,'Zapomněli jste své heslo? Nevadí, na e-mail vám pošleme nové.\r\nStačí zadat e-mail, který jste uvedli při registraci.\r\n','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(75,1,1,75,72,2,'1|72|',1,1,'userProfil',1,'2013-11-06 17:17:48','2013-11-06 17:17:48',12,'2021-05-18 12:50:11',':Front:User:profil','common','2013-11-06 17:17:48','2100-01-01 00:00:00','Můj účet','Můj účet','Můj účet','','',1,0,0,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(76,1,1,76,72,2,'1|72|',5,1,'registration',1,'2013-11-07 06:49:48','2013-11-07 06:49:48',4,'2018-10-23 17:22:53',':Front:User:registration','common','2013-11-07 06:49:48','2100-01-01 00:00:00','Registrace','Registrace','Registrace','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(77,1,1,77,21,2,'1|21|',2,0,'eshop-1',0,'2018-09-06 12:43:30','2018-09-06 12:43:00',11,'2024-07-24 15:30:32',':Front:Catalog:default','catalog','2018-09-06 12:43:00','2118-09-06 12:43:00','Diáře','Diáře','Diáře','','',1,0,0,0,'Anotace','<p>Obsah</p>',1,'','<select> aaa','<select>','<select>','','{}','{}',NULL,0),(78,1,1,78,21,2,'1|21|',4,0,'eshop-2',0,'2018-09-06 12:46:12','2018-09-06 12:46:00',11,'2024-07-24 15:30:41',':Front:Catalog:default','catalog','2018-09-06 12:46:00','2118-09-06 12:46:00','Trička','Trička','Trička','','',1,0,0,0,'','',1,'','','','','','{}','{}',NULL,0),(94,1,1,94,78,3,'1|21|78|',1,1,'',0,'2018-10-10 09:54:13','2018-10-10 09:54:00',11,'2024-07-24 15:30:58',':Front:Catalog:default','catalog','2018-10-10 09:54:00','2118-10-10 09:54:00','Sportovní','Sportovní','Sportovní','','',1,0,0,0,'','',0,'','Nože s hodnotou <select> samostatně a <značkou [manufacturer], která neexistuje> <s délkou čepele [select]>','Nože <značky [manufacturer]> <s délkou čepele [select] cm>','Nože <značky [manufacturer]> <s délkou čepele [select] cm>','','{}','{}',NULL,0),(102,1,1,102,1,1,'1|',3,1,'aboutUs',0,'2018-10-10 13:56:24','2018-10-10 13:56:00',11,'2024-07-24 15:37:25',':Front:Page:default','common','2021-07-08 17:00:00','2118-10-10 13:56:00','O nás','O nás','O nás','','',1,0,0,0,'Anotace stránky O nás','<p>Suspendisse link vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus!</p>',0,'','','','','https://www.youtube.com/watch?v=B8P2fewY4n4|Google mapy\nhttps://www.youtube.com/watch?v=o3_TEKZ-axw|When JavaScript bytes','{}','{}',NULL,NULL),(255,1,1,255,43,2,'1|43|',4,1,'personalData',0,'2018-12-18 10:07:15','2018-12-18 10:07:15',4,'2019-03-05 09:09:48',':Front:Page:default','common','2018-12-18 10:07:14','2118-12-18 10:07:14','Zpracováním osobních údajů','Zpracováním osobních údajů','Zpracováním osobních údajů','','',1,0,0,0,'aaaa','<p>ddddddd</p>',0,'','','','','','{}',NULL,NULL,NULL),(394,1,1,394,398,2,'1|398|',1,1,'popupContact',0,'2019-03-21 13:35:53','2019-03-21 13:35:52',3,'2021-07-08 12:11:49',':Front:Internal:default','common','2019-03-21 13:35:52','2119-03-21 13:35:52','Popup','Popup','Popup','','',1,0,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(398,1,1,398,1,1,'1|',9,0,'systemPageId',0,'2020-09-25 15:13:45','2020-09-25 15:13:44',3,'2021-07-08 12:12:18',':Front:Homepage:default','common','2020-09-25 15:13:44','2120-09-25 15:13:44','System pages (only develpers)','System pages (only develpers)','System pages (only develpers)','','',0,0,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(405,1,1,405,72,2,'1|72|',1,1,'userLogout',0,'2021-05-19 12:36:35','2021-05-19 12:36:35',12,'2021-05-19 12:37:10',':Front:User:default','common','2021-05-19 12:36:35','2121-05-19 12:36:35','Odhlášení','Odhlásit','Odhlásit','','',1,1,1,1,'','',0,'','','','','','{}',NULL,NULL,NULL),(413,1,1,413,72,2,'1|72|',1,1,'userChangePassword',0,'2021-06-21 18:21:49','2021-06-21 18:21:49',12,'2021-06-21 19:06:46',':Front:User:default','common','2021-06-21 18:21:49','2121-06-21 18:21:49','Změna hesla','Změna hesla','Změna hesla','','',1,0,1,1,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(445,1,1,445,398,2,'1|398|',1,1,'',0,'2021-07-14 17:23:51','2021-07-14 17:23:51',1,'2021-07-14 17:24:03',':Front:Page:styleguide','common','2021-07-14 17:23:51','2121-07-14 17:23:51','Styleguide','Styleguide','Styleguide','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(446,1,1,446,1,1,'1|',1,0,'blog',0,'2021-08-16 16:41:04','2021-08-16 16:41:00',11,'2024-07-24 10:33:36',':Blog:Front:Blog:default','common','2021-08-16 16:41:00','2121-08-16 16:41:00','Blog','Blog','Blog','','',1,0,0,0,'Anotace stránky blog','',0,'',NULL,NULL,NULL,'','{}','{}',NULL,NULL),(447,1,1,447,446,2,'1|446|',1,1,'',0,'2021-08-16 16:42:05','2021-08-16 16:42:00',4,'2024-09-05 12:19:30',':Blog:Front:Blog:default','common','2021-08-16 16:42:00','2121-08-16 16:42:00','Kategorie 1','Kategorie 1','Kategorie 1','','',1,0,0,0,'Popis kategorie číslo 1','',0,'',NULL,NULL,NULL,'','{}','{}',NULL,NULL),(448,1,1,448,446,2,'1|446|',1,1,'',0,'2021-08-16 16:42:34','2021-08-16 16:42:34',5,'2021-08-23 15:44:30',':Blog:Front:Blog:default','common','2021-08-16 16:42:34','2121-08-16 16:42:34','Kategorie 2','Kategorie 2','Kategorie 2','','',1,0,0,0,'','',0,'',NULL,NULL,NULL,'','{}',NULL,NULL,NULL),(449,1,1,449,1,1,'1|',1,0,'authors',0,'2021-08-24 10:20:43','2021-08-24 10:20:00',11,'2024-07-24 10:33:44',':Author:Front:Author:default','common','2021-08-24 10:20:00','2121-08-24 10:20:00','Autoři','Autoři','Autoři','','',1,0,0,0,'Anotace stránky autoři','',0,'',NULL,NULL,NULL,'','{}','{}',NULL,NULL),(450,1,1,450,398,2,'1|398|',3,1,'precart',0,'2024-03-01 11:44:30','2024-03-01 11:44:00',35,'2024-03-01 12:03:59',':Front:Precart:default','common','2024-03-01 11:44:00','2124-03-01 11:44:00','Predkošík','Predkošík','Predkošík','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(451,1,1,451,398,2,'1|398|',4,0,'cart',0,'2024-03-01 13:44:03','2024-03-01 13:44:00',35,'2024-03-01 13:44:29',':Front:Order:default','common','2024-03-01 13:44:00','2124-03-01 13:44:00','Košík','Košík','Košík','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(452,1,1,450,451,3,'1|398|451|',1,1,'step1',0,'2024-03-14 09:28:31','2024-03-14 09:28:00',1,'2024-03-20 16:27:46',':Front:Order:step1','common','2024-03-14 09:28:00','2124-03-14 09:28:00','Doprava a platba','Doprava a platba','Doprava a platba','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(453,1,1,450,451,3,'1|398|451|',2,1,'step2',0,'2024-03-14 09:29:30','2024-03-14 09:29:00',1,'2024-03-20 16:28:17',':Front:Order:step2','common','2024-03-14 09:29:00','2124-03-14 09:29:00','Doručovací údaje','Doručovací údaje','Doručovací údaje','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(454,1,1,452,451,3,'1|398|451|',3,1,'step3',0,'2024-03-14 09:30:15','2024-03-14 09:30:00',1,'2024-03-20 16:36:50',':Front:Order:step3','common','2024-03-14 09:30:00','2124-03-14 09:30:00','Objednávka dokončena','Objednávka dokončena','Objednávka dokončena','','',1,1,1,0,'','<p>Děkujeme vám za objednávku. O jejím vyřizování budete průběžně informováni e-mailem nebo telefonicky.</p>',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(455,1,1,451,72,2,'1|72|',9,1,'userAddress',0,'2024-03-20 08:56:26','2024-03-20 08:56:00',33,'2024-03-20 09:05:29',':Front:User:default','common','2024-03-20 08:56:00','2124-03-20 08:56:00','Moje adresy','Moje adresy','Moje adresy','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(456,1,1,452,72,2,'1|72|',10,1,'userOrderHistory',0,'2024-03-20 08:58:38','2024-03-20 08:58:00',33,'2024-03-20 09:02:48',':Front:User:orderHistory','common','2024-03-20 08:58:00','2124-03-20 08:58:00','Historie objednávek','Historie objednávek','Historie objednávek','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(457,1,1,453,72,2,'1|72|',11,1,'userOrderHistoryDetail',0,'2024-03-20 09:02:19','2024-03-20 09:02:00',33,'2024-03-20 09:06:13',':Front:User:orderHistory','common','2024-03-20 09:02:00','2124-03-20 09:02:00','Historie objednávek detail','Historie objednávek detail','Historie objednávek detail','','',1,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{}',NULL,NULL),(459,1,1,456,43,2,'1|43|',5,1,'ccDemo',0,'2024-07-24 15:38:12','2024-07-24 15:38:00',11,'2024-08-14 21:07:02',':Front:Page:default','common','2024-07-24 15:38:00','2124-07-24 15:38:00','Rozcestník komponent','Rozcestník komponent','Rozcestník komponent','','',1,0,0,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,'{}','{\"gallery____o4K0OwJkMqrIqUCY17Bch\":[{\"images\":[\"104\",\"105\",\"110\",\"109\",\"108\"]}],\"content____TVKLJ_urNtdTKy0nBnunb\":[{\"content\":\"<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vulputate libero massa, ac pellentesque libero convallis a. Quisque laoreet purus diam, id laoreet lectus pharetra quis. Etiam convallis non magna non rutrum. Cras at ultricies mi, sit amet blandit arcu. Etiam ac eros non nisi commodo venenatis ut et felis. Vestibulum sit amet vulputate orci, in volutpat velit. Integer vel augue dui. Nulla eu venenatis nisl.</p>\\n<p>Proin nec lobortis dui, a blandit ligula. Nulla dapibus, ante elementum consectetur imperdiet, eros purus consectetur enim, nec facilisis quam felis a leo. Integer rhoncus ornare mi sed maximus. Nunc orci ex, molestie eget lectus sit amet, consequat eleifend odio. Nunc eu facilisis elit, et suscipit orci. Suspendisse potenti. Fusce ultrices mattis elit vitae laoreet. Proin et congue lacus, eget placerat tortor.</p>\\n<p>Aliquam leo sem, eleifend ac erat nec, facilisis lacinia lacus. Maecenas lacinia rhoncus gravida. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Pellentesque a porta arcu. Curabitur pharetra sem ut leo scelerisque hendrerit. Donec non interdum risus. Cras arcu metus, imperdiet eget mi et, elementum finibus justo. Nulla lacinia consectetur nisi at tristique. Aliquam sed massa eget felis facilisis aliquam vel non tellus.</p>\"}],\"video____hck5unLhIhC2XQEcVZISA\":[{\"poster\":\"105\",\"video\":[{\"toggle\":\"link\",\"link\":[{\"link\":\"https://www.youtube.com/watch?v=InPM7HUHIl4&t=1s\"}]}]}],\"video____nilDCTJUX3bVUWbRu7bMG\":[{\"poster\":\"100\",\"video\":[{\"toggle\":\"link\",\"link\":[{\"link\":\"https://vimeo.com/341987706\"}]}]}],\"video____wbQlv02yA1qDTMk_n01Ts\":[{\"autoplay\":true,\"video\":[{\"toggle\":\"link\",\"link\":[{\"link\":\"https://www.youtube.com/watch?v=InPM7HUHIl4&t=1s\"}]}]}],\"video____DMd8inExRAldKKqxXfs94\":[{\"poster\":\"105\",\"autoplay\":true,\"video\":[{\"toggle\":\"link\",\"link\":[{\"link\":\"https://vimeo.com/341987706\"}]}]}],\"video____N4iMU17RZvuNwelMv0dSh\":[{\"poster\":\"104\",\"autoplay\":true,\"video\":[{\"toggle\":\"file\",\"file\":[{\"file\":{\"id\":106,\"name\":\"4440931-hd_1920_1080_25fps.mp4\"}}]}]}]}',NULL,NULL),(460,1,1,457,21,2,'1|21|',5,1,'',0,'2024-07-26 09:34:24','2024-07-26 09:34:00',11,'2024-07-26 09:34:41',':Front:Catalog:default','catalog','2024-07-26 09:34:00','2124-07-26 09:34:00','Ostatní','Ostatní','Ostatní','','',1,0,0,0,'','',NULL,NULL,'','','',NULL,'{}','{}',NULL,0);
/*!40000 ALTER TABLE `tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_file`
--

DROP TABLE IF EXISTS `tree_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fileId` int(11) NOT NULL COMMENT 'idfile',
  `treeId` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `size` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fileId_treeId` (`fileId`,`treeId`),
  KEY `idtree_idx` (`treeId`),
  KEY `idfile_idx` (`fileId`),
  CONSTRAINT `tree_file_ibfk_1` FOREIGN KEY (`fileId`) REFERENCES `file` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tree_file_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_file`
--

LOCK TABLES `tree_file` WRITE;
/*!40000 ALTER TABLE `tree_file` DISABLE KEYS */;
INSERT INTO `tree_file` VALUES (2,37,102,'girl-with-red-hat-sLcVe47YUJI-unsplash.jpg','/data/files/37-girl-with-red-hat-slcve47yuji-unsplash.jpeg',2,5611172),(3,36,102,'Sample image','/data/files/36-toby-elliott-m3srhemrmbq-unsplash.jpeg',1,2864450),(4,40,102,'payment-detail-T644971264.pdf','/data/files/40-payment-detail-t644971264.pdf',0,3045),(5,41,102,'payment-detail-T738633803.pdf','/data/files/41-payment-detail-t738633803.pdf',41,3034),(6,102,450,'Název souboru','/data/files/102-clinic-material-4.pdf',102,1061611);
/*!40000 ALTER TABLE `tree_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_parent`
--

DROP TABLE IF EXISTS `tree_parent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_parent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=458 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_parent`
--

LOCK TABLES `tree_parent` WRITE;
/*!40000 ALTER TABLE `tree_parent` DISABLE KEYS */;
INSERT INTO `tree_parent` VALUES (1),(8),(12),(21),(26),(37),(40),(42),(43),(72),(74),(75),(76),(77),(78),(94),(102),(255),(394),(398),(405),(413),(445),(446),(447),(448),(449),(450),(451),(452),(453),(454),(456),(457);
/*!40000 ALTER TABLE `tree_parent` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_product`
--

DROP TABLE IF EXISTS `tree_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `productId` int(11) NOT NULL,
  `treeId` int(11) NOT NULL,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `productId_treeId_type` (`productId`,`treeId`,`type`),
  KEY `idproduct` (`productId`),
  KEY `idtree` (`treeId`),
  CONSTRAINT `tree_product_ibfk_2` FOREIGN KEY (`treeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tree_product_ibfk_5` FOREIGN KEY (`productId`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_product`
--

LOCK TABLES `tree_product` WRITE;
/*!40000 ALTER TABLE `tree_product` DISABLE KEYS */;
INSERT INTO `tree_product` VALUES (4,7,102,'attachedToProduct',0),(8,7,408,'attachedToProduct',0);
/*!40000 ALTER TABLE `tree_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tree_tree`
--

DROP TABLE IF EXISTS `tree_tree`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tree_tree` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mainTreeId` int(11) NOT NULL,
  `attachedTreeId` int(11) NOT NULL,
  `type` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `sort` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mainTreeId_attachedTreeId_type` (`mainTreeId`,`attachedTreeId`,`type`),
  KEY `idtree` (`mainTreeId`),
  KEY `id_attached_tree` (`attachedTreeId`),
  CONSTRAINT `tree_tree_ibfk_3` FOREIGN KEY (`mainTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tree_tree_ibfk_4` FOREIGN KEY (`attachedTreeId`) REFERENCES `tree` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tree_tree`
--

LOCK TABLES `tree_tree` WRITE;
/*!40000 ALTER TABLE `tree_tree` DISABLE KEYS */;
INSERT INTO `tree_tree` VALUES (8,189,417,'linkedCategory',0);
/*!40000 ALTER TABLE `tree_tree` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT 'user',
  `firstname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `lastname` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `phone` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `street` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `city` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `zip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `stateId` int(11) DEFAULT NULL,
  `company` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `ic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `dic` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` int(11) DEFAULT NULL,
  `createdTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `lastLogin` datetime DEFAULT NULL,
  `customAddressJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `orderCount` int(11) DEFAULT '0',
  `priceLevelId` int(11) DEFAULT '1',
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  `googleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `stateId` (`stateId`),
  KEY `FK_user_price_level` (`priceLevelId`),
  CONSTRAINT `FK_user_price_level` FOREIGN KEY (`priceLevelId`) REFERENCES `price_level` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=46 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (1,'<EMAIL>','$2y$10$UdLmJe8FuBJ2fsjDptPkvuVUCZN0z56NGtJGUsYN2nL7WQqczxIoK','developer','Tomáš','Hejč','','','','',61,'','12345678','dic',NULL,NULL,NULL,'2024-07-08 15:15:38','2024-04-10 07:48:45','[{\"invFirstname\":\"Eli\\u0161ka\",\"invLastname\":\"Plitzov\\u00e1\",\"invPhone\":\"*********\",\"invStreet\":\"Kl\\u00ed\\u010dova\",\"invCity\":\"Brno\",\"invZip\":\"61800\",\"invState\":\"61\",\"invCompany\":null,\"invIc\":null,\"invDic\":null,\"delFirstname\":null,\"delLastname\":null,\"delCompany\":null,\"delPhone\":null,\"delStreet\":null,\"delCity\":null,\"delZip\":null,\"delState\":null},{\"invFirstname\":\"Eli\\u0161ka\",\"invLastname\":\"Plitzov\\u00e1\",\"invPhone\":\"*********\",\"invStreet\":\"Kl\\u00ed\\u010dova test\",\"invCity\":\"Brno\",\"invZip\":\"61800\",\"invState\":\"61\",\"invCompany\":\"test\",\"invIc\":\"123\",\"invDic\":\"456\",\"delFirstname\":null,\"delLastname\":null,\"delCompany\":null,\"delPhone\":null,\"delStreet\":null,\"delCity\":null,\"delZip\":null,\"delState\":null}]',9,1,NULL,NULL),(2,'<EMAIL>','$2y$10$dOhmKS9Rxc2UlzVHzZloiuZLlz9lp4v76KJFDxGLkcxaPp9LfO6/.','admin','Podpora','','','','','',1,'','','',NULL,NULL,NULL,'2021-06-22 17:50:18',NULL,NULL,0,1,NULL,NULL),(3,'<EMAIL>','$2a$07$7aze1h55fxd7z7n6srlboepOlzysqQl6qA6ojP9a13vCqv7vyKlvm','developer','Vojta','Brtník','*********0','street','city','90210',NULL,'company','ic','dic',NULL,NULL,NULL,'2019-02-08 16:11:33','2018-03-14 20:54:57',NULL,5,1,NULL,NULL),(4,'<EMAIL>','$2y$10$gcTPV3c/3l7fxjm1Cj1S/..vHkWXZ6WPGzfHRNlhdxPxyW0hwDTfm','developer','Ludek1','Hradil','*********','test1a','test','22344',NULL,'','','',NULL,'2017-12-06 12:19:09',NULL,'2021-07-02 17:38:22','2024-05-07 10:47:12',NULL,26,1,NULL,NULL),(5,'<EMAIL>','$2y$10$m43UWmYwoxb9pmTbhUfal.ywWa0MP1doEqpBxLbThVEVlyF0bbS5i','developer','Tomáš','Krejčí','*********','Hněvkovského 65, Podnikatelský inkubátor PI 2 (SuperKodéři)','Brno','61700',1,'SuperKodéři s.r.o.','','',NULL,NULL,NULL,'2021-06-23 12:51:03','2021-07-07 11:06:27','[{\"firstname\":\"Tom\\u00e1\\u0161\",\"lastname\":\"Krej\\u010d\\u00ed\",\"company\":\"SuperKod\\u00e9\\u0159i s.r.o.\",\"phone\":\"*********\",\"phonePrefix\":\"420\",\"street\":\"Hn\\u011bvkovsk\\u00e9ho 65, Podnikatelsk\\u00fd inkub\\u00e1tor PI 2 (SuperKod\\u00e9\\u0159i)\",\"city\":\"Brno\",\"zip\":\"61700\",\"state\":\"1\"},{\"firstname\":\"Tom\\u00e1\\u0161\",\"lastname\":\"Krej\\u010d\\u00ed\",\"company\":\"SuperKod\\u00e9\\u0159i s.r.o.\",\"phone\":\"*********\",\"phonePrefix\":\"420\",\"street\":\"Nikol\\u010dice\",\"city\":\"Nikol\\u010dice\",\"zip\":\"69171\",\"state\":\"1\"}]',0,1,NULL,NULL),(6,'<EMAIL>','$2y$10$RZKM2zqN23BWizGUyyE1seKg81SlbyMuYG5s3P87s9njyOr82G8JO','admin','info','','','','','',NULL,'','','',NULL,'2019-04-11 14:55:23',NULL,'2021-06-10 17:28:49',NULL,NULL,0,1,NULL,NULL),(8,'<EMAIL>','$2y$10$9I1ZNifdMDGWJJlOroT3cu4fJqYdnmBCFizLE0Ij2N3IJa7wChwQu','user','Tomáš','Hejč','*********','9. května 45a','Blansko','67801',1,'SuperKodéři s.r.o.','123','zc123',NULL,'2019-02-12 22:11:28',NULL,'2021-06-23 09:47:05','2021-06-10 17:53:58',NULL,1,1,NULL,NULL),(9,'<EMAIL>','$2y$10$iX75WQo8O1Pp.0ay3JrPeulPhNILC6KsDzx8OOtjTHAfma8HkL/r6','developer','Petr','Goca','*********','Hněvkovského 65','Brno','61700',1,'','','',NULL,'2019-01-07 13:44:58',NULL,'2025-05-13 15:02:15','2021-06-25 09:07:31','[{\"firstname\":\"Petr 1\",\"lastname\":\"Goca 1\",\"company\":\"Firma 1\",\"phone\":\"*********\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 1\",\"city\":\"M\\u011bsto 1\",\"zip\":\"60001\",\"state\":\"1\"},{\"firstname\":\"Petr 2\",\"lastname\":\"Goca 2\",\"company\":\"\",\"phone\":\"*********\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 2\",\"city\":\"M\\u011bsto 2\",\"zip\":\"60002\",\"state\":\"1\"},{\"firstname\":\"Petr 3\",\"lastname\":\"Goca 3\",\"company\":\"\",\"phone\":\"\",\"phonePrefix\":\"CZ\",\"street\":\"Ulice 3\",\"city\":\"\",\"zip\":\"\",\"state\":\"1\"}]',7,1,NULL,NULL),(11,'<EMAIL>','$2y$10$k3kavdbI99dp9aMYYig9WOWHKZHozPINdpmiK.1fjeQ5GmAuRI7NG','developer','Eliška','Plitzová','*********','Klíčova','Brno','61800',61,'','','',NULL,'2020-03-11 14:43:51',NULL,'2024-07-26 07:31:17','2024-06-13 07:43:12','[{\"invFirstname\":\"Eli\\u0161ka\",\"invLastname\":\"Plitzov\\u00e1\",\"invPhone\":\"*********\",\"invStreet\":\"Kl\\u00ed\\u010dova\",\"invCity\":\"Brno\",\"invZip\":\"61800\",\"invState\":61,\"invCompany\":null,\"invIc\":null,\"invDic\":null,\"delFirstname\":null,\"delLastname\":null,\"delCompany\":null,\"delPhone\":null,\"delStreet\":null,\"delCity\":null,\"delZip\":null,\"delState\":null}]',0,1,NULL,NULL),(12,'<EMAIL>','$2y$10$XgxBed2akfS7T9N.xXJ./OBnIMitcNrnRTJP6dSWB7v1yyMTp8dCm','developer','Jan','Kočiš','+420606123456','test','test','59266',1,'','','',NULL,'2020-12-07 14:26:46',NULL,'2021-07-14 11:35:28','2021-07-14 11:35:14','[{\"firstname\":\"Jan\",\"lastname\":\"Ko\\u010di\\u0161\",\"company\":\"\",\"phone\":\"+420606559677\",\"street\":\"V\\u00edr 274\",\"city\":\"V\\u00edr\",\"zip\":\"59266\",\"state\":\"1\"}]',4,1,NULL,NULL),(13,'<EMAIL>','$2y$10$2Mcj0QpAdj4zNyB5vyd6jO82cU1mvfjrN6M1CVnT/1G/z0D1n.oJG','developer','Petr','Grochál','*********0','street','city','90210',NULL,'company','ic','dic',NULL,NULL,NULL,'2021-05-21 10:21:23','2018-03-14 20:54:57',NULL,5,1,NULL,NULL),(17,'<EMAIL>','$2y$10$1m5AaCttSPOgmkIUkN.rRelepjPfGFmb9B8aYj8u8Psk2HhaEzXMy','user','','','','','','',NULL,'','','',NULL,'2021-06-14 17:29:07',NULL,'2021-06-25 10:53:38',NULL,NULL,0,1,NULL,NULL),(28,'<EMAIL>','$2y$10$OAtxFog3Z2FkogkiEIBLY.ZRSr6mRVnXoUM883rhOrzToHEfBDPDK','developer','Jiří','Pudil','','','','',1,'','','',NULL,'2022-01-04 19:59:32',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(29,'<EMAIL>','$2y$10$4fQIYhfGaPbqg/4pIuWKt./TvTDLKrSzhWA3ElbvBns2ztMadFaki','developer','Martin','Fryšták','','','','',1,'','','',NULL,'2022-04-13 15:36:20',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(30,'<EMAIL>','$2y$10$0M9SScI8pNnV3I3WJIk1B.vZz/mtTZQkXNnPFu9OqMzBw9OGk5MLy','user','MartinFryšták','Test','','','','',1,'','','',NULL,'2022-04-14 13:07:00',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(31,'<EMAIL>','$2y$10$LZh4Z4E4ZMnrclB2gAL3qupAI4D1ujev2Cyinefzf343FJTWNRV26','admin','Test','User','','','','',1,'','','',NULL,'2024-03-18 10:18:24',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(32,'<EMAIL>','$2y$10$unREJLZVvjmmwuLjhYSRUucnZxJS/QPjNh08uGMiyMQsSnYwQ7whm','developer','Martina','Paclíková','','','','',1,'','','',NULL,NULL,NULL,'2023-03-01 09:40:34',NULL,NULL,0,1,NULL,NULL),(33,'<EMAIL>','$2y$10$fliC2SobBP53YHHgol62Vex0fUCqjEduU2J4fsUxS5mT5s7BanAaa','developer','Jakub','Mikita','','','','',1,'','','',NULL,NULL,NULL,'2024-01-22 16:40:34',NULL,NULL,0,1,NULL,NULL),(34,'<EMAIL>','$2y$10$SLaMST7q7Sa7NVOKUgAZluwY26N6D939XqnCjso9iKuq/.J2AAwlC','user','','','','','','',NULL,'','','',NULL,'2024-03-19 09:24:57',NULL,NULL,NULL,NULL,0,1,'{}',NULL),(35,'<EMAIL>','$2y$10$TTAttIz/t.HZac7QdrXwQ.1BsWZF/Og97lhxDW1IsrPKGVOG5c0oe','developer','Michal','Kurečka','','','','',61,'','','',NULL,'2024-05-09 09:18:22',NULL,'2024-05-09 09:18:43',NULL,NULL,0,1,'{}',NULL),(37,'<EMAIL>','$2y$10$.Rxf.tYd/6hcUPniPCSI8.L74y8Y0Inbt3o1DvCPy1IsTKwAWc6m.','developer','Karel','Bednář','','','','',61,'','','',NULL,'2024-06-24 15:22:19',NULL,'2024-06-25 12:56:30',NULL,NULL,0,1,'{}',NULL),(38,'<EMAIL>','$2y$10$unREJLZVvjmmwuLjhYSRUucnZxJS/QPjNh08uGMiyMQsSnYwQ7whm','developer','David','Kadlec','','','','',1,'','','',NULL,NULL,NULL,'2024-07-17 09:40:34',NULL,NULL,0,1,NULL,NULL),(42,'<EMAIL>','$2y$10$YgCMFqgKGXOeMKzHDUUBr.w9zd3TqMEcbyDHBCDNwLkkKwqQG84Zu','developer','Miroslav ','Hofbauer','','','','',61,'','','',NULL,'2024-09-05 12:14:52',NULL,'2024-09-05 12:18:26',NULL,NULL,0,1,'{}',NULL),(43,'<EMAIL>','$2y$10$czzOndOQ23LWaGHxuDMBkOBK4yeBI9EMuqv..OXCaSoUFCEG0JtTy','developer','Michal','Loukota','','','','',61,'','','',NULL,'2024-09-05 12:14:52',NULL,'2025-03-05 02:53:24',NULL,NULL,0,1,'{}',NULL),(44,'<EMAIL>','$2y$10$E5D0ajGkgkefcXBcqiS4O.aPfVdES4FuqJ1Thsy00lY9Vf6OYxNYC','developer','Ludek','Chaloupka','*********','Test','Test','12312',NULL,'','','',NULL,'2017-12-06 12:19:09',NULL,'2025-03-10 08:44:55','2024-05-07 10:47:12',NULL,26,1,NULL,NULL),(45,'<EMAIL>','$2y$10$4T5bKJarVfez04Um4oh3o.K57M1Us51J.WsgZEH2q0PvUfjK6PnRu','developer','Vojta','Matějíček','','','','',61,'','','',NULL,'2025-04-28 12:44:46',NULL,'2025-04-28 12:45:22',NULL,NULL,0,1,'{}',NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_hash`
--

DROP TABLE IF EXISTS `user_hash`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_hash` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `createdTime` datetime NOT NULL,
  `validTo` datetime NOT NULL,
  `valid` tinyint(1) NOT NULL DEFAULT '1',
  `usedTime` datetime DEFAULT NULL,
  `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  CONSTRAINT `user_hash_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf16 COLLATE=utf16_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_hash`
--

LOCK TABLES `user_hash` WRITE;
/*!40000 ALTER TABLE `user_hash` DISABLE KEYS */;
INSERT INTO `user_hash` VALUES (1,12,'lostPassword','7277fd8ae8b52ddffbe8c01a433ffb67c7b479dd','2020-12-07 14:26:59','2020-12-08 14:26:59',1,NULL,NULL),(7,5,'lostPassword','3d88e133d5f00f94fbb94ab15e4c3bea4e5a5505','2021-06-23 12:16:41','2021-06-24 12:16:41',1,NULL,NULL),(8,5,'lostPassword','f8682eb898d701933c7053c6d7d7b86bb5d5b7c3','2021-06-23 12:16:42','2021-06-24 12:16:42',1,NULL,NULL),(9,5,'lostPassword','042b063bd61844ecd4b1f2e62d8bb04fb1911c34','2021-06-23 12:17:06','2021-06-24 12:17:06',1,NULL,NULL),(17,4,'lostPassword','36b2b40c6eacb4b6fd1aed1b54a39d10129505a7','2021-07-02 17:38:05','2021-07-03 17:38:05',1,NULL,NULL),(19,11,'lostPassword','0ea11d8744f0c882df63cc9561981d7ee34055ca','2024-04-10 09:26:13','2024-04-11 09:26:13',1,NULL,'{}'),(21,4,'lostPassword','110c73890b0c3c6805dcfab0765f417192f1963a','2024-10-07 15:04:11','2024-10-08 15:04:11',1,NULL,'{}'),(22,4,'lostPassword','bcaae42eb90230ff2437a3478080ddbc16164fa9','2024-10-07 15:08:22','2024-10-08 15:08:22',1,NULL,'{}'),(23,9,'lostPassword','35e5d247f50fc6fc2ee729a1187b59536930244a','2025-05-13 14:53:31','2025-05-14 14:53:31',1,NULL,'{}'),(24,4,'lostPassword','dab89f9f0be0dc3784d6ce06a71fd778e51f3bb3','2025-05-13 15:00:18','2025-05-14 15:00:18',1,NULL,'{}');
/*!40000 ALTER TABLE `user_hash` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_mutation`
--

DROP TABLE IF EXISTS `user_mutation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_mutation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `mutationId` int(11) NOT NULL,
  `newsletter` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `user_mutation_ibfk_3` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_mutation_ibfk_4` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8 COLLATE=utf8_czech_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_mutation`
--

LOCK TABLES `user_mutation` WRITE;
/*!40000 ALTER TABLE `user_mutation` DISABLE KEYS */;
INSERT INTO `user_mutation` VALUES (1,1,1,0),(24,2,1,0),(25,3,1,0),(26,4,1,0),(27,5,1,0),(28,6,1,0),(29,8,1,0),(30,9,1,0),(32,11,1,0),(33,12,1,0),(34,13,1,0),(38,17,1,0),(49,28,1,0),(50,29,1,0),(51,30,1,0),(52,31,1,0),(53,32,1,0),(54,33,1,0),(55,34,1,0),(56,35,1,0),(58,37,1,0),(59,38,1,0),(63,42,1,0),(64,43,1,0),(65,44,1,0),(66,45,1,0);
/*!40000 ALTER TABLE `user_mutation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `voucher`
--

DROP TABLE IF EXISTS `voucher`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `voucher` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `internalName` varchar(255) NOT NULL,
  `type` varchar(20) NOT NULL,
  `minPriceOrder` decimal(18,4) DEFAULT NULL,
  `reuse` int(11) NOT NULL DEFAULT '0',
  `combination` int(11) NOT NULL DEFAULT '0',
  `combinationType` varchar(20) DEFAULT NULL,
  `public` int(11) NOT NULL DEFAULT '0',
  `publicTo` datetime DEFAULT NULL,
  `publicFrom` datetime DEFAULT NULL,
  `created` int(11) DEFAULT NULL,
  `createdTime` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `discount_amount` decimal(18,4) DEFAULT NULL,
  `discount_currency` char(3) DEFAULT NULL,
  `discountPercent` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mutationId` (`mutationId`),
  CONSTRAINT `voucher_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `voucher`
--

LOCK TABLES `voucher` WRITE;
/*!40000 ALTER TABLE `voucher` DISABLE KEYS */;
INSERT INTO `voucher` VALUES (1,1,'aaa','aaa','amount',NULL,0,0,NULL,0,'2124-05-28 08:38:00','2024-05-28 08:38:00',4,'2024-05-28 08:38:41',4,'2024-05-28 08:39:57',0.0000,'CZK',NULL);
/*!40000 ALTER TABLE `voucher` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `voucher_code`
--

DROP TABLE IF EXISTS `voucher_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `voucher_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isUsed` int(11) NOT NULL DEFAULT '0',
  `usedAt` datetime DEFAULT NULL,
  `createdTime` datetime NOT NULL,
  `voucher` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `voucher` (`voucher`),
  CONSTRAINT `voucher_code_ibfk_1` FOREIGN KEY (`voucher`) REFERENCES `voucher` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `voucher_code`
--

LOCK TABLES `voucher_code` WRITE;
/*!40000 ALTER TABLE `voucher_code` DISABLE KEYS */;
INSERT INTO `voucher_code` VALUES (1,'TH3XSN',0,NULL,'2024-05-28 08:39:30',1),(2,'J8RD6Q',0,NULL,'2024-05-28 08:39:30',1),(3,'991AZ7',0,NULL,'2024-05-28 08:39:30',1),(4,'EXJ1K3',0,NULL,'2024-05-28 08:39:30',1),(5,'QTKZDT',0,NULL,'2024-05-28 08:39:30',1),(6,'P4DXEF',0,NULL,'2024-05-28 08:39:30',1),(7,'H27SFN',0,NULL,'2024-05-28 08:39:30',1),(8,'KX2CUT',0,NULL,'2024-05-28 08:39:30',1),(9,'DPKJ97',0,NULL,'2024-05-28 08:39:30',1),(10,'NZA5GU',0,NULL,'2024-05-28 08:39:30',1),(11,'TEST1',0,NULL,'2024-05-28 08:39:57',1);
/*!40000 ALTER TABLE `voucher_code` ENABLE KEYS */;
UNLOCK TABLES;
/*!50112 SET @disable_bulk_load = IF (@is_rocksdb_supported, 'SET SESSION rocksdb_bulk_load = @old_rocksdb_bulk_load', 'SET @dummy_rocksdb_bulk_load = 0') */;
/*!50112 PREPARE s FROM @disable_bulk_load */;
/*!50112 EXECUTE s */;
/*!50112 DEALLOCATE PREPARE s */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-13 15:06:20
