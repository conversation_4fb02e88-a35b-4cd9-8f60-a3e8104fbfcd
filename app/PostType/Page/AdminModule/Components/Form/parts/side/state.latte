{embed $templates.'/part/box/std.latte', props=>[
title=> 'Stav',
]}
	{block content}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['publish']['public'],
				label: '<span class="grid-inline"><span class="tag">'.strtoupper($entityLocalization->mutation->langCode).'</span> <strong>Publikováno</strong></span>',
				classes: ['u-mb-sm']
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['forceNoIndex'],
				classes: ['u-mb-sm']
			]
		}
		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $form['hideInSearch'],
				classes: ['u-mb-sm']
			]
		}


	{/block}
{/embed}
