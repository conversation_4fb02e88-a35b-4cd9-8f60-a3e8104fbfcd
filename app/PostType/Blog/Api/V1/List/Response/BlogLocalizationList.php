<?php declare(strict_types = 1);

namespace App\PostType\Blog\Api\V1\List\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use Nextras\Orm\Collection\ICollection;

class BlogLocalizationList extends BasicEntity
{

	/** @var BlogLocalizationListItem[] */
	public readonly array $blogs;

	public function __construct(
		ICollection $items
	)
	{
		$blogs = [];
		foreach ($items as $item) {
			assert($item instanceof BlogLocalization);
			$blogs[] = new BlogLocalizationListItem($item);
		}

		$this->blogs = $blogs;
	}

}
