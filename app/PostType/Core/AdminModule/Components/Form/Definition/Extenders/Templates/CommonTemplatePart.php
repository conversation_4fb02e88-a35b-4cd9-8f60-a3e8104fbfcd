<?php declare(strict_types=1);

namespace App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates;

use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;

final class CommonTemplatePart implements TemplatePart
{
	public const TYPE_MAIN = 'main';
	public const TYPE_SIDE = 'side';

	/**
	 * @param self::TYPE_* $type
	 */
	public function __construct(
		private readonly string $templateFile,
		private readonly string $type = self::TYPE_MAIN,
		private readonly ?array $parameters = [],
	)
	{}

	public function getType(): string
	{
		return $this->type;
	}

	public function getTemplatePath(): string
	{
		return $this->templateFile;
	}

	public function getParameters(): array
	{
		return $this->parameters;
	}
}
