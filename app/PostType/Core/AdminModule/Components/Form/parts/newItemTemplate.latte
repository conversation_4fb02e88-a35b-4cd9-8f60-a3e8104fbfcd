<div class="u-hide">
	{varType App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition $formDefinition}
	{foreach $formDefinition->extenders as $extender}

		{foreach $extender->getTemplateParts([App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart::TYPE_RELATION_PRESCRIPTION]) as $templatePart}

			{if $templatePart instanceof App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart}
				{varType App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart $templatePart}

				{var $relationInfo = $templatePart->getRelationInfo()}
				{if ! $relationInfo->singleValue}
					{if isset($form[$relationInfo->propertyName])}
						{include $templates.'/part/core/formTargetItem.latte', props: [
							itemTargetName: $relationInfo->propertyName . 'ListItem',
							formContainer: $form[$relationInfo->propertyName]['newItemMarker'],
							listPlaceholder: $relationInfo->inputPlaceHolder,
							listSearchUrl: $relationInfo->suggestUrl,
							dragdrop: $relationInfo->dragAndDrop,
						]}
					{/if}
				{/if}
			{/if}
		{/foreach}
	{/foreach}
</div>
