{var $props = [
	title: $props['title'] ?? '',
	variant: (isset($props['variant']) && in_array($props['variant'], ['main', 'default'])) ? $props['variant'] : 'default',
	id: $props['id'] ?? '',
	icon: $props['icon'] ?? '',
	tags: $props['tags'] ?? [],
	open: $props['open'] ?? false,
	rowMain: $props['rowMain'] ?? true,
	classes: $props['classes'] ?? ['u-mb-md'],
]}

{var $baseClasses = ['b-toggle']}
{if $props['variant']}
	{php $baseClasses[] = 'b-toggle--' . $props['variant']}
{/if}
{if $props['open']}
	{php $baseClasses[] = 'is-open'}
{/if}
{var $classes = implode(' ', array_merge($baseClasses, $props['classes']))}

<div
	id="{$props['id']}"
	class="{$classes}"
	data-controller="Toggle"
	data-toggle-target-value="#{$props['id']}"
	data-toggle-target-class-value="is-open"
	data-toggle-name-value="{$props['id']}"
	data-action="ToggleAll:toggle@window->Toggle#toggle"
	{if $props['variant'] == 'main'}
		data-pagemenu-target="item"
		data-title="{$props['title']}"
		data-icon-url="{$props['icon']}"
	{/if}
>
	<div class="b-toggle__header">
		<button class="b-toggle__tool btn-icon" data-action="Toggle#changeClass" type="button">
			{include $templates.'/part/icons/chevron-right.svg'}
		</button>
		<div class="grid-inline">
			<strong class="flex-grow">
				{if $props['icon']}
					<span class="item-icon">
						<span class="item-icon__icon icon">
							{include $props['icon']}
						</span>
						<span class="item-icon__text">
							{$props['title']|noescape}
						</span>
					</span>
				{else}
					{$props['title']|noescape}
				{/if}
			</strong>
			{if $props['tags']}
				<span class="grid-inline">
					{foreach $props['tags'] as $tag}
						<span class="tag">
							{$tag['text']}
						</span>
					{/foreach}
				</span>
			{/if}
		</div>
	</div>
	<div class="b-toggle__content">
		{if $props['variant'] == 'main' && $props['rowMain']} <div class="row-main"> {/if}
			{block content}{/block}
		{if $props['variant'] == 'main' && $props['rowMain']} </div> {/if}
	</div>
</div>



















