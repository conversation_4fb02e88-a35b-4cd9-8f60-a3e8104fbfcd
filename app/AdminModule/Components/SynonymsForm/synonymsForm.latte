<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>

{form form, novalidate => 'novalidate'}
	{if $statusInfo == 'waiting'}
		<p class="message message-info">
			{_synonyms_info}
		</p>
	{/if}
	<div class="box-param-type" data-type='["select", "multiselect"]'>
		<div class="crossroad-attached">
			<div class="holder">
				<div class="hd">
					<div class="grid-row">
						<p class="grid-1-5">
							<strong>{_Word}</strong>
						</p>
						<p class="grid-3-5">
							<strong>{_Synonyms}</strong>
						</p>
						<p class="grid-1-5">
						</p>
					</div>
				</div>
				<div class="bd">
					<ul class="sortable reset" data-copy="values" data-pattern='{include "part/form/values.latte"}'>
						{if $items}
							{foreach $items as $word => $synonyms}
								{include 'part/form/values.latte', 'k' => $iterator, 'word' => $word, 'synonyms' => implode(App\Model\Orm\Mutation\Mutation::SYNONYMS_DELIMITER, (array)$synonyms)}
							{/foreach}
						{/if}
					</ul>
				</div>
			</div>
			<div class="ft">
				<p>
					<a href="#" class="btn btn-icon-before" data-copy="values">
						<span><span class="icon icon-plus"></span> {_add_value_button}</span>
					</a>
				</p>
			</div>
		</div>
	</div>


	<div class="fixed-bar">
		<button class="btn btn-green btn-icon-before">
			<span><span class="icon icon-checkmark"></span> {_save_button}</span>
		</button>
	</div>


{/form}
