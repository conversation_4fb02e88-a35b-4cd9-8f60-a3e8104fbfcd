<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\ShellForm;


use App\AdminModule\Presenters\PaymentMethod\Components\ShellForm\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\PaymentMethod\PaymentMethod;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PaymentMethod\PaymentMethodConfigurationRepository;
use App\Model\Orm\PaymentMethod\PaymentMethodRegistry;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\DI\Container;
use Nette\Utils\Random;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
class ShellForm extends Control
{

	/**
	 * @var ICollection<Mutation>
	 */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly PaymentMethodConfigurationRepository $paymentMethodConfigurationRepository,
		private readonly PaymentMethodRegistry $paymentMethodRegistry,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}

	private function getPaymentIdentifiers(): array
	{
		$payments = [];
		foreach ($this->paymentMethodRegistry->list() as $paymentMethod) {
			$payments[$paymentMethod->getUniqueIdentifier()] = $paymentMethod->getUniqueIdentifier();
		}

		return $payments;
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSelect('paymentIdentificator', 'select_payment_identificator', $this->getPaymentIdentifiers())->setRequired();
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		$form->onValidate[] = [$this, 'formValidate'];

		return $form;
	}


	public function formValidate(Form $form, BaseFormData $data): void
	{
		$exists = $this->paymentMethodConfigurationRepository->findBy(['mutation' => $data->mutation, 'paymentMethodUniqueIdentifier' => $data->paymentIdentificator])->countStored();

		if ($exists > 0) {
			$form->addError('payment_already_exists_in_mutation');
		}
	}

	public function formError(Form $form): void
	{
		$this->presenter->flashMessage('Error: '.implode(', ', $form->getErrors()), 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$paymentMethod = new PaymentMethodConfiguration();
		$paymentMethod->mutation = $this->mutations->getById($data->mutation);
		$paymentMethod->paymentMethodUniqueIdentifier = $data->paymentIdentificator;

		$this->paymentMethodConfigurationRepository->persistAndFlush($paymentMethod);

		$this->presenter->redirect('edit', ['id' => $paymentMethod->id]);
	}

}
