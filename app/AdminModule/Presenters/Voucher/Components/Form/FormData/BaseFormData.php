<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Voucher\Components\Form\FormData;

use App\Model\Orm\Price;
use App\PostType\Core\AdminModule\Components\Form\FormData\FormDataHelper;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class BaseFormData
{


	public Price $discount;
	public ?DateTimeImmutable $publicFrom;
	public ?DateTimeImmutable $publicTo;

	public function __construct(
		public string $type,
		public string $name,
		public string $internalName,
		public ?float $minPriceOrder,
		public bool $reuse,
		?string $publicFrom,
		?string $publicTo,
		public PublishFormData $publish,
		public ?int $discountPercent,
		public float $discountAmount,
		public ArrayHash $codes,
		public bool $combination,
		public ?string $combinationType,

	)
	{
		$this->publicFrom =	FormDataHelper::convertInvalidDateTimeValue($publicFrom);
		$this->publicTo = FormDataHelper::convertInvalidDateTimeValue($publicTo);
		$this->discount = Price::from(Money::of($this->discountAmount ?? 0, 'CZK'));
	}
}
