extensions:
    console: Contributte\Console\DI\ConsoleExtension(%consoleMode%)
    console.cache: Contributte\Console\Extra\DI\AdvancedCacheConsoleExtension(%consoleMode%)

console:
    name: Superadmin
    version: '1.0'

console.cache:
	generators:
		latte: Contributte\Console\Extra\Cache\Generators\LatteTemplatesCacheGenerator(
			dirs: [%appDir%],
			rootDir: ::realpath(%appDir%/..)
		)

		di: Contributte\Console\Extra\Cache\Generators\DiContainersCacheGenerator(
			[production: [debugMode: false, consoleMode: false]],
			@configurator
		)

	cleaners:
		cache: App\Infrastructure\Cache\RedisCacheCleaner(
			commonPrefix: "%config.projectName%:%stageName%:"
			currentPrefix: %redis.storage.prefix%
		)

services:
	configurator:
		type: Nette\Bootstrap\Configurator
		imported: true
		autowired: false

	- Apitte\Console\Command\RouteDumpCommand

search:
	commands:
		in: %appDir%/Console
		extends: Symfony\Component\Console\Command\Command
