includes:
	- environment.dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://kohinoor.superkoders.test/
		mutations:
			cs:
				domain: kohinoor.superkoders.test
			en:
				domain: kohinoor.superkoders.test

	database:
		host: kohinoor_db
		database: kohinoor
		user: kohinoor
		password: kohinoor

	redis:
		host: kohinoor_redis

elastica:
	config:
		host: kohinoor_es

http:
	proxy:
		- *********/8

mail:
	host: kohinoor_mailcatcher
	port: 1025
