parameters:
	stageName: 'production'

	config:
		isDev: false
		env: production

		mutations:
			cs:
				domain: superadmin.www6.superkoderi.cz
				urlPrefix: false
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				mutationId: 1
				googleAnalyticsCode: 'UA-222222-1'
				robots: "index, follow"
				sitemap: true


		domainUrl: "https://superadmin.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena
		domainUrlPdf: "https://superadmin.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena

		translations:
			insertNew: false
			markUsage: false                                  #kdyz neni zalozeny prekad v DB -> vytovri se novy

includes:
    - header.php

services:
	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")



