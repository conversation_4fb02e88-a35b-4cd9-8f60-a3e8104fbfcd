extensions:
	cf: App\Model\CustomField\CustomFieldsExtension

cf:
	definitions:
		content:
			type: tinymce
			label: "Obsah"
		annotation:
			type: textarea
			label: "Anotace"
		text:
			type: text
			label: "Text"
		photo:
			type: image
			label: "Fotka"
		photos:
			type: image
			label: "Galerie"
			multiple: true
		file:
			type: file
			label: "Soubor"
		files:
			type: file
			label: "Soubory"
			multiple: true
		dateTime:
			type: dateTime
			label: "Datum a čas"
		linkChoose:
			hasContentToggle: true
			type: group
			label: "Odkaz"
			items:
				toggle:
					type: radio
					inline: true
					isContentToggle: true
					defaultValue: 'systemHref'
					options: [
						{ label: "<PERSON>ystémov<PERSON> stránka", value: "systemHref" },
						{ label: "Vlastní odkaz", value: "customHref" },
					]
				systemHref:
					type: group
					items:
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
						hrefName:
							type: text
							label: "Text odkazu (volitelné)"
				customHref:
					type: group
					items:
						href:
							type: text
							label: "Odkaz"
						hrefName:
							type: text
							label: "Text odkazu"
		video:
			type: group
			label: "Video"
			items:
				poster:
					type: image
					label: "Zástupný obrázek (min. 1200x675)"
				autoplay:
					type: checkbox
					label: "Automatické přehrávání"
				video:
					type: group
					label: "Video"
					hasContentToggle: true
					items:
						toggle:
							type: radio
							inline: true
							isContentToggle: true
							defaultValue: 'systemHref'
							options: [
								{ label: "Youtube / Vimeo", value: "link" },
								{ label: "Soubor", value: "file" },
							]
						link:
							type: group
							label: "Odkaz"
							items:
								link:
									type: text
						file:
							type: group
							label: "Soubor"
							items:
								file:
									type: file

	fields:
		mutationData:
			type: group
			label: "Nastavení mutace"
			items:
				icon:
					type: text
					label: "jméno svg ikony v přepínači jazyků"
					# defaultValue: "John Doe"
				ogImage:
					type: image
					label: "og:image - obrázkový náhled webu (1200x630)"
		base:
			type: group
			items:
				mainImage:
					type: image
					label: "Hlavní obrázek (min. DOPLNIT ROZMĚR)"

		parameterForFilter:
			type: group
			label: "Nastavení filtru"
			items:
				visibleParameters:
					type: list
					label: Parametry
					items:
						indexable:
							type: checkbox
							label: "Indexovatelné"
						visibleCount:
							type: text
							label: "Počet viditelných hodnot"
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForFilter


						numberAsRange:
							type: checkbox
							label: "Rozsah pro číselné hodnoty"

		feeds:
			type: group
			label: "Nastavení kategorií feedů"
			items:
				zbozi:
					type: text
					label: "Zboží"
				heureka:
					type: text
					label: "Heureka"
				google:
					type: text
					label: "Google"

		userMenuLoggedUser:
			label: "User menu pro přihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		userMenuUnloggedUser:
			label: "User menu pro nepřihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		deliveryPayment:
			label: "Nastavení dopravy a platby"
			type: group
			items:
				icon:
					type: image
					multiple: true
					label: "SVG ikona / obrázek (min. 100x100)"

		paymentAccount:
			label: 'Nastavení bankovního spojení'
			type: group
			items:
				bban:
					type: text
					label: BBAN
				iban:
					type: text
					label: IBAN
				swift:
					type: text
					label: SWIFT / BIC

		paymentText:
			label: 'Nastavení thank you page'
			type: group
			items:
				Physical:
					type: group
					label: 'Doručení na adresu'
					items:
						text:
							type: tinymce
							label: 'Text'
						note:
							type: textarea
							label: 'Poznámka'

				Pickup:
					type: group
					label: 'Odberné místo'
					items:
						text:
							type: tinymce
							label: 'Text'
						note:
							type: textarea
							label: 'Poznámka'

		userSideMenu:
			label: "User side menu (uživatelská sekce)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		cookies:
			label: "Cookies"
			type: group
			items:
				title:
					label: "Nadpis"
					type: text
					value: ""
				text:
					label: "Text"
					type: tinymce
					value: ""
				btnSetPreferences:
					label: "Tlačítko - nastavit preference"
					type: text
					value: ""
				btnReject:
					label: "Tlačítko - Odmítnout"
					type: text
					value: ""
				btnConsentAndContinuation:
					label: "Tlačítko - souhlas a pokračování"
					type: text
					value: ""
				consentsTitle:
					label: "Nadpis - nastavení preferencí"
					type: text
					value: ""
				necessarilyLink:
					label: "Nezbytné - link"
					type: text
					value: ""
				necessarilyText:
					label: "Nezbytné - text"
					type: tinymce
					value: ""
				preferenceslLink:
					label: "Předvolby - link"
					type: text
					value: ""
				preferencesText:
					label: "Předvolby - text"
					type: tinymce
					value: ""
				analyticsLink:
					label: "Analytika - link"
					type: text
					value: ""
				analyticsText:
					label: "Analytika - text"
					type: tinymce
					value: ""
				marketingLink:
					label: "Marketingové - link"
					type: text
					value: ""
				marketingText:
					label: "Marketingové - text"
					type: tinymce
					value: ""
				btnConfirmSelected:
					label: "Tlačítko - potvrdit vybrané"
					type: text
					value: ""
				btnAcceptEverything:
					label: "Tlačítko - přijmout vše"
					type: text
					value: ""

		footerMenu:
			type: group
			label: "Footer menu"
			items:
				footer_menu_1:
					type: group
					label: "1. sloupec"
					order: 1
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"
				footer_menu_2:
					type: group
					label: "2. sloupec"
					order: 2
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"

				footer_menu_3:
					type: group
					label: "3. sloupec"
					order: 3
					items:
						title:
							type: text
							label: "Nadpis"
						list:
							extends: @cf.definitions.linkChoose
							type: list
							label: "Položky menu"

	suggestUrls:
		searchMutationPage:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"
			params: []
		searchBlogTag:
			searchParameterName: search
			link: "/superadmin/search2/blog-tag"
			params: []
		searchPage:
			searchParameterName: search
			link: "/superadmin/search2/page"
			params: []
		searchProduct:
			searchParameterName: search
			link: "/superadmin/search2/product"
			params: []
		searchParameter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: []
		searchParameterForFilter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: [ 'types': ['select', 'multiselect', 'number'], 'onlyForFilter': 1]
		searchSeoLinkParameterValues:
			searchParameterName: search
			link: "/superadmin/search2/seolink-parameter-values"
			params: []
		searchAuthors: <AUTHORS>
			link: "/superadmin/search2/author"
			params: []
		searchMutationBlogs:
			searchParameterName: search
			link: "/superadmin/search2/blog-in-mutation"
			params: []
		searchMutationAlias:
			searchParameterName: search
			link: "/superadmin/search2/alias"
			params: []
		searchState:
			searchParameterName: search
			link: "/superadmin/search2/state"
			params: []
		searchPaymentMethod:
			searchParameterName: search
			link: "/superadmin/search2/payment-method"
			params: []

	templates:
		#template: [customfields]

		# examples - todo to WIKI:
		# WIKI - https://www.notion.so/superkoders/Vlastn-pole-Custom-Fields-verze-1-0-2c3322c358224c769c0bdb1a9593b6d2
		#1) :Front:Page:default: [customfields] # CF pro stránku s šablonou Page:default
		#2) Product:detail: [customfields] # CF pro produkt
		#3) product-AAA: [customfields] # CF pro produkt jehož nejvyšším rodičem (hlavní katgorie) je UID produktové kategorie AAA
		#4) uid-XXX: [customfields] # CF pro stránku s uid = XXX
		#5) parameter-YYY: [customfields] #CF pro parametr s uid = YYY
		#6) parameters: [customfields] #CF pro všechny parametry obecně, parameter-YYY přebíjí
		#7) banner-ZZZ: [customfields] #CF pro banner s pozici = ZZZ
		#8) CF pro všechny stránky ve stromě, nasatvuje se do speciální sekce: customFieldsTemplates
		#9) user-ROLE || users


		mutation: [@cf.mutationData, @cf.footerMenu]
		uid-userSection: [@cf.userMenuUnloggedUser, @cf.userMenuLoggedUser, @cf.userSideMenu]
		uid-cookie: [@cf.cookies]
		uid-deliveryMethod: [@cf.deliveryPayment]
		uid-paymentMethod: [@cf.deliveryPayment, @cf.paymentAccount, @cf.paymentText]
		:Front:Catalog:default: [@cf.parameterForFilter]
		:Front:Product:detail: [@cf.base]
		:Front:Page:default: [@cf.base]
