<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Menu;

use App\Model\ConfigService;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\LocalizationEntity;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageFactory;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguage;

/**
 * @property-read DefaultTemplate $template
 */
final class Menu extends UI\Control
{

	private readonly Mutation $mutation;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly MenuService $menuService,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly ToggleLanguageFactory $toggleLanguageFactory,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}


	private function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->pages = $this->mutation->pages;
		$this->template->object = $this->object;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}


	public function createComponentToggleLanguage(): ToggleLanguage
	{
		if ($this->object instanceof LocalizationEntity) {
			return $this->toggleLanguageFactory->create($this->object);
		} else {
			return $this->toggleLanguageFactory->create($this->mutation->pages->title);
		}
	}


	public function render(): void
	{
		$this->initTemplate();
		$markSelected = true;
		$this->template->menu = $this->menuService->getMenu($this->mutation, $this->mutation->rootId, $this->object, $markSelected, $this->configService->getParam('mainMenu'));
		$this->template->mutation = $this->mutation;
		$this->template->render(__DIR__ . '/mainMenu.latte');
	}

}
