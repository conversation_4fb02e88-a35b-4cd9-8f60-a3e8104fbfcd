{default $snippetSuffix = ""}

{snippet form}
	{php $control['form']->action .= "#frm-contactForm-form"}
	{form form class: 'f-contact block-loader', data-naja: '', novalidate: "novalidate"}
		<h2 class="f-contact__title h3">
			<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se k <PERSON>,<br>
			ať vám nic neute<PERSON>e
		</h2>

		{control messageForForm, $flashes, $form}

		{include '../inp.latte', form: $form, name: name, validate: true, error: true}
		{include '../inp.latte', form: $form, name: surname, labelLang: 'form_label_surname', validate: true, error: true}
		{include '../inp.latte', form: $form, name: email, labelLang: 'form_label_email', validate: true, error: true}
		{include '../inp.latte', form: $form, name: phone, type: 'tel', labelLang: 'form_label_phone', validate: true, error: true}

		<p class="f-contact__privacy">
			Odesláním formuláře udělujete souh<PERSON> s <a href="https://psn.cz/content/gdprdetail" target="_blank" aria-describedby="new-tab-desc">podmínkami zpracování osobních údajů</a>.
		</p>

		<p class="f-contact__btn u-mb-0">
			<button type="submit" class="btn">
				<span class="btn__text">
					{_"btn_send"}
				</span>
			</button>
		</p>

		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

	{/form}

{/snippet}
