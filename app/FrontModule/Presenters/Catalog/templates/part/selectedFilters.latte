{default $class = 'u-mb-sm'}

<div n:if="$filter->nonRoot ?? false" n:class="b-filters, $class">
	<p class="b-filters__title u-mb-0">
		{_"title_selected_filters"}
	</p>
	<ul n:if="isset($filter->boxes)" n:ifcontent class="grid grid--x-xs grid--y-xs">
		{foreach $filter->boxes as $box}
			{varType App\Model\BucketFilter\Box\CheckBoxes $box}
			{if $box->getCheckedItems()}
				{if $box instanceOf App\Model\BucketFilter\Box\Slider}
					{* {include './selectedPart/slider.latte', box=>$box} *}

					{capture $link}{link 'this', filter => $box->filterToDeSelect}{/capture}
					{php $link = urldecode(htmlspecialchars_decode($link))}
					{var $value = $box}

					<li class="grid__cell size--auto">
						<a href="{$link}" class="flag" data-naja data-naja-loader="body">
							{_$box->title}:
							{if $box->inputValueMin != $box->selectedMin && $box->inputValueMax != $box->selectedMax}
								{$box->format($box->selectedMin)}{if $box->unit} {$box->unit}{/if} &ndash; {$box->format($box->selectedMax)}{if $box->unit} {$box->unit}{/if}
							{elseif $box->inputValueMin != $box->selectedMin}
								{_from} {$box->format($box->selectedMin)}{if $box->unit} {$box->unit}{/if}
							{elseif $box->inputValueMax != $box->selectedMax}
								{_to} {$box->format($box->selectedMax)}{if $box->unit} {$box->unit}{/if}
							{/if}
							{('close')|icon, 'flag__icon'}
						</a>
					</li>
				{else}
					{* {include './selectedPart/checkBoxes.latte', box=>$box} *}
					{var $isFlagFilter = isset($filter->boxesByNamespace['flags']) && in_array($box, $filter->boxesByNamespace['flags'])}
						{foreach $box->getCheckedItems() as $value}
							{capture $link}{link 'this', filter => $value->filterToDeSelect}{/capture}
							{php $link = urldecode(htmlspecialchars_decode($link))}
							<li class="grid__cell size--auto">
								<a href="{$link}" class="flag" data-naja data-naja-loader="body">
									{if !$isFlagFilter}{_$box->title}: {/if}{$value->name}
									{if $box->unit}
										{$box->unit}
									{/if}
									{('close')|icon, 'flag__icon'}
								</a>
							</li>
						{/foreach}
				{/if}
			{/if}
		{/foreach}

		<li class="grid__cell size--auto">
			{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}
			<a href="{$link}" class="flag flag--secondary">
				{_btn_filter_remove}
			</a>
		</li>
	</ul>
</div>
