{varType App\Model\BucketFilter\Box\CheckBoxes $box}
<div class="b-filters__group">
	<p class="b-filters__title">
		{$box->title}:
	</p>

	{capture $link}{link 'this', 'filter' => $box->filterToDeSelect, 'pager-page' => null}{/capture}
	{php $link = urldecode(htmlspecialchars_decode($link))}
	<p>
		<a href="{$link}" data-naja data-naja-loader="body">
			{_btn_filter_cancel}
		</a>
	</p>

	<ul class="b-filters__list">
		{foreach $box->getCheckedItems() as $value}
			{capture $link}{link 'this', filter => $value->filterToDeSelect}{/capture}
			{php $link = urldecode(htmlspecialchars_decode($link))}
			<li class="b-filters__item">
				<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow($object, ['filter' => $value->filterToDeSelect])} rel="nofollow"{/if}>
					{$value->name}
					{if $box->unit}
						{$box->unit}
					{/if}
				</a>
			</li>
		{/foreach}
	</ul>
</div>
