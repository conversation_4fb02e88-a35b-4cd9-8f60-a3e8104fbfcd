{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
{var $price = $variant->price($mutation, $priceLevel, $state)}
{var $priceVat = $variant->priceVat($mutation, $priceLevel, $state)}

{*
	Cena produktu se nove urcuje podle mutace a cenove hladiny
	u ceny s DPH je navic potreba predat aktualni stat (urcuje sazbu DPH)

	{dump $mutation} // object \App\Model\Mutation
	{dump $priceLevel} // object \App\Model\PriceLevel
	{dump $state} // object \App\Model\State

	{dump $price} // float cena bez DPH
	{dump $priceVat} // float cena s DPH
*}

<p n:class="price, $priceVat->isZero() ? 'price--soldout'">
	{if $priceVat->isZero()}
		<strong>
			{_price_not_determined}
		</strong>
	{else}
		<strong>
			{$priceVat|money}
		</strong>
		{_price_tax}
	{/if}<br>

	{if $priceVat->isPositive()}
		<small>
			{$price|money} {_price_without_tax}
		</small>
	{/if}
</p>
