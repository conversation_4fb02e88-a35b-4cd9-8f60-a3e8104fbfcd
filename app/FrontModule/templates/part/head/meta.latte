{* Twitter *}
<meta name="twitter:card" content="summary_large_image">
{* OpenGraph *}
{if isset($object->nameTitle)}
	<meta property="og:title" content="{$object->nameTitle}">
{/if}
{if isset($object->description) && $object->description}
	<meta property="og:description" content="{$object->description}">
{elseif isset($object->annotation) && $object->annotation}
	<meta property="og:description" content="{$object->annotation}">
{/if}
{ifset $mutation->cf->mutationData->ogImage}
	{php $img = $mutation->cf->mutationData->ogImage->getSize('ogImage')}
	<meta property="og:image" content="{$mutation->getBaseUrl()}{$img->src}">
{/ifset}
<meta property="og:site_name" content="{_title}">
{if isset($object) && $object instanceOf App\Model\Orm\Routable}
	<meta property="og:url" content="{link $object}">
	{if isset($object) && $object && $object->template == ':Front:Product:detail'}
		<meta property="og:type" content="product">
	{elseif isset($object) && $object && $object->template == ':Blog:Front:Blog:detail'}
		<meta property="og:type" content="article">
	{else}
		<meta property="og:type" content="website">
	{/if}
{else}
	<meta property="og:url" content="{$mutation->getBaseUrl()}">
	<meta property="og:type" content="website">
{/if}

<link rel="stylesheet" href="https://use.typekit.net/ren0wsd.css">

{* favicons *}
<link rel="apple-touch-icon" sizes="180x180" href="{$mutation->getBaseUrl()}/static/img/favicon/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon-16x16.png">
<link rel="manifest" href="{$mutation->getBaseUrl()}/site.webmanifest">
<link rel="mask-icon" href="{$mutation->getBaseUrl()}/static/img/favicon/safari-pinned-tab.svg" color="#5bbad5">
<meta name="msapplication-TileColor" content="#00aba9">
<meta name="theme-color" content="#ffffff">
<link rel="shortcut icon" href="/favicon.ico">
