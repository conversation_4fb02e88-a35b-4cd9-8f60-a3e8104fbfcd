{default $class = 'u-mb-lg'}
{default $name = $seoLink??->name ?? $object->name}
{default $uid = $object->uid ?? false}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $date = false}

{if $uid == 'articlesMain' && isset($tagValue) && $tagValue}
	{php $name = $name . ' - ' . $tagValue->value}
{/if}

<header n:class="b-annot, $class, u-mb-last-0">
	<h1>
		{$name}
	</h1>
	<p n:if="$date" class="b-annot__date">
		{$object->publicFrom|date:"j. n. Y"}
	</p>
	<p n:if="$annotation" class="b-annot__desc">
		{rtrim($annotation ?? '', '<br>')|texy|noescape}
	</p>
</header>
