<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nette\SmartObject;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read array|int[] $itemIds
 * @property ICollection&iterable<Product> $items
 * @property int $count
 * @property int $totalCount
 */
final class Result
{

	use SmartObject;

	/** @var ICollection&iterable<Product> */
	private ICollection $items;

	private int $count;

	private int $totalCount;


	private function __construct()
	{
	}


	public static function empty(): self
	{
		/** @var ICollection&iterable<Product> $emptyCollection */
		$emptyCollection = new EmptyCollection();

		$result = new self();
		$result->items = $emptyCollection;
		$result->count = 0;
		$result->totalCount = 0;
		return $result;
	}


	/**
	 * @param ICollection&iterable<Product> $items
	 */
	public static function from(
		ICollection $items,
		int $count,
		int $totalCount,
	): self
	{
		$result = new self();
		$result->items = $items;
		$result->count = $count;
		$result->totalCount = $totalCount;
		return $result;
	}


	protected function getItemIds(): array
	{
		$ids = [];

		foreach ($this->items as $item) {
			$ids[] = $item->id;
		}

		return $ids;
	}


	protected function getItems(): ICollection
	{
		return $this->items;
	}


	protected function getCount(): int
	{
		return $this->count;
	}


	protected function getTotalCount(): int
	{
		return $this->totalCount;
	}

}
