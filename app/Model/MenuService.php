<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\StaticPage\StaticPage;

final class MenuService
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	public function getMenu(Mutation $mutation, int $parent, Routable|StaticPage $object, bool $markSelected = true, array $items = [], array $hide = []): array
	{
		$menu = [];
		if ($items) {
			if ($object !== NULL) {
				foreach ($items as $uid) {
					$menuItem = new \stdClass();
					$menuItem->page = $page = $this->orm->tree->getByUid($uid, $mutation);
					if (!$page) {
						continue;
					}

					if (isset($object->path) && in_array($page->id, (array)$object->path, true)) {
						$menuItem->active = true;
					} else {
						$menuItem->active = false;
					}

					if ($object instanceof Routable && $markSelected && isset($object->id) && $page->id === $object->id) {
						$menuItem->selected = true;
					} else {
						$menuItem->selected = false;
					}

					if ($menuItem->active || $menuItem->selected) {
						$menuItem->submenu = $this->getMenu($mutation, $page->id, $object, $markSelected);
					}

					$menu[] = $menuItem;
				}
			}

			return $menu;

		} else {
			$where = [
				'parent' => $parent,
				'public' => 1
			];
			if ($hide !== []) {
				$where['uid!='] = $hide;
			}

			$pages = $this->orm->tree->findBy($where)->orderBy('sort');

			$menu = [];
			if ($object !== NULL) {
				foreach ($pages as $page) {
					$menuItem = new \stdClass();
					$menuItem->page = $page;

					if (isset($object->path) && in_array($page->id, (array)$object->path, true)) {
						$menuItem->active = true;
					} else {
						$menuItem->active = false;
					}

					if ($object instanceof Routable && $markSelected && isset($object->id) && $page->id === $object->id) {
						$menuItem->selected = true;
					} else {
						$menuItem->selected = false;
					}

					if ($menuItem->active || $menuItem->selected) {
						$menuItem->submenu = $this->getMenu($mutation, $page->id, $object, $markSelected);
					}
					$menu[] = $menuItem;
				}
			}
			return $menu;
		}

	}

	public function getUserMenu(Routable|StaticPage $object, bool $markSelected = true, array $items = []): array
	{
		$menu = [];

		if ($items === []) {
			return $menu;
		}

		foreach ($items as $item) {
			$menuItem = new \stdClass();
			$menuItem->page = $item;

			if (in_array($item->id, (array)$object->getPath(), true)) {
				$menuItem->active = true;
			} else {
				$menuItem->active = false;
			}

			if ($markSelected && $item->id == $object->getId()) {
				$menuItem->selected = true;
			} else {
				$menuItem->selected = false;
			}

			$menu[] = $menuItem;
		}
		return $menu;
	}



}
