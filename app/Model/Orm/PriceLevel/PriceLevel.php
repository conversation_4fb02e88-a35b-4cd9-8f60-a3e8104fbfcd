<?php

declare(strict_types=1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\User\User;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string $name
 *
 * RELATIONS
 * @property ProductVariantPrice[]|OneHasMany $prices {1:m ProductVariantPrice::$priceLevel, cascade=[persist, remove]}
 * @property User[]|OneHasMany $users {1:m User::$priceLevel, cascade=[persist, remove]}
 */
class PriceLevel extends Entity
{

	public const DEFAULT_ID = 1;

	public const TYPE_DEFAULT = 'default';
	public const TYPE_WHOLESALE = 'wholesale';

}
