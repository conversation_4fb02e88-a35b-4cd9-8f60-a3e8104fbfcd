<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ProductLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'product_localization';

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('pv.id')
			->from($this->tableName, 'pv')
			->andWhere('pv.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

}
