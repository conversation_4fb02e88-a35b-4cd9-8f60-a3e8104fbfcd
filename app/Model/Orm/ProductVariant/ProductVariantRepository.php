<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductVariant getById($id)
 * @method ICollection|ProductVariant[] findFilteredVariants($variantsIds)
 * @method ICollection|ProductVariant[] findBestseller($category)
 * @method ProductVariant save(?ProductVariant $entity, array $data)
 */
final class ProductVariantRepository extends Repository
{

	use HasSimpleSave;

	public function __construct(
		IMapper $mapper,
		?IDependencyProvider $dependencyProvider,
		private readonly ProductRepository $productRepository,
	)
	{
		parent::__construct($mapper, $dependencyProvider);
	}

	public static function getEntityClassNames(): array
	{
		return [ProductVariant::class];
	}

	/**
	 * @param string|int $id
	 * @return ProductVariant|null
	 */
	public function getActiveVariant($id): ?ProductVariant
	{
		$onlyPublicProduct = $this->productRepository->getPublicOnly();
		$orm = $this->getModel();
		assert($orm instanceof Orm);

		$mutation = $orm->getMutation();

		if ($onlyPublicProduct) {
			$variant = $this->findBy([
				'product->productLocalizations->public' => 1,
				'product->productLocalizations->mutation' => $mutation,

			])->getBy([
				'id' => $id,
				'variantLocalizations->active' => 1,
				'variantLocalizations->mutation' => $mutation,
			]);
		} else {
			$variant = $this->getBy(['id' => $id]);
		}

		return $variant;
	}


	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?ProductVariant
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

}
