<?php

declare(strict_types=1);

namespace App\Model\Orm\Voucher;

use App\Model\Orm\BigDecimalContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Price;
use App\Model\Orm\State\State;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\PostType\Core\Model\Publishable;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property-read int $id {primary}
 * @property string $name {default ''}
 * @property string $internalName {default ''}
 * @property string $type {enum self::TYPE_*} {default self::TYPE_AMOUNT}
 * @property BigDecimal|null $minPriceOrder {wrapper BigDecimalContainer}
 * @property int $reuse {default 0}
 * @property int $combination {default 0}
 * @property string|null $combinationType {enum self::TYPE_*} {default null}
 * @property int $public {default 0}
 * @property Price $discount {embeddable}
 * @property int|null $discountPercent {default null}
 *
 * @property int|null $created
 * @property int|null $edited
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $publicFrom {default 'now'}
 * @property DateTimeImmutable|null $createdTime {default 'now'}
 * @property DateTimeImmutable|null $editedTime
 *
 * RELATIONS
 * @property VoucherCode[]|OneHasMany $codes {1:m VoucherCode::$voucher, cascade=[persist, remove]}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property-read bool $isActive {virtual}
 * @property-read bool $isUsed {virtual}
 * @property-read bool $isInOrder {virtual}
 */
final class Voucher extends Entity implements Publishable
{
	use HasFormDefaultData;
	public const TYPE_AMOUNT = 'amount';
	public const TYPE_PERCENT = 'percent';
	public const TYPE_AMOUNT_COMBINATION = 'amountCombination';

	public function getterIsActive(): bool
	{
		$now = new DateTimeImmutable();
		$andCondition = $this->publicFrom->getTimestamp() <= $now->getTimestamp() && $this->publicTo->getTimestamp() >= $now->getTimestamp();
		return $this->getIsPublic() && $andCondition;
	}

	public function getterIsUsed(): bool
	{
		return $this->codes->toCollection()->findBy(['isUsed' => 1])->countStored() > 0;
	}

	public function getterIsInOrder(): bool
	{
		/** @var VoucherCode $voucherCode */
		foreach ($this->codes->toCollection() as $voucherCode) {
			if ($voucherCode->isInOrder) {
				return true;
			}
		}
		return false;
	}

	public function getCurrency(): Currency
	{
		return Currency::of($this->discount->currency);
	}

	public function getVatRate(State $state/*, Order $order*/): VatRate
	{
		return VatRate::Standard; // todo
	}

	public function getIsPublic(): bool
	{
		//
		return (bool) $this->public;// && $andCondition;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = (int) $isPublic;
	}
}
