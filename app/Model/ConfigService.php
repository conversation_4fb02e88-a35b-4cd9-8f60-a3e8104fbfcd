<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Orm\Orm;
use Nette;

final class ConfigService
{

	use Nette\SmartObject;

	public const ENV_LOCAL = 'local';
	public const ENV_STAGE = 'stage';
	public const ENV_PRODUCTION = 'production';

	public const ES_SYNONYMS_UPDATED = 'synonymsUpdated';
	public const ES_SYNONYMS_UPDATED_BY = 'synonymsUpdatedBy';
	public const ES_USED_INDEX_TS = 'esUsedIndexTimestamp';
	public const USER_GROUP_UPDATED = 'userGroupUpdated';

	/** config parametry z neonu */
	protected array $params = [];

	/** config hodnoty */
	protected array $config;

	protected Orm $orm;

	protected string $table = 'config';

	protected string $ckey = 'config';

	function __construct(array $config, Orm $orm)
	{
		$this->config = $config;
		$this->orm = $orm;
	}


	/**
	 * Vr<PERSON><PERSON><PERSON> hodnotu config parametru
	 *
	 * ->get('x', 'y', 'z');
	 *
	 * 	parameters:
	 * 		x:
	 * 			y:
	 * 				z:
	 */
	public function get(string ...$path): mixed
	{
		return Nette\Utils\Arrays::get($this->config, $path, null);
	}


	public function getParams(): mixed
	{
		return $this->config;
	}

	public function getParam(string $name, ?string $sub = null): mixed
	{
		$param = null;

		if ($this->params === []) {
			$this->params = $this->config;
		}

		if (!isset($sub) || $sub === '') {
			if (isset($this->params[$name])) {
				$param = $this->params[$name];
			}
		} else {
			if (isset($this->params[$name][$sub])) {
				$param = $this->params[$name][$sub];

			}
		}

		return $param;
	}

	public function isEnvLocal(): bool
	{
		return $this->get('env') === self::ENV_LOCAL;
	}

	public function isEnvStage(): bool
	{
		return $this->get('env') === self::ENV_STAGE;
	}

	public function isEnvProduction(): bool
	{
		return $this->get('env') === self::ENV_PRODUCTION;
	}



	public function isDev(): bool
	{
		return boolval($this->get('isDev'));
	}

}
