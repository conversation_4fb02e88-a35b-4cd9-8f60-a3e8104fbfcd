<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor\Helper\CustomFieldHelper;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\ElasticSearch\All\Convertor;

class BlogData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof BlogLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'blog',
			'langCode' => $object->mutation->langCode,

			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => $object->annotation,
		];
		$ret['kind'] = 'blog';

		$ret['repository'] = $object->getRepository()::class;
		list($imageIds, $fileIds) = CustomFieldHelper::getImagesIds($object);
		$ret['imageIds'] = $imageIds;
		$ret['fileIds'] = $fileIds;

		return $ret;
	}

}
