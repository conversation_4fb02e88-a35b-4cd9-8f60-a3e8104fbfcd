<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\Product\Convertor\CategoryData;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Convertor\TopScoreData;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Messenger\Elasticsearch\Product\Message\QuickReplaceProductMessage;
use App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Elastica\Exception\NotFoundException;

class Facade
{

	public function __construct(
		private Service $service,
		private ProductRepository $productRepository,
		private EsIndexRepository $esIndexRepository,
		private ConvertorProvider $convertorProvider,
		private ElasticBusWrapper $elasticBusWrapper,
	)
	{
	}

	public function deleteFromAllMutationNow(Product $product): void
	{
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);
			try {
				$elasticEntity = new ElasticProduct($product, []);
				$this->service->deleteDoc($esIndex, $elasticEntity);
			} catch (NotFoundException) {
				//ignore if not found
			}
		}
	}

	public function updateAllMutationsNow(Product $product, array $convertors = []): void
	{
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);

			$productLocalization = $product->getLocalization($esIndex->mutation);

			if ($convertors === []) {
				$convertors = $this->convertorProvider->getAll();
			}

			$elasticEntity = new ElasticProduct($product, $convertors);

			if ($productLocalization->public !== 0) {
				$this->service->replaceDoc($esIndex, $elasticEntity);
			} else {
				try {
					$this->service->deleteDoc($esIndex, $elasticEntity);
				} catch (NotFoundException) {
					//just trying
				}
			}
		}
	}


	public function updateAllMutations(Product $product): void
	{
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);
			$productLocalization = $product->getLocalization($esIndex->mutation);

			if ($productLocalization !== null) {
				$message = new QuickReplaceProductMessage($product->id, $esIndex, $this->convertorProvider->getAllLikeStrings());
//				$message = new ReplaceProductMessage($product->id, $esIndex, $this->convertorProvider->getAllLikeStrings());
				$this->elasticBusWrapper->send($message);
			}
		}
	}


	public function update(Product $product, Mutation $mutation, array $convertorClasses = []): void
	{
		$esIndex = $this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT)->getBy(['mutation' => $mutation]);
		if ($esIndex !== null) {
			assert($esIndex instanceof EsIndex);
			$convertorClasses = ($convertorClasses === []) ? $this->convertorProvider->getAllLikeStrings() : $convertorClasses;
			$this->elasticBusWrapper->send(
				new QuickReplaceProductMessage($product->id, $esIndex, $convertorClasses)
				//				new ReplaceProductMessage($product->id, $esIndex, $convertorClasses)
			);
		}
	}


	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false): void
	{
		$ids = $this->productRepository->findAllIds($limit);
		$idsCount = count($ids);
		$convertorStrings = $this->convertorProvider->getAllLikeStrings();

		foreach ($ids as $key => $productRow) {
			$key = (int) $key;
			$productId = $productRow->id;

			$signals = [];

			if ($key === 0) {
				$signals[] = ElasticBusWrapper::SIGNAL_FIRST;
			}

			if ($key + 1 === $idsCount) {
				$signals[] = ElasticBusWrapper::SIGNAL_LAST;
				if ($autoSwitch) {
					$signals[] = ElasticBusWrapper::AUTO_SWITCH;
				}
			}



			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings, $signals)
			);
		}
	}


	public function fillQuickUpdate(EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIds($limit);
		$convertorStrings = [
			TopScoreData::class,
			PriceData::class,
			CategoryData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}


	public function fillPriceUpdate(EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIds($limit);
		$convertorStrings = [
			PriceData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}


	public function fillCategoryUpdate(?EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIds($limit);
		$convertorStrings = [
			CategoryData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}

}
