all: up
.PHONY: all up stop install install-php install-front install-admin install-admin-new build-front build-admin build-admin-new migrate migrations-reset xdebug-on xdebug-off

ROOT_DIR := $(strip $(shell dirname "$(realpath $(firstword $(MAKEFILE_LIST)))"))

start:
	sh ./docker/db/make-dump.sh
	make up
	make install
	make build
	make clean-cache
	make populate-elastic
	docker compose exec app php bin/console contributte:cache:clean

up:
	docker compose up -d

stop:
	docker compose stop

install: install-php install-front install-admin install-admin-new

install-php: up
	docker compose run --rm -it app php composer.phar install

install-front: up
	docker compose run --rm -it front npm install --yes

install-admin: src/admin/package.json src/admin/package-lock.json
	docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:14 npm install
	docker compose run --rm -it admin npm rebuild node-sass

install-admin-new: up
	docker compose run --rm -it admin npm install --yes
	docker compose run --rm -it admin npm rebuild node-sass

build: build-front build-admin build-admin-new

build-front: install-front
	docker compose run --rm -it front npm run build

build-admin: install-admin
	#docker run --rm -it -v $(ROOT_DIR):/app -w /app/src/admin node:14 npm run build

build-admin-new: install-admin-new
	docker compose run --rm -it admin npm run

migrate: install-php
	docker compose run --rm -it app php bin/console migrations:continue

migrations-reset: install-php
	docker compose run --rm -it app php bin/console migrations:reset

populate-elastic: install-php
	docker compose run --rm -it app php bin/console elastic:index:create -psc

xdebug-on:
	SUPERADMIN_XDEBUG=on docker compose up --force-recreate --no-deps -d app

xdebug-off:
	SUPERADMIN_XDEBUG=off docker compose up --force-recreate --no-deps -d app

rebuild:
	docker compose up --build

clean-cache:
	docker compose exec app php bin/console contributte:cache:clean

orbstack:
	sh ./docker/orbstack.sh
	make start

docker-start:
	sh ./docker/docker.sh
	make start

diff:
	sh ./docker/db/make-dump.sh
	sh ./docker/db/compare-db.sh

drop-db:
	sh ./docker/db/drop-database.sh

phpstan:
	docker compose run --rm -it app php vendor/bin/phpstan analyse -c .phpstan.neon --memory-limit=2G
